"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark";
exports.ids = ["vendor-chunks/micromark"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark/dev/lib/constructs.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/constructs.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attentionMarkers: () => (/* binding */ attentionMarkers),\n/* harmony export */   contentInitial: () => (/* binding */ contentInitial),\n/* harmony export */   disable: () => (/* binding */ disable),\n/* harmony export */   document: () => (/* binding */ document),\n/* harmony export */   flow: () => (/* binding */ flow),\n/* harmony export */   flowInitial: () => (/* binding */ flowInitial),\n/* harmony export */   insideSpan: () => (/* binding */ insideSpan),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/list.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/block-quote.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/definition.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-indented.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-flow.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-reference.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/line-ending.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/attention.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/autolink.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-text.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-text.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/**\n * @import {Extension} from 'micromark-util-types'\n */\n\n\n\n\n\n/** @satisfies {Extension['document']} */\nconst document = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit1]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit2]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit3]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit4]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit5]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit6]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit7]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit8]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit9]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.greaterThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.blockQuote\n}\n\n/** @satisfies {Extension['contentInitial']} */\nconst contentInitial = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__.definition\n}\n\n/** @satisfies {Extension['flowInitial']} */\nconst flowInitial = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented\n}\n\n/** @satisfies {Extension['flow']} */\nconst flow = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.numberSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__.headingAtx,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline, micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak],\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__.htmlFlow,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.equalsTo]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced\n}\n\n/** @satisfies {Extension['string']} */\nconst string = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n}\n\n/** @satisfies {Extension['text']} */\nconst text = {\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__.labelStartImage,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__.autolink, micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__.htmlText],\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__.labelStartLink,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__.hardBreakEscape, micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape],\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__.labelEnd,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n  [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__.codeText\n}\n\n/** @satisfies {Extension['insideSpan']} */\nconst insideSpan = {null: [micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention, _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__.resolver]}\n\n/** @satisfies {Extension['attentionMarkers']} */\nconst attentionMarkers = {null: [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]}\n\n/** @satisfies {Extension['disable']} */\nconst disable = {null: []}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvY29uc3RydWN0cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQSxZQUFZLFdBQVc7QUFDdkI7O0FBdUJrQztBQUNTO0FBQ2lCOztBQUU1RCxnQkFBZ0IsdUJBQXVCO0FBQ2hDO0FBQ1AsR0FBRyx3REFBSyxZQUFZLDJEQUFJO0FBQ3hCLEdBQUcsd0RBQUssWUFBWSwyREFBSTtBQUN4QixHQUFHLHdEQUFLLFFBQVEsMkRBQUk7QUFDcEIsR0FBRyx3REFBSyxVQUFVLDJEQUFJO0FBQ3RCLEdBQUcsd0RBQUssVUFBVSwyREFBSTtBQUN0QixHQUFHLHdEQUFLLFVBQVUsMkRBQUk7QUFDdEIsR0FBRyx3REFBSyxVQUFVLDJEQUFJO0FBQ3RCLEdBQUcsd0RBQUssVUFBVSwyREFBSTtBQUN0QixHQUFHLHdEQUFLLFVBQVUsMkRBQUk7QUFDdEIsR0FBRyx3REFBSyxVQUFVLDJEQUFJO0FBQ3RCLEdBQUcsd0RBQUssVUFBVSwyREFBSTtBQUN0QixHQUFHLHdEQUFLLFVBQVUsMkRBQUk7QUFDdEIsR0FBRyx3REFBSyxVQUFVLDJEQUFJO0FBQ3RCLEdBQUcsd0RBQUssZUFBZSxpRUFBVTtBQUNqQzs7QUFFQSxnQkFBZ0IsNkJBQTZCO0FBQ3RDO0FBQ1AsR0FBRyx3REFBSyxxQkFBcUIsaUVBQVU7QUFDdkM7O0FBRUEsZ0JBQWdCLDBCQUEwQjtBQUNuQztBQUNQLEdBQUcsd0RBQUssaUJBQWlCLG1FQUFZO0FBQ3JDLEdBQUcsd0RBQUssZ0JBQWdCLG1FQUFZO0FBQ3BDLEdBQUcsd0RBQUssU0FBUyxtRUFBWTtBQUM3Qjs7QUFFQSxnQkFBZ0IsbUJBQW1CO0FBQzVCO0FBQ1AsR0FBRyx3REFBSyxjQUFjLGlFQUFVO0FBQ2hDLEdBQUcsd0RBQUssWUFBWSxvRUFBYTtBQUNqQyxHQUFHLHdEQUFLLFNBQVMsc0VBQWUsRUFBRSxvRUFBYTtBQUMvQyxHQUFHLHdEQUFLLFlBQVksK0RBQVE7QUFDNUIsR0FBRyx3REFBSyxZQUFZLHNFQUFlO0FBQ25DLEdBQUcsd0RBQUssY0FBYyxvRUFBYTtBQUNuQyxHQUFHLHdEQUFLLGVBQWUsaUVBQVU7QUFDakMsR0FBRyx3REFBSyxTQUFTLGlFQUFVO0FBQzNCOztBQUVBLGdCQUFnQixxQkFBcUI7QUFDOUI7QUFDUCxHQUFHLHdEQUFLLGFBQWEsMEVBQWtCO0FBQ3ZDLEdBQUcsd0RBQUssYUFBYSx1RUFBZTtBQUNwQzs7QUFFQSxnQkFBZ0IsbUJBQW1CO0FBQzVCO0FBQ1AsR0FBRyx3REFBSyxrQkFBa0Isa0VBQVU7QUFDcEMsR0FBRyx3REFBSyxZQUFZLGtFQUFVO0FBQzlCLEdBQUcsd0RBQUssMEJBQTBCLGtFQUFVO0FBQzVDLEdBQUcsd0RBQUssbUJBQW1CLHVFQUFlO0FBQzFDLEdBQUcsd0RBQUssYUFBYSwwRUFBa0I7QUFDdkMsR0FBRyx3REFBSyxZQUFZLGlFQUFTO0FBQzdCLEdBQUcsd0RBQUssYUFBYSxnRUFBUSxFQUFFLGdFQUFRO0FBQ3ZDLEdBQUcsd0RBQUsscUJBQXFCLHNFQUFjO0FBQzNDLEdBQUcsd0RBQUssY0FBYyx1RUFBZSxFQUFFLHVFQUFlO0FBQ3RELEdBQUcsd0RBQUssc0JBQXNCLGdFQUFRO0FBQ3RDLEdBQUcsd0RBQUssY0FBYyxpRUFBUztBQUMvQixHQUFHLHdEQUFLLGVBQWUsZ0VBQVE7QUFDL0I7O0FBRUEsZ0JBQWdCLHlCQUF5QjtBQUNsQyxvQkFBb0IsT0FBTyxpRUFBUyxFQUFFLDBEQUFXOztBQUV4RCxnQkFBZ0IsK0JBQStCO0FBQ3hDLDBCQUEwQixPQUFPLHdEQUFLLFdBQVcsd0RBQUs7O0FBRTdELGdCQUFnQixzQkFBc0I7QUFDL0IsaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcbm9kZV9tb2R1bGVzXFxtaWNyb21hcmtcXGRldlxcbGliXFxjb25zdHJ1Y3RzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7RXh0ZW5zaW9ufSBmcm9tICdtaWNyb21hcmstdXRpbC10eXBlcydcbiAqL1xuXG5pbXBvcnQge1xuICBhdHRlbnRpb24sXG4gIGF1dG9saW5rLFxuICBibG9ja1F1b3RlLFxuICBjaGFyYWN0ZXJFc2NhcGUsXG4gIGNoYXJhY3RlclJlZmVyZW5jZSxcbiAgY29kZUZlbmNlZCxcbiAgY29kZUluZGVudGVkLFxuICBjb2RlVGV4dCxcbiAgZGVmaW5pdGlvbixcbiAgaGFyZEJyZWFrRXNjYXBlLFxuICBoZWFkaW5nQXR4LFxuICBodG1sRmxvdyxcbiAgaHRtbFRleHQsXG4gIGxhYmVsRW5kLFxuICBsYWJlbFN0YXJ0SW1hZ2UsXG4gIGxhYmVsU3RhcnRMaW5rLFxuICBsaW5lRW5kaW5nLFxuICBsaXN0LFxuICBzZXRleHRVbmRlcmxpbmUsXG4gIHRoZW1hdGljQnJlYWtcbn0gZnJvbSAnbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyaydcbmltcG9ydCB7Y29kZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbCdcbmltcG9ydCB7cmVzb2x2ZXIgYXMgcmVzb2x2ZVRleHR9IGZyb20gJy4vaW5pdGlhbGl6ZS90ZXh0LmpzJ1xuXG4vKiogQHNhdGlzZmllcyB7RXh0ZW5zaW9uWydkb2N1bWVudCddfSAqL1xuZXhwb3J0IGNvbnN0IGRvY3VtZW50ID0ge1xuICBbY29kZXMuYXN0ZXJpc2tdOiBsaXN0LFxuICBbY29kZXMucGx1c1NpZ25dOiBsaXN0LFxuICBbY29kZXMuZGFzaF06IGxpc3QsXG4gIFtjb2Rlcy5kaWdpdDBdOiBsaXN0LFxuICBbY29kZXMuZGlnaXQxXTogbGlzdCxcbiAgW2NvZGVzLmRpZ2l0Ml06IGxpc3QsXG4gIFtjb2Rlcy5kaWdpdDNdOiBsaXN0LFxuICBbY29kZXMuZGlnaXQ0XTogbGlzdCxcbiAgW2NvZGVzLmRpZ2l0NV06IGxpc3QsXG4gIFtjb2Rlcy5kaWdpdDZdOiBsaXN0LFxuICBbY29kZXMuZGlnaXQ3XTogbGlzdCxcbiAgW2NvZGVzLmRpZ2l0OF06IGxpc3QsXG4gIFtjb2Rlcy5kaWdpdDldOiBsaXN0LFxuICBbY29kZXMuZ3JlYXRlclRoYW5dOiBibG9ja1F1b3RlXG59XG5cbi8qKiBAc2F0aXNmaWVzIHtFeHRlbnNpb25bJ2NvbnRlbnRJbml0aWFsJ119ICovXG5leHBvcnQgY29uc3QgY29udGVudEluaXRpYWwgPSB7XG4gIFtjb2Rlcy5sZWZ0U3F1YXJlQnJhY2tldF06IGRlZmluaXRpb25cbn1cblxuLyoqIEBzYXRpc2ZpZXMge0V4dGVuc2lvblsnZmxvd0luaXRpYWwnXX0gKi9cbmV4cG9ydCBjb25zdCBmbG93SW5pdGlhbCA9IHtcbiAgW2NvZGVzLmhvcml6b250YWxUYWJdOiBjb2RlSW5kZW50ZWQsXG4gIFtjb2Rlcy52aXJ0dWFsU3BhY2VdOiBjb2RlSW5kZW50ZWQsXG4gIFtjb2Rlcy5zcGFjZV06IGNvZGVJbmRlbnRlZFxufVxuXG4vKiogQHNhdGlzZmllcyB7RXh0ZW5zaW9uWydmbG93J119ICovXG5leHBvcnQgY29uc3QgZmxvdyA9IHtcbiAgW2NvZGVzLm51bWJlclNpZ25dOiBoZWFkaW5nQXR4LFxuICBbY29kZXMuYXN0ZXJpc2tdOiB0aGVtYXRpY0JyZWFrLFxuICBbY29kZXMuZGFzaF06IFtzZXRleHRVbmRlcmxpbmUsIHRoZW1hdGljQnJlYWtdLFxuICBbY29kZXMubGVzc1RoYW5dOiBodG1sRmxvdyxcbiAgW2NvZGVzLmVxdWFsc1RvXTogc2V0ZXh0VW5kZXJsaW5lLFxuICBbY29kZXMudW5kZXJzY29yZV06IHRoZW1hdGljQnJlYWssXG4gIFtjb2Rlcy5ncmF2ZUFjY2VudF06IGNvZGVGZW5jZWQsXG4gIFtjb2Rlcy50aWxkZV06IGNvZGVGZW5jZWRcbn1cblxuLyoqIEBzYXRpc2ZpZXMge0V4dGVuc2lvblsnc3RyaW5nJ119ICovXG5leHBvcnQgY29uc3Qgc3RyaW5nID0ge1xuICBbY29kZXMuYW1wZXJzYW5kXTogY2hhcmFjdGVyUmVmZXJlbmNlLFxuICBbY29kZXMuYmFja3NsYXNoXTogY2hhcmFjdGVyRXNjYXBlXG59XG5cbi8qKiBAc2F0aXNmaWVzIHtFeHRlbnNpb25bJ3RleHQnXX0gKi9cbmV4cG9ydCBjb25zdCB0ZXh0ID0ge1xuICBbY29kZXMuY2FycmlhZ2VSZXR1cm5dOiBsaW5lRW5kaW5nLFxuICBbY29kZXMubGluZUZlZWRdOiBsaW5lRW5kaW5nLFxuICBbY29kZXMuY2FycmlhZ2VSZXR1cm5MaW5lRmVlZF06IGxpbmVFbmRpbmcsXG4gIFtjb2Rlcy5leGNsYW1hdGlvbk1hcmtdOiBsYWJlbFN0YXJ0SW1hZ2UsXG4gIFtjb2Rlcy5hbXBlcnNhbmRdOiBjaGFyYWN0ZXJSZWZlcmVuY2UsXG4gIFtjb2Rlcy5hc3Rlcmlza106IGF0dGVudGlvbixcbiAgW2NvZGVzLmxlc3NUaGFuXTogW2F1dG9saW5rLCBodG1sVGV4dF0sXG4gIFtjb2Rlcy5sZWZ0U3F1YXJlQnJhY2tldF06IGxhYmVsU3RhcnRMaW5rLFxuICBbY29kZXMuYmFja3NsYXNoXTogW2hhcmRCcmVha0VzY2FwZSwgY2hhcmFjdGVyRXNjYXBlXSxcbiAgW2NvZGVzLnJpZ2h0U3F1YXJlQnJhY2tldF06IGxhYmVsRW5kLFxuICBbY29kZXMudW5kZXJzY29yZV06IGF0dGVudGlvbixcbiAgW2NvZGVzLmdyYXZlQWNjZW50XTogY29kZVRleHRcbn1cblxuLyoqIEBzYXRpc2ZpZXMge0V4dGVuc2lvblsnaW5zaWRlU3BhbiddfSAqL1xuZXhwb3J0IGNvbnN0IGluc2lkZVNwYW4gPSB7bnVsbDogW2F0dGVudGlvbiwgcmVzb2x2ZVRleHRdfVxuXG4vKiogQHNhdGlzZmllcyB7RXh0ZW5zaW9uWydhdHRlbnRpb25NYXJrZXJzJ119ICovXG5leHBvcnQgY29uc3QgYXR0ZW50aW9uTWFya2VycyA9IHtudWxsOiBbY29kZXMuYXN0ZXJpc2ssIGNvZGVzLnVuZGVyc2NvcmVdfVxuXG4vKiogQHNhdGlzZmllcyB7RXh0ZW5zaW9uWydkaXNhYmxlJ119ICovXG5leHBvcnQgY29uc3QgZGlzYWJsZSA9IHtudWxsOiBbXX1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/constructs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js":
/*!************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/create-tokenizer.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTokenizer: () => (/* binding */ createTokenizer)\n/* harmony export */ });\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/values.js\");\n/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */\n\n\n\n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_0__('micromark')\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */\nfunction createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = {\n    _bufferIndex: -1,\n    _index: 0,\n    line: (from && from.line) || 1,\n    column: (from && from.column) || 1,\n    offset: (from && from.offset) || 0\n  }\n  /** @type {Record<string, number>} */\n  const columnStart = {}\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = []\n  /** @type {Array<Chunk>} */\n  let chunks = []\n  /** @type {Array<Token>} */\n  let stack = []\n  /** @type {boolean | undefined} */\n  let consumed = true\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    consume,\n    enter,\n    exit,\n    interrupt: constructFactory(onsuccessfulcheck, {interrupt: true})\n  }\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    code: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n    containerState: {},\n    defineSkip,\n    events: [],\n    now,\n    parser,\n    previous: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n    sliceSerialize,\n    sliceStream,\n    write\n  }\n\n  /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */\n  let state = initialize.tokenize.call(context, effects)\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode\n\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize)\n  }\n\n  return context\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(chunks, slice)\n\n    main()\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return []\n    }\n\n    addResult(initialize, 0)\n\n    // Otherwise, resolve, and exit.\n    context.events = (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(resolveAllConstructs, context.events, context)\n\n    return context.events\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs)\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token)\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {_bufferIndex, _index, line, column, offset} = point\n    return {_bufferIndex, _index, line, column, offset}\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column\n    accountForPotentialSkip()\n    debug('position: define skip: `%j`', point)\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex\n\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index]\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index\n\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0\n        }\n\n        while (\n          point._index === chunkIndex &&\n          point._bufferIndex < chunk.length\n        ) {\n          go(chunk.charCodeAt(point._bufferIndex))\n        }\n      } else {\n        go(chunk)\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function go(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === true, 'expected character to be consumed')\n    consumed = undefined\n    debug('main: passing `%s` to %s', code, state && state.name)\n    expectedCode = code\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof state === 'function', 'expected state')\n    state = state(code)\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, 'expected given code to equal expected code')\n\n    debug('consume: `%s`', code)\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      consumed === undefined,\n      'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      code === null\n        ? context.events.length === 0 ||\n            context.events[context.events.length - 1][0] === 'exit'\n        : context.events[context.events.length - 1][0] === 'enter',\n      'expected last token to be open'\n    )\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n      point.line++\n      point.column = 1\n      point.offset += code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed ? 2 : 1\n      accountForPotentialSkip()\n      debug('position: after eol: `%j`', point)\n    } else if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace) {\n      point.column++\n      point.offset++\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++\n    } else {\n      point._bufferIndex++\n\n      // At end of string chunk.\n      if (\n        point._bufferIndex ===\n        // Points w/ non-negative `_bufferIndex` reference\n        // strings.\n        /** @type {string} */ (chunks[point._index]).length\n      ) {\n        point._bufferIndex = -1\n        point._index++\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code\n\n    // Mark as consumed.\n    consumed = true\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {}\n    token.type = type\n    token.start = now()\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === 'string', 'expected string type')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, 'expected non-empty string')\n    debug('enter: `%s`', type)\n\n    context.events.push(['enter', token, context])\n\n    stack.push(token)\n\n    return token\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === 'string', 'expected string type')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, 'expected non-empty string')\n\n    const token = stack.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(token, 'cannot close w/o open tokens')\n    token.end = now()\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type === token.type, 'expected exit token to match current token')\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      !(\n        token.start._index === token.end._index &&\n        token.start._bufferIndex === token.end._bufferIndex\n      ),\n      'expected non-empty token (`' + type + '`)'\n    )\n\n    debug('exit: `%s`', token.type)\n    context.events.push(['exit', token, context])\n\n    return token\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from)\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore()\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */\n  function constructFactory(onreturn, fields) {\n    return hook\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {ReadonlyArray<Construct>} */\n      let listOfConstructs\n      /** @type {number} */\n      let constructIndex\n      /** @type {Construct} */\n      let currentConstruct\n      /** @type {Info} */\n      let info\n\n      return Array.isArray(constructs)\n        ? /* c8 ignore next 1 */\n          handleListOfConstructs(constructs)\n        : 'tokenize' in constructs\n          ? // Looks like a construct.\n            handleListOfConstructs([/** @type {Construct} */ (constructs)])\n          : handleMapOfConstructs(constructs)\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleMapOfConstructs(map) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          const left = code !== null && map[code]\n          const all = code !== null && map.null\n          const list = [\n            // To do: add more extension tests.\n            /* c8 ignore next 2 */\n            ...(Array.isArray(left) ? left : left ? [left] : []),\n            ...(Array.isArray(all) ? all : all ? [all] : [])\n          ]\n\n          return handleListOfConstructs(list)(code)\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list\n        constructIndex = 0\n\n        if (list.length === 0) {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(bogusState, 'expected `bogusState` to be given')\n          return bogusState\n        }\n\n        return handleConstruct(list[constructIndex])\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */\n      function handleConstruct(construct) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store()\n          currentConstruct = construct\n\n          if (!construct.partial) {\n            context.currentConstruct = construct\n          }\n\n          // Always populated by defaults.\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n            context.parser.constructs.disable.null,\n            'expected `disable.null` to be populated'\n          )\n\n          if (\n            construct.name &&\n            context.parser.constructs.disable.null.includes(construct.name)\n          ) {\n            return nok(code)\n          }\n\n          return construct.tokenize.call(\n            // If we do have fields, create an object w/ `context` as its\n            // prototype.\n            // This allows a “live binding”, which is needed for `interrupt`.\n            fields ? Object.assign(Object.create(context), fields) : context,\n            effects,\n            ok,\n            nok\n          )(code)\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, 'expected code')\n        consumed = true\n        onreturn(currentConstruct, info)\n        return returnState\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, 'expected code')\n        consumed = true\n        info.restore()\n\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex])\n        }\n\n        return bogusState\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct)\n    }\n\n    if (construct.resolve) {\n      (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(\n        context.events,\n        from,\n        context.events.length - from,\n        construct.resolve(context.events.slice(from), context)\n      )\n    }\n\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context)\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      construct.partial ||\n        context.events.length === 0 ||\n        context.events[context.events.length - 1][0] === 'exit',\n      'expected last token to end'\n    )\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */\n  function store() {\n    const startPoint = now()\n    const startPrevious = context.previous\n    const startCurrentConstruct = context.currentConstruct\n    const startEventsIndex = context.events.length\n    const startStack = Array.from(stack)\n\n    return {from: startEventsIndex, restore}\n\n    /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */\n    function restore() {\n      point = startPoint\n      context.previous = startPrevious\n      context.currentConstruct = startCurrentConstruct\n      context.events.length = startEventsIndex\n      stack = startStack\n      accountForPotentialSkip()\n      debug('position: restore: `%j`', point)\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line]\n      point.offset += columnStart[point.line] - 1\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index\n  const startBufferIndex = token.start._bufferIndex\n  const endIndex = token.end._index\n  const endBufferIndex = token.end._bufferIndex\n  /** @type {Array<Chunk>} */\n  let view\n\n  if (startIndex === endIndex) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(endBufferIndex > -1, 'expected non-negative end buffer index')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex > -1, 'expected non-negative start buffer index')\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)]\n  } else {\n    view = chunks.slice(startIndex, endIndex)\n\n    if (startBufferIndex > -1) {\n      const head = view[0]\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex)\n        /* c8 ignore next 4 -- used to be used, no longer */\n      } else {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`')\n        view.shift()\n      }\n    }\n\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex))\n    }\n  }\n\n  return view\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {boolean | undefined} */\n  let atTab\n\n  while (++index < chunks.length) {\n    const chunk = chunks[index]\n    /** @type {string} */\n    let value\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else\n      switch (chunk) {\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturn: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lineFeed: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed: {\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab: {\n          value = expandTabs ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.ht\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace: {\n          if (!expandTabs && atTab) continue\n          value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space\n\n          break\n        }\n\n        default: {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof chunk === 'number', 'expected number')\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk)\n        }\n      }\n\n    atTab = chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab\n    result.push(value)\n  }\n\n  return result.join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/content.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/content.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {InitialConstruct} */\nconst content = {tokenize: initializeContent}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(\n    this.parser.constructs.contentInitial,\n    afterContentStartConstruct,\n    paragraphInitial\n  )\n  /** @type {Token} */\n  let previous\n\n  return contentStart\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code),\n      'expected eol or eof'\n    )\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, contentStart, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix)\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code),\n      'expected anything other than a line ending or EOF'\n    )\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph)\n    return lineStart(code)\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText, {\n      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText,\n      previous\n    })\n\n    if (previous) {\n      previous.next = token\n    }\n\n    previous = token\n\n    return data(code)\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph)\n      effects.consume(code)\n      return\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      effects.consume(code)\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText)\n      return lineStart\n    }\n\n    // Data.\n    effects.consume(code)\n    return data\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/document.js":
/*!***************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/document.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   document: () => (/* binding */ document)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   ContainerState,\n *   InitialConstruct,\n *   Initializer,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef {[Construct, ContainerState]} StackItem\n *   Construct and its state.\n */\n\n\n\n\n\n\n\n/** @type {InitialConstruct} */\nconst document = {tokenize: initializeDocument}\n\n/** @type {Construct} */\nconst containerConstruct = {tokenize: tokenizeContainer}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeDocument(effects) {\n  const self = this\n  /** @type {Array<StackItem>} */\n  const stack = []\n  let continued = 0\n  /** @type {TokenizeContext | undefined} */\n  let childFlow\n  /** @type {Token | undefined} */\n  let childToken\n  /** @type {number} */\n  let lineStartOffset\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    // First we iterate through the open blocks, starting with the root\n    // document, and descending through last children down to the last open\n    // block.\n    // Each block imposes a condition that the line must satisfy if the block is\n    // to remain open.\n    // For example, a block quote requires a `>` character.\n    // A paragraph requires a non-blank line.\n    // In this phase we may match all or just some of the open blocks.\n    // But we cannot close unmatched blocks yet, because we may have a lazy\n    // continuation line.\n    if (continued < stack.length) {\n      const item = stack[continued]\n      self.containerState = item[1]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n        item[0].continuation,\n        'expected `continuation` to be defined on container construct'\n      )\n      return effects.attempt(\n        item[0].continuation,\n        documentContinue,\n        checkNewContainers\n      )(code)\n    }\n\n    // Done.\n    return checkNewContainers(code)\n  }\n\n  /** @type {State} */\n  function documentContinue(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.containerState,\n      'expected `containerState` to be defined after continuation'\n    )\n\n    continued++\n\n    // Note: this field is called `_closeFlow` but it also closes containers.\n    // Perhaps a good idea to rename it but it’s already used in the wild by\n    // extensions.\n    if (self.containerState._closeFlow) {\n      self.containerState._closeFlow = undefined\n\n      if (childFlow) {\n        closeFlow()\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when dealing with lazy lines in `writeToChild`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the flow chunk.\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow\n        ) {\n          point = self.events[indexBeforeFlow][1].end\n          break\n        }\n      }\n\n      (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      let index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n\n      return checkNewContainers(code)\n    }\n\n    return start(code)\n  }\n\n  /** @type {State} */\n  function checkNewContainers(code) {\n    // Next, after consuming the continuation markers for existing blocks, we\n    // look for new block starts (e.g. `>` for a block quote).\n    // If we encounter a new block start, we close any blocks unmatched in\n    // step 1 before creating the new block as a child of the last matched\n    // block.\n    if (continued === stack.length) {\n      // No need to `check` whether there’s a container, of `exitContainers`\n      // would be moot.\n      // We can instead immediately `attempt` to parse one.\n      if (!childFlow) {\n        return documentContinued(code)\n      }\n\n      // If we have concrete content, such as block HTML or fenced code,\n      // we can’t have containers “pierce” into them, so we can immediately\n      // start.\n      if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n        return flowStart(code)\n      }\n\n      // If we do have flow, it could still be a blank line,\n      // but we’d be interrupting it w/ a new container if there’s a current\n      // construct.\n      // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n      // needed in micromark-extension-gfm-table@1.0.6).\n      self.interrupt = Boolean(\n        childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack\n      )\n    }\n\n    // Check if there is a new container.\n    self.containerState = {}\n    return effects.check(\n      containerConstruct,\n      thereIsANewContainer,\n      thereIsNoNewContainer\n    )(code)\n  }\n\n  /** @type {State} */\n  function thereIsANewContainer(code) {\n    if (childFlow) closeFlow()\n    exitContainers(continued)\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function thereIsNoNewContainer(code) {\n    self.parser.lazy[self.now().line] = continued !== stack.length\n    lineStartOffset = self.now().offset\n    return flowStart(code)\n  }\n\n  /** @type {State} */\n  function documentContinued(code) {\n    // Try new containers.\n    self.containerState = {}\n    return effects.attempt(\n      containerConstruct,\n      containerContinue,\n      flowStart\n    )(code)\n  }\n\n  /** @type {State} */\n  function containerContinue(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.currentConstruct,\n      'expected `currentConstruct` to be defined on tokenizer'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.containerState,\n      'expected `containerState` to be defined on tokenizer'\n    )\n    continued++\n    stack.push([self.currentConstruct, self.containerState])\n    // Try another.\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function flowStart(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n      if (childFlow) closeFlow()\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    childFlow = childFlow || self.parser.flow(self.now())\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow, {\n      _tokenizer: childFlow,\n      contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.contentTypeFlow,\n      previous: childToken\n    })\n\n    return flowContinue(code)\n  }\n\n  /** @type {State} */\n  function flowContinue(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n      writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow), true)\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n      effects.consume(code)\n      writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow))\n      // Get ready for the next line.\n      continued = 0\n      self.interrupt = undefined\n      return start\n    }\n\n    effects.consume(code)\n    return flowContinue\n  }\n\n  /**\n   * @param {Token} token\n   *   Token.\n   * @param {boolean | undefined} [endOfFile]\n   *   Whether the token is at the end of the file (default: `false`).\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function writeToChild(token, endOfFile) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, 'expected `childFlow` to be defined when continuing')\n    const stream = self.sliceStream(token)\n    if (endOfFile) stream.push(null)\n    token.previous = childToken\n    if (childToken) childToken.next = token\n    childToken = token\n    childFlow.defineSkip(token.start)\n    childFlow.write(stream)\n\n    // Alright, so we just added a lazy line:\n    //\n    // ```markdown\n    // > a\n    // b.\n    //\n    // Or:\n    //\n    // > ~~~c\n    // d\n    //\n    // Or:\n    //\n    // > | e |\n    // f\n    // ```\n    //\n    // The construct in the second example (fenced code) does not accept lazy\n    // lines, so it marked itself as done at the end of its first line, and\n    // then the content construct parses `d`.\n    // Most constructs in markdown match on the first line: if the first line\n    // forms a construct, a non-lazy line can’t “unmake” it.\n    //\n    // The construct in the third example is potentially a GFM table, and\n    // those are *weird*.\n    // It *could* be a table, from the first line, if the following line\n    // matches a condition.\n    // In this case, that second line is lazy, which “unmakes” the first line\n    // and turns the whole into one content block.\n    //\n    // We’ve now parsed the non-lazy and the lazy line, and can figure out\n    // whether the lazy line started a new flow block.\n    // If it did, we exit the current containers between the two flow blocks.\n    if (self.parser.lazy[token.start.line]) {\n      let index = childFlow.events.length\n\n      while (index--) {\n        if (\n          // The token starts before the line ending…\n          childFlow.events[index][1].start.offset < lineStartOffset &&\n          // …and either is not ended yet…\n          (!childFlow.events[index][1].end ||\n            // …or ends after it.\n            childFlow.events[index][1].end.offset > lineStartOffset)\n        ) {\n          // Exit: there’s still something open, which means it’s a lazy line\n          // part of something.\n          return\n        }\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when closing flow in `documentContinue`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {boolean | undefined} */\n      let seen\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the previous chunk (the one before the lazy line).\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow\n        ) {\n          if (seen) {\n            point = self.events[indexBeforeFlow][1].end\n            break\n          }\n\n          seen = true\n        }\n      }\n\n      (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n    }\n  }\n\n  /**\n   * @param {number} size\n   *   Size.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function exitContainers(size) {\n    let index = stack.length\n\n    // Exit open containers.\n    while (index-- > size) {\n      const entry = stack[index]\n      self.containerState = entry[1]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n        entry[0].exit,\n        'expected `exit` to be defined on container construct'\n      )\n      entry[0].exit.call(self, effects)\n    }\n\n    stack.length = size\n  }\n\n  function closeFlow() {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.containerState,\n      'expected `containerState` to be defined when closing flow'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, 'expected `childFlow` to be defined when closing it')\n    childFlow.write([micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof])\n    childToken = undefined\n    childFlow = undefined\n    self.containerState._closeFlow = undefined\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n *   Tokenizer.\n */\nfunction tokenizeContainer(effects, ok, nok) {\n  // Always populated by defaults.\n  (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(\n    this.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(\n    effects,\n    effects.attempt(this.parser.constructs.document, ok, nok),\n    micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix,\n    this.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvaW5pdGlhbGl6ZS9kb2N1bWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQztBQUNBOztBQUVtQztBQUNpQjtBQUNPO0FBQ2Q7QUFDZ0I7O0FBRTdELFdBQVcsa0JBQWtCO0FBQ3RCLGtCQUFrQjs7QUFFekIsV0FBVyxXQUFXO0FBQ3RCLDRCQUE0Qjs7QUFFNUI7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGtCQUFrQjtBQUMvQjtBQUNBO0FBQ0EsYUFBYSw2QkFBNkI7QUFDMUM7QUFDQSxhQUFhLG1CQUFtQjtBQUNoQztBQUNBLGFBQWEsUUFBUTtBQUNyQjs7QUFFQTs7QUFFQSxhQUFhLE9BQU87QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sMkNBQU07QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGFBQWEsT0FBTztBQUNwQjtBQUNBLElBQUksMENBQU07QUFDVjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsbUJBQW1CO0FBQ3BDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1ELHdEQUFLO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsTUFBTSwwQ0FBTTs7QUFFWjs7QUFFQTtBQUNBOztBQUVBO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0E7O0FBRUE7QUFDQSxNQUFNLDhEQUFNO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxhQUFhLE9BQU87QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGFBQWEsT0FBTztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGFBQWEsT0FBTztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGFBQWEsT0FBTztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsYUFBYSxPQUFPO0FBQ3BCO0FBQ0EsSUFBSSwwQ0FBTTtBQUNWO0FBQ0E7QUFDQTtBQUNBLElBQUksMkNBQU07QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGFBQWEsT0FBTztBQUNwQjtBQUNBLGlCQUFpQix3REFBSztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0JBQWtCLHdEQUFLO0FBQ3ZCO0FBQ0EsbUJBQW1CLDREQUFTO0FBQzVCO0FBQ0EsS0FBSzs7QUFFTDtBQUNBOztBQUVBLGFBQWEsT0FBTztBQUNwQjtBQUNBLGlCQUFpQix3REFBSztBQUN0QixnQ0FBZ0Msd0RBQUs7QUFDckM7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsUUFBUSw0RUFBa0I7QUFDMUI7QUFDQSxnQ0FBZ0Msd0RBQUs7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLE9BQU87QUFDcEI7QUFDQSxhQUFhLHFCQUFxQjtBQUNsQztBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxJQUFJLDBDQUFNO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHFCQUFxQjtBQUN0QztBQUNBLGlCQUFpQixtQkFBbUI7QUFDcEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsd0RBQUs7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsTUFBTSwwQ0FBTTs7QUFFWjs7QUFFQTtBQUNBOztBQUVBO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0E7O0FBRUE7QUFDQSxNQUFNLDhEQUFNO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sMkNBQU07QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxJQUFJLDBDQUFNO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyQ0FBTTtBQUNWLHFCQUFxQix3REFBSztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVTtBQUNWO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSwwQ0FBTTtBQUNSO0FBQ0E7QUFDQTtBQUNBLFNBQVMscUVBQVk7QUFDckI7QUFDQTtBQUNBLElBQUksd0RBQUs7QUFDVDtBQUNBO0FBQ0EsUUFBUSw0REFBUztBQUNqQjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcbm9kZV9tb2R1bGVzXFxtaWNyb21hcmtcXGRldlxcbGliXFxpbml0aWFsaXplXFxkb2N1bWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1xuICogICBDb25zdHJ1Y3QsXG4gKiAgIENvbnRhaW5lclN0YXRlLFxuICogICBJbml0aWFsQ29uc3RydWN0LFxuICogICBJbml0aWFsaXplcixcbiAqICAgUG9pbnQsXG4gKiAgIFN0YXRlLFxuICogICBUb2tlbml6ZUNvbnRleHQsXG4gKiAgIFRva2VuaXplcixcbiAqICAgVG9rZW5cbiAqIH0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuLyoqXG4gKiBAdHlwZWRlZiB7W0NvbnN0cnVjdCwgQ29udGFpbmVyU3RhdGVdfSBTdGFja0l0ZW1cbiAqICAgQ29uc3RydWN0IGFuZCBpdHMgc3RhdGUuXG4gKi9cblxuaW1wb3J0IHtvayBhcyBhc3NlcnR9IGZyb20gJ2RldmxvcCdcbmltcG9ydCB7ZmFjdG9yeVNwYWNlfSBmcm9tICdtaWNyb21hcmstZmFjdG9yeS1zcGFjZSdcbmltcG9ydCB7bWFya2Rvd25MaW5lRW5kaW5nfSBmcm9tICdtaWNyb21hcmstdXRpbC1jaGFyYWN0ZXInXG5pbXBvcnQge3NwbGljZX0gZnJvbSAnbWljcm9tYXJrLXV0aWwtY2h1bmtlZCdcbmltcG9ydCB7Y29kZXMsIGNvbnN0YW50cywgdHlwZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbCdcblxuLyoqIEB0eXBlIHtJbml0aWFsQ29uc3RydWN0fSAqL1xuZXhwb3J0IGNvbnN0IGRvY3VtZW50ID0ge3Rva2VuaXplOiBpbml0aWFsaXplRG9jdW1lbnR9XG5cbi8qKiBAdHlwZSB7Q29uc3RydWN0fSAqL1xuY29uc3QgY29udGFpbmVyQ29uc3RydWN0ID0ge3Rva2VuaXplOiB0b2tlbml6ZUNvbnRhaW5lcn1cblxuLyoqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogICBTZWxmLlxuICogQHR5cGUge0luaXRpYWxpemVyfVxuICogICBJbml0aWFsaXplci5cbiAqL1xuZnVuY3Rpb24gaW5pdGlhbGl6ZURvY3VtZW50KGVmZmVjdHMpIHtcbiAgY29uc3Qgc2VsZiA9IHRoaXNcbiAgLyoqIEB0eXBlIHtBcnJheTxTdGFja0l0ZW0+fSAqL1xuICBjb25zdCBzdGFjayA9IFtdXG4gIGxldCBjb250aW51ZWQgPSAwXG4gIC8qKiBAdHlwZSB7VG9rZW5pemVDb250ZXh0IHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgY2hpbGRGbG93XG4gIC8qKiBAdHlwZSB7VG9rZW4gfCB1bmRlZmluZWR9ICovXG4gIGxldCBjaGlsZFRva2VuXG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICBsZXQgbGluZVN0YXJ0T2Zmc2V0XG5cbiAgcmV0dXJuIHN0YXJ0XG5cbiAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgIC8vIEZpcnN0IHdlIGl0ZXJhdGUgdGhyb3VnaCB0aGUgb3BlbiBibG9ja3MsIHN0YXJ0aW5nIHdpdGggdGhlIHJvb3RcbiAgICAvLyBkb2N1bWVudCwgYW5kIGRlc2NlbmRpbmcgdGhyb3VnaCBsYXN0IGNoaWxkcmVuIGRvd24gdG8gdGhlIGxhc3Qgb3BlblxuICAgIC8vIGJsb2NrLlxuICAgIC8vIEVhY2ggYmxvY2sgaW1wb3NlcyBhIGNvbmRpdGlvbiB0aGF0IHRoZSBsaW5lIG11c3Qgc2F0aXNmeSBpZiB0aGUgYmxvY2sgaXNcbiAgICAvLyB0byByZW1haW4gb3Blbi5cbiAgICAvLyBGb3IgZXhhbXBsZSwgYSBibG9jayBxdW90ZSByZXF1aXJlcyBhIGA+YCBjaGFyYWN0ZXIuXG4gICAgLy8gQSBwYXJhZ3JhcGggcmVxdWlyZXMgYSBub24tYmxhbmsgbGluZS5cbiAgICAvLyBJbiB0aGlzIHBoYXNlIHdlIG1heSBtYXRjaCBhbGwgb3IganVzdCBzb21lIG9mIHRoZSBvcGVuIGJsb2Nrcy5cbiAgICAvLyBCdXQgd2UgY2Fubm90IGNsb3NlIHVubWF0Y2hlZCBibG9ja3MgeWV0LCBiZWNhdXNlIHdlIG1heSBoYXZlIGEgbGF6eVxuICAgIC8vIGNvbnRpbnVhdGlvbiBsaW5lLlxuICAgIGlmIChjb250aW51ZWQgPCBzdGFjay5sZW5ndGgpIHtcbiAgICAgIGNvbnN0IGl0ZW0gPSBzdGFja1tjb250aW51ZWRdXG4gICAgICBzZWxmLmNvbnRhaW5lclN0YXRlID0gaXRlbVsxXVxuICAgICAgYXNzZXJ0KFxuICAgICAgICBpdGVtWzBdLmNvbnRpbnVhdGlvbixcbiAgICAgICAgJ2V4cGVjdGVkIGBjb250aW51YXRpb25gIHRvIGJlIGRlZmluZWQgb24gY29udGFpbmVyIGNvbnN0cnVjdCdcbiAgICAgIClcbiAgICAgIHJldHVybiBlZmZlY3RzLmF0dGVtcHQoXG4gICAgICAgIGl0ZW1bMF0uY29udGludWF0aW9uLFxuICAgICAgICBkb2N1bWVudENvbnRpbnVlLFxuICAgICAgICBjaGVja05ld0NvbnRhaW5lcnNcbiAgICAgICkoY29kZSlcbiAgICB9XG5cbiAgICAvLyBEb25lLlxuICAgIHJldHVybiBjaGVja05ld0NvbnRhaW5lcnMoY29kZSlcbiAgfVxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGZ1bmN0aW9uIGRvY3VtZW50Q29udGludWUoY29kZSkge1xuICAgIGFzc2VydChcbiAgICAgIHNlbGYuY29udGFpbmVyU3RhdGUsXG4gICAgICAnZXhwZWN0ZWQgYGNvbnRhaW5lclN0YXRlYCB0byBiZSBkZWZpbmVkIGFmdGVyIGNvbnRpbnVhdGlvbidcbiAgICApXG5cbiAgICBjb250aW51ZWQrK1xuXG4gICAgLy8gTm90ZTogdGhpcyBmaWVsZCBpcyBjYWxsZWQgYF9jbG9zZUZsb3dgIGJ1dCBpdCBhbHNvIGNsb3NlcyBjb250YWluZXJzLlxuICAgIC8vIFBlcmhhcHMgYSBnb29kIGlkZWEgdG8gcmVuYW1lIGl0IGJ1dCBpdOKAmXMgYWxyZWFkeSB1c2VkIGluIHRoZSB3aWxkIGJ5XG4gICAgLy8gZXh0ZW5zaW9ucy5cbiAgICBpZiAoc2VsZi5jb250YWluZXJTdGF0ZS5fY2xvc2VGbG93KSB7XG4gICAgICBzZWxmLmNvbnRhaW5lclN0YXRlLl9jbG9zZUZsb3cgPSB1bmRlZmluZWRcblxuICAgICAgaWYgKGNoaWxkRmxvdykge1xuICAgICAgICBjbG9zZUZsb3coKVxuICAgICAgfVxuXG4gICAgICAvLyBOb3RlOiB0aGlzIGFsZ29yaXRobSBmb3IgbW92aW5nIGV2ZW50cyBhcm91bmQgaXMgc2ltaWxhciB0byB0aGVcbiAgICAgIC8vIGFsZ29yaXRobSB3aGVuIGRlYWxpbmcgd2l0aCBsYXp5IGxpbmVzIGluIGB3cml0ZVRvQ2hpbGRgLlxuICAgICAgY29uc3QgaW5kZXhCZWZvcmVFeGl0cyA9IHNlbGYuZXZlbnRzLmxlbmd0aFxuICAgICAgbGV0IGluZGV4QmVmb3JlRmxvdyA9IGluZGV4QmVmb3JlRXhpdHNcbiAgICAgIC8qKiBAdHlwZSB7UG9pbnQgfCB1bmRlZmluZWR9ICovXG4gICAgICBsZXQgcG9pbnRcblxuICAgICAgLy8gRmluZCB0aGUgZmxvdyBjaHVuay5cbiAgICAgIHdoaWxlIChpbmRleEJlZm9yZUZsb3ctLSkge1xuICAgICAgICBpZiAoXG4gICAgICAgICAgc2VsZi5ldmVudHNbaW5kZXhCZWZvcmVGbG93XVswXSA9PT0gJ2V4aXQnICYmXG4gICAgICAgICAgc2VsZi5ldmVudHNbaW5kZXhCZWZvcmVGbG93XVsxXS50eXBlID09PSB0eXBlcy5jaHVua0Zsb3dcbiAgICAgICAgKSB7XG4gICAgICAgICAgcG9pbnQgPSBzZWxmLmV2ZW50c1tpbmRleEJlZm9yZUZsb3ddWzFdLmVuZFxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgYXNzZXJ0KHBvaW50LCAnY291bGQgbm90IGZpbmQgcHJldmlvdXMgZmxvdyBjaHVuaycpXG5cbiAgICAgIGV4aXRDb250YWluZXJzKGNvbnRpbnVlZClcblxuICAgICAgLy8gRml4IHBvc2l0aW9ucy5cbiAgICAgIGxldCBpbmRleCA9IGluZGV4QmVmb3JlRXhpdHNcblxuICAgICAgd2hpbGUgKGluZGV4IDwgc2VsZi5ldmVudHMubGVuZ3RoKSB7XG4gICAgICAgIHNlbGYuZXZlbnRzW2luZGV4XVsxXS5lbmQgPSB7Li4ucG9pbnR9XG4gICAgICAgIGluZGV4KytcbiAgICAgIH1cblxuICAgICAgLy8gSW5qZWN0IHRoZSBleGl0cyBlYXJsaWVyICh0aGV54oCZcmUgc3RpbGwgYWxzbyBhdCB0aGUgZW5kKS5cbiAgICAgIHNwbGljZShcbiAgICAgICAgc2VsZi5ldmVudHMsXG4gICAgICAgIGluZGV4QmVmb3JlRmxvdyArIDEsXG4gICAgICAgIDAsXG4gICAgICAgIHNlbGYuZXZlbnRzLnNsaWNlKGluZGV4QmVmb3JlRXhpdHMpXG4gICAgICApXG5cbiAgICAgIC8vIERpc2NhcmQgdGhlIGR1cGxpY2F0ZSBleGl0cy5cbiAgICAgIHNlbGYuZXZlbnRzLmxlbmd0aCA9IGluZGV4XG5cbiAgICAgIHJldHVybiBjaGVja05ld0NvbnRhaW5lcnMoY29kZSlcbiAgICB9XG5cbiAgICByZXR1cm4gc3RhcnQoY29kZSlcbiAgfVxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGZ1bmN0aW9uIGNoZWNrTmV3Q29udGFpbmVycyhjb2RlKSB7XG4gICAgLy8gTmV4dCwgYWZ0ZXIgY29uc3VtaW5nIHRoZSBjb250aW51YXRpb24gbWFya2VycyBmb3IgZXhpc3RpbmcgYmxvY2tzLCB3ZVxuICAgIC8vIGxvb2sgZm9yIG5ldyBibG9jayBzdGFydHMgKGUuZy4gYD5gIGZvciBhIGJsb2NrIHF1b3RlKS5cbiAgICAvLyBJZiB3ZSBlbmNvdW50ZXIgYSBuZXcgYmxvY2sgc3RhcnQsIHdlIGNsb3NlIGFueSBibG9ja3MgdW5tYXRjaGVkIGluXG4gICAgLy8gc3RlcCAxIGJlZm9yZSBjcmVhdGluZyB0aGUgbmV3IGJsb2NrIGFzIGEgY2hpbGQgb2YgdGhlIGxhc3QgbWF0Y2hlZFxuICAgIC8vIGJsb2NrLlxuICAgIGlmIChjb250aW51ZWQgPT09IHN0YWNrLmxlbmd0aCkge1xuICAgICAgLy8gTm8gbmVlZCB0byBgY2hlY2tgIHdoZXRoZXIgdGhlcmXigJlzIGEgY29udGFpbmVyLCBvZiBgZXhpdENvbnRhaW5lcnNgXG4gICAgICAvLyB3b3VsZCBiZSBtb290LlxuICAgICAgLy8gV2UgY2FuIGluc3RlYWQgaW1tZWRpYXRlbHkgYGF0dGVtcHRgIHRvIHBhcnNlIG9uZS5cbiAgICAgIGlmICghY2hpbGRGbG93KSB7XG4gICAgICAgIHJldHVybiBkb2N1bWVudENvbnRpbnVlZChjb2RlKVxuICAgICAgfVxuXG4gICAgICAvLyBJZiB3ZSBoYXZlIGNvbmNyZXRlIGNvbnRlbnQsIHN1Y2ggYXMgYmxvY2sgSFRNTCBvciBmZW5jZWQgY29kZSxcbiAgICAgIC8vIHdlIGNhbuKAmXQgaGF2ZSBjb250YWluZXJzIOKAnHBpZXJjZeKAnSBpbnRvIHRoZW0sIHNvIHdlIGNhbiBpbW1lZGlhdGVseVxuICAgICAgLy8gc3RhcnQuXG4gICAgICBpZiAoY2hpbGRGbG93LmN1cnJlbnRDb25zdHJ1Y3QgJiYgY2hpbGRGbG93LmN1cnJlbnRDb25zdHJ1Y3QuY29uY3JldGUpIHtcbiAgICAgICAgcmV0dXJuIGZsb3dTdGFydChjb2RlKVxuICAgICAgfVxuXG4gICAgICAvLyBJZiB3ZSBkbyBoYXZlIGZsb3csIGl0IGNvdWxkIHN0aWxsIGJlIGEgYmxhbmsgbGluZSxcbiAgICAgIC8vIGJ1dCB3ZeKAmWQgYmUgaW50ZXJydXB0aW5nIGl0IHcvIGEgbmV3IGNvbnRhaW5lciBpZiB0aGVyZeKAmXMgYSBjdXJyZW50XG4gICAgICAvLyBjb25zdHJ1Y3QuXG4gICAgICAvLyBUbyBkbzogbmV4dCBtYWpvcjogcmVtb3ZlIGBfZ2ZtVGFibGVEeW5hbWljSW50ZXJydXB0SGFja2AgKG5vIGxvbmdlclxuICAgICAgLy8gbmVlZGVkIGluIG1pY3JvbWFyay1leHRlbnNpb24tZ2ZtLXRhYmxlQDEuMC42KS5cbiAgICAgIHNlbGYuaW50ZXJydXB0ID0gQm9vbGVhbihcbiAgICAgICAgY2hpbGRGbG93LmN1cnJlbnRDb25zdHJ1Y3QgJiYgIWNoaWxkRmxvdy5fZ2ZtVGFibGVEeW5hbWljSW50ZXJydXB0SGFja1xuICAgICAgKVxuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHRoZXJlIGlzIGEgbmV3IGNvbnRhaW5lci5cbiAgICBzZWxmLmNvbnRhaW5lclN0YXRlID0ge31cbiAgICByZXR1cm4gZWZmZWN0cy5jaGVjayhcbiAgICAgIGNvbnRhaW5lckNvbnN0cnVjdCxcbiAgICAgIHRoZXJlSXNBTmV3Q29udGFpbmVyLFxuICAgICAgdGhlcmVJc05vTmV3Q29udGFpbmVyXG4gICAgKShjb2RlKVxuICB9XG5cbiAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgZnVuY3Rpb24gdGhlcmVJc0FOZXdDb250YWluZXIoY29kZSkge1xuICAgIGlmIChjaGlsZEZsb3cpIGNsb3NlRmxvdygpXG4gICAgZXhpdENvbnRhaW5lcnMoY29udGludWVkKVxuICAgIHJldHVybiBkb2N1bWVudENvbnRpbnVlZChjb2RlKVxuICB9XG5cbiAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgZnVuY3Rpb24gdGhlcmVJc05vTmV3Q29udGFpbmVyKGNvZGUpIHtcbiAgICBzZWxmLnBhcnNlci5sYXp5W3NlbGYubm93KCkubGluZV0gPSBjb250aW51ZWQgIT09IHN0YWNrLmxlbmd0aFxuICAgIGxpbmVTdGFydE9mZnNldCA9IHNlbGYubm93KCkub2Zmc2V0XG4gICAgcmV0dXJuIGZsb3dTdGFydChjb2RlKVxuICB9XG5cbiAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgZnVuY3Rpb24gZG9jdW1lbnRDb250aW51ZWQoY29kZSkge1xuICAgIC8vIFRyeSBuZXcgY29udGFpbmVycy5cbiAgICBzZWxmLmNvbnRhaW5lclN0YXRlID0ge31cbiAgICByZXR1cm4gZWZmZWN0cy5hdHRlbXB0KFxuICAgICAgY29udGFpbmVyQ29uc3RydWN0LFxuICAgICAgY29udGFpbmVyQ29udGludWUsXG4gICAgICBmbG93U3RhcnRcbiAgICApKGNvZGUpXG4gIH1cblxuICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICBmdW5jdGlvbiBjb250YWluZXJDb250aW51ZShjb2RlKSB7XG4gICAgYXNzZXJ0KFxuICAgICAgc2VsZi5jdXJyZW50Q29uc3RydWN0LFxuICAgICAgJ2V4cGVjdGVkIGBjdXJyZW50Q29uc3RydWN0YCB0byBiZSBkZWZpbmVkIG9uIHRva2VuaXplcidcbiAgICApXG4gICAgYXNzZXJ0KFxuICAgICAgc2VsZi5jb250YWluZXJTdGF0ZSxcbiAgICAgICdleHBlY3RlZCBgY29udGFpbmVyU3RhdGVgIHRvIGJlIGRlZmluZWQgb24gdG9rZW5pemVyJ1xuICAgIClcbiAgICBjb250aW51ZWQrK1xuICAgIHN0YWNrLnB1c2goW3NlbGYuY3VycmVudENvbnN0cnVjdCwgc2VsZi5jb250YWluZXJTdGF0ZV0pXG4gICAgLy8gVHJ5IGFub3RoZXIuXG4gICAgcmV0dXJuIGRvY3VtZW50Q29udGludWVkKGNvZGUpXG4gIH1cblxuICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICBmdW5jdGlvbiBmbG93U3RhcnQoY29kZSkge1xuICAgIGlmIChjb2RlID09PSBjb2Rlcy5lb2YpIHtcbiAgICAgIGlmIChjaGlsZEZsb3cpIGNsb3NlRmxvdygpXG4gICAgICBleGl0Q29udGFpbmVycygwKVxuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBjaGlsZEZsb3cgPSBjaGlsZEZsb3cgfHwgc2VsZi5wYXJzZXIuZmxvdyhzZWxmLm5vdygpKVxuICAgIGVmZmVjdHMuZW50ZXIodHlwZXMuY2h1bmtGbG93LCB7XG4gICAgICBfdG9rZW5pemVyOiBjaGlsZEZsb3csXG4gICAgICBjb250ZW50VHlwZTogY29uc3RhbnRzLmNvbnRlbnRUeXBlRmxvdyxcbiAgICAgIHByZXZpb3VzOiBjaGlsZFRva2VuXG4gICAgfSlcblxuICAgIHJldHVybiBmbG93Q29udGludWUoY29kZSlcbiAgfVxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGZ1bmN0aW9uIGZsb3dDb250aW51ZShjb2RlKSB7XG4gICAgaWYgKGNvZGUgPT09IGNvZGVzLmVvZikge1xuICAgICAgd3JpdGVUb0NoaWxkKGVmZmVjdHMuZXhpdCh0eXBlcy5jaHVua0Zsb3cpLCB0cnVlKVxuICAgICAgZXhpdENvbnRhaW5lcnMoMClcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgaWYgKG1hcmtkb3duTGluZUVuZGluZyhjb2RlKSkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICB3cml0ZVRvQ2hpbGQoZWZmZWN0cy5leGl0KHR5cGVzLmNodW5rRmxvdykpXG4gICAgICAvLyBHZXQgcmVhZHkgZm9yIHRoZSBuZXh0IGxpbmUuXG4gICAgICBjb250aW51ZWQgPSAwXG4gICAgICBzZWxmLmludGVycnVwdCA9IHVuZGVmaW5lZFxuICAgICAgcmV0dXJuIHN0YXJ0XG4gICAgfVxuXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgcmV0dXJuIGZsb3dDb250aW51ZVxuICB9XG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7VG9rZW59IHRva2VuXG4gICAqICAgVG9rZW4uXG4gICAqIEBwYXJhbSB7Ym9vbGVhbiB8IHVuZGVmaW5lZH0gW2VuZE9mRmlsZV1cbiAgICogICBXaGV0aGVyIHRoZSB0b2tlbiBpcyBhdCB0aGUgZW5kIG9mIHRoZSBmaWxlIChkZWZhdWx0OiBgZmFsc2VgKS5cbiAgICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAgICogICBOb3RoaW5nLlxuICAgKi9cbiAgZnVuY3Rpb24gd3JpdGVUb0NoaWxkKHRva2VuLCBlbmRPZkZpbGUpIHtcbiAgICBhc3NlcnQoY2hpbGRGbG93LCAnZXhwZWN0ZWQgYGNoaWxkRmxvd2AgdG8gYmUgZGVmaW5lZCB3aGVuIGNvbnRpbnVpbmcnKVxuICAgIGNvbnN0IHN0cmVhbSA9IHNlbGYuc2xpY2VTdHJlYW0odG9rZW4pXG4gICAgaWYgKGVuZE9mRmlsZSkgc3RyZWFtLnB1c2gobnVsbClcbiAgICB0b2tlbi5wcmV2aW91cyA9IGNoaWxkVG9rZW5cbiAgICBpZiAoY2hpbGRUb2tlbikgY2hpbGRUb2tlbi5uZXh0ID0gdG9rZW5cbiAgICBjaGlsZFRva2VuID0gdG9rZW5cbiAgICBjaGlsZEZsb3cuZGVmaW5lU2tpcCh0b2tlbi5zdGFydClcbiAgICBjaGlsZEZsb3cud3JpdGUoc3RyZWFtKVxuXG4gICAgLy8gQWxyaWdodCwgc28gd2UganVzdCBhZGRlZCBhIGxhenkgbGluZTpcbiAgICAvL1xuICAgIC8vIGBgYG1hcmtkb3duXG4gICAgLy8gPiBhXG4gICAgLy8gYi5cbiAgICAvL1xuICAgIC8vIE9yOlxuICAgIC8vXG4gICAgLy8gPiB+fn5jXG4gICAgLy8gZFxuICAgIC8vXG4gICAgLy8gT3I6XG4gICAgLy9cbiAgICAvLyA+IHwgZSB8XG4gICAgLy8gZlxuICAgIC8vIGBgYFxuICAgIC8vXG4gICAgLy8gVGhlIGNvbnN0cnVjdCBpbiB0aGUgc2Vjb25kIGV4YW1wbGUgKGZlbmNlZCBjb2RlKSBkb2VzIG5vdCBhY2NlcHQgbGF6eVxuICAgIC8vIGxpbmVzLCBzbyBpdCBtYXJrZWQgaXRzZWxmIGFzIGRvbmUgYXQgdGhlIGVuZCBvZiBpdHMgZmlyc3QgbGluZSwgYW5kXG4gICAgLy8gdGhlbiB0aGUgY29udGVudCBjb25zdHJ1Y3QgcGFyc2VzIGBkYC5cbiAgICAvLyBNb3N0IGNvbnN0cnVjdHMgaW4gbWFya2Rvd24gbWF0Y2ggb24gdGhlIGZpcnN0IGxpbmU6IGlmIHRoZSBmaXJzdCBsaW5lXG4gICAgLy8gZm9ybXMgYSBjb25zdHJ1Y3QsIGEgbm9uLWxhenkgbGluZSBjYW7igJl0IOKAnHVubWFrZeKAnSBpdC5cbiAgICAvL1xuICAgIC8vIFRoZSBjb25zdHJ1Y3QgaW4gdGhlIHRoaXJkIGV4YW1wbGUgaXMgcG90ZW50aWFsbHkgYSBHRk0gdGFibGUsIGFuZFxuICAgIC8vIHRob3NlIGFyZSAqd2VpcmQqLlxuICAgIC8vIEl0ICpjb3VsZCogYmUgYSB0YWJsZSwgZnJvbSB0aGUgZmlyc3QgbGluZSwgaWYgdGhlIGZvbGxvd2luZyBsaW5lXG4gICAgLy8gbWF0Y2hlcyBhIGNvbmRpdGlvbi5cbiAgICAvLyBJbiB0aGlzIGNhc2UsIHRoYXQgc2Vjb25kIGxpbmUgaXMgbGF6eSwgd2hpY2gg4oCcdW5tYWtlc+KAnSB0aGUgZmlyc3QgbGluZVxuICAgIC8vIGFuZCB0dXJucyB0aGUgd2hvbGUgaW50byBvbmUgY29udGVudCBibG9jay5cbiAgICAvL1xuICAgIC8vIFdl4oCZdmUgbm93IHBhcnNlZCB0aGUgbm9uLWxhenkgYW5kIHRoZSBsYXp5IGxpbmUsIGFuZCBjYW4gZmlndXJlIG91dFxuICAgIC8vIHdoZXRoZXIgdGhlIGxhenkgbGluZSBzdGFydGVkIGEgbmV3IGZsb3cgYmxvY2suXG4gICAgLy8gSWYgaXQgZGlkLCB3ZSBleGl0IHRoZSBjdXJyZW50IGNvbnRhaW5lcnMgYmV0d2VlbiB0aGUgdHdvIGZsb3cgYmxvY2tzLlxuICAgIGlmIChzZWxmLnBhcnNlci5sYXp5W3Rva2VuLnN0YXJ0LmxpbmVdKSB7XG4gICAgICBsZXQgaW5kZXggPSBjaGlsZEZsb3cuZXZlbnRzLmxlbmd0aFxuXG4gICAgICB3aGlsZSAoaW5kZXgtLSkge1xuICAgICAgICBpZiAoXG4gICAgICAgICAgLy8gVGhlIHRva2VuIHN0YXJ0cyBiZWZvcmUgdGhlIGxpbmUgZW5kaW5n4oCmXG4gICAgICAgICAgY2hpbGRGbG93LmV2ZW50c1tpbmRleF1bMV0uc3RhcnQub2Zmc2V0IDwgbGluZVN0YXJ0T2Zmc2V0ICYmXG4gICAgICAgICAgLy8g4oCmYW5kIGVpdGhlciBpcyBub3QgZW5kZWQgeWV04oCmXG4gICAgICAgICAgKCFjaGlsZEZsb3cuZXZlbnRzW2luZGV4XVsxXS5lbmQgfHxcbiAgICAgICAgICAgIC8vIOKApm9yIGVuZHMgYWZ0ZXIgaXQuXG4gICAgICAgICAgICBjaGlsZEZsb3cuZXZlbnRzW2luZGV4XVsxXS5lbmQub2Zmc2V0ID4gbGluZVN0YXJ0T2Zmc2V0KVxuICAgICAgICApIHtcbiAgICAgICAgICAvLyBFeGl0OiB0aGVyZeKAmXMgc3RpbGwgc29tZXRoaW5nIG9wZW4sIHdoaWNoIG1lYW5zIGl04oCZcyBhIGxhenkgbGluZVxuICAgICAgICAgIC8vIHBhcnQgb2Ygc29tZXRoaW5nLlxuICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIE5vdGU6IHRoaXMgYWxnb3JpdGhtIGZvciBtb3ZpbmcgZXZlbnRzIGFyb3VuZCBpcyBzaW1pbGFyIHRvIHRoZVxuICAgICAgLy8gYWxnb3JpdGhtIHdoZW4gY2xvc2luZyBmbG93IGluIGBkb2N1bWVudENvbnRpbnVlYC5cbiAgICAgIGNvbnN0IGluZGV4QmVmb3JlRXhpdHMgPSBzZWxmLmV2ZW50cy5sZW5ndGhcbiAgICAgIGxldCBpbmRleEJlZm9yZUZsb3cgPSBpbmRleEJlZm9yZUV4aXRzXG4gICAgICAvKiogQHR5cGUge2Jvb2xlYW4gfCB1bmRlZmluZWR9ICovXG4gICAgICBsZXQgc2VlblxuICAgICAgLyoqIEB0eXBlIHtQb2ludCB8IHVuZGVmaW5lZH0gKi9cbiAgICAgIGxldCBwb2ludFxuXG4gICAgICAvLyBGaW5kIHRoZSBwcmV2aW91cyBjaHVuayAodGhlIG9uZSBiZWZvcmUgdGhlIGxhenkgbGluZSkuXG4gICAgICB3aGlsZSAoaW5kZXhCZWZvcmVGbG93LS0pIHtcbiAgICAgICAgaWYgKFxuICAgICAgICAgIHNlbGYuZXZlbnRzW2luZGV4QmVmb3JlRmxvd11bMF0gPT09ICdleGl0JyAmJlxuICAgICAgICAgIHNlbGYuZXZlbnRzW2luZGV4QmVmb3JlRmxvd11bMV0udHlwZSA9PT0gdHlwZXMuY2h1bmtGbG93XG4gICAgICAgICkge1xuICAgICAgICAgIGlmIChzZWVuKSB7XG4gICAgICAgICAgICBwb2ludCA9IHNlbGYuZXZlbnRzW2luZGV4QmVmb3JlRmxvd11bMV0uZW5kXG4gICAgICAgICAgICBicmVha1xuICAgICAgICAgIH1cblxuICAgICAgICAgIHNlZW4gPSB0cnVlXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgYXNzZXJ0KHBvaW50LCAnY291bGQgbm90IGZpbmQgcHJldmlvdXMgZmxvdyBjaHVuaycpXG5cbiAgICAgIGV4aXRDb250YWluZXJzKGNvbnRpbnVlZClcblxuICAgICAgLy8gRml4IHBvc2l0aW9ucy5cbiAgICAgIGluZGV4ID0gaW5kZXhCZWZvcmVFeGl0c1xuXG4gICAgICB3aGlsZSAoaW5kZXggPCBzZWxmLmV2ZW50cy5sZW5ndGgpIHtcbiAgICAgICAgc2VsZi5ldmVudHNbaW5kZXhdWzFdLmVuZCA9IHsuLi5wb2ludH1cbiAgICAgICAgaW5kZXgrK1xuICAgICAgfVxuXG4gICAgICAvLyBJbmplY3QgdGhlIGV4aXRzIGVhcmxpZXIgKHRoZXnigJlyZSBzdGlsbCBhbHNvIGF0IHRoZSBlbmQpLlxuICAgICAgc3BsaWNlKFxuICAgICAgICBzZWxmLmV2ZW50cyxcbiAgICAgICAgaW5kZXhCZWZvcmVGbG93ICsgMSxcbiAgICAgICAgMCxcbiAgICAgICAgc2VsZi5ldmVudHMuc2xpY2UoaW5kZXhCZWZvcmVFeGl0cylcbiAgICAgIClcblxuICAgICAgLy8gRGlzY2FyZCB0aGUgZHVwbGljYXRlIGV4aXRzLlxuICAgICAgc2VsZi5ldmVudHMubGVuZ3RoID0gaW5kZXhcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQHBhcmFtIHtudW1iZXJ9IHNpemVcbiAgICogICBTaXplLlxuICAgKiBAcmV0dXJucyB7dW5kZWZpbmVkfVxuICAgKiAgIE5vdGhpbmcuXG4gICAqL1xuICBmdW5jdGlvbiBleGl0Q29udGFpbmVycyhzaXplKSB7XG4gICAgbGV0IGluZGV4ID0gc3RhY2subGVuZ3RoXG5cbiAgICAvLyBFeGl0IG9wZW4gY29udGFpbmVycy5cbiAgICB3aGlsZSAoaW5kZXgtLSA+IHNpemUpIHtcbiAgICAgIGNvbnN0IGVudHJ5ID0gc3RhY2tbaW5kZXhdXG4gICAgICBzZWxmLmNvbnRhaW5lclN0YXRlID0gZW50cnlbMV1cbiAgICAgIGFzc2VydChcbiAgICAgICAgZW50cnlbMF0uZXhpdCxcbiAgICAgICAgJ2V4cGVjdGVkIGBleGl0YCB0byBiZSBkZWZpbmVkIG9uIGNvbnRhaW5lciBjb25zdHJ1Y3QnXG4gICAgICApXG4gICAgICBlbnRyeVswXS5leGl0LmNhbGwoc2VsZiwgZWZmZWN0cylcbiAgICB9XG5cbiAgICBzdGFjay5sZW5ndGggPSBzaXplXG4gIH1cblxuICBmdW5jdGlvbiBjbG9zZUZsb3coKSB7XG4gICAgYXNzZXJ0KFxuICAgICAgc2VsZi5jb250YWluZXJTdGF0ZSxcbiAgICAgICdleHBlY3RlZCBgY29udGFpbmVyU3RhdGVgIHRvIGJlIGRlZmluZWQgd2hlbiBjbG9zaW5nIGZsb3cnXG4gICAgKVxuICAgIGFzc2VydChjaGlsZEZsb3csICdleHBlY3RlZCBgY2hpbGRGbG93YCB0byBiZSBkZWZpbmVkIHdoZW4gY2xvc2luZyBpdCcpXG4gICAgY2hpbGRGbG93LndyaXRlKFtjb2Rlcy5lb2ZdKVxuICAgIGNoaWxkVG9rZW4gPSB1bmRlZmluZWRcbiAgICBjaGlsZEZsb3cgPSB1bmRlZmluZWRcbiAgICBzZWxmLmNvbnRhaW5lclN0YXRlLl9jbG9zZUZsb3cgPSB1bmRlZmluZWRcbiAgfVxufVxuXG4vKipcbiAqIEB0aGlzIHtUb2tlbml6ZUNvbnRleHR9XG4gKiAgIENvbnRleHQuXG4gKiBAdHlwZSB7VG9rZW5pemVyfVxuICogICBUb2tlbml6ZXIuXG4gKi9cbmZ1bmN0aW9uIHRva2VuaXplQ29udGFpbmVyKGVmZmVjdHMsIG9rLCBub2spIHtcbiAgLy8gQWx3YXlzIHBvcHVsYXRlZCBieSBkZWZhdWx0cy5cbiAgYXNzZXJ0KFxuICAgIHRoaXMucGFyc2VyLmNvbnN0cnVjdHMuZGlzYWJsZS5udWxsLFxuICAgICdleHBlY3RlZCBgZGlzYWJsZS5udWxsYCB0byBiZSBwb3B1bGF0ZWQnXG4gIClcbiAgcmV0dXJuIGZhY3RvcnlTcGFjZShcbiAgICBlZmZlY3RzLFxuICAgIGVmZmVjdHMuYXR0ZW1wdCh0aGlzLnBhcnNlci5jb25zdHJ1Y3RzLmRvY3VtZW50LCBvaywgbm9rKSxcbiAgICB0eXBlcy5saW5lUHJlZml4LFxuICAgIHRoaXMucGFyc2VyLmNvbnN0cnVjdHMuZGlzYWJsZS5udWxsLmluY2x1ZGVzKCdjb2RlSW5kZW50ZWQnKVxuICAgICAgPyB1bmRlZmluZWRcbiAgICAgIDogY29uc3RhbnRzLnRhYlNpemVcbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/document.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/flow.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flow: () => (/* binding */ flow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/content.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n/** @type {InitialConstruct} */\nconst flow = {tokenize: initializeFlow}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeFlow(effects) {\n  const self = this\n  const initial = effects.attempt(\n    // Try to parse a blank line.\n    micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__.blankLine,\n    atBlankEnding,\n    // Try to parse initial flow (essentially, only code).\n    effects.attempt(\n      this.parser.constructs.flowInitial,\n      afterConstruct,\n      (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__.factorySpace)(\n        effects,\n        effects.attempt(\n          this.parser.constructs.flow,\n          afterConstruct,\n          effects.attempt(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.content, afterConstruct)\n        ),\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix\n      )\n    )\n  )\n\n  return initial\n\n  /** @type {State} */\n  function atBlankEnding(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code),\n      'expected eol or eof'\n    )\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank)\n    self.currentConstruct = undefined\n    return initial\n  }\n\n  /** @type {State} */\n  function afterConstruct(code) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code),\n      'expected eol or eof'\n    )\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n    self.currentConstruct = undefined\n    return initial\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/text.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/text.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolver: () => (/* binding */ resolver),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   InitialConstruct,\n *   Initializer,\n *   Resolver,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\n\n\n\nconst resolver = {resolveAll: createResolver()}\nconst string = initializeFactory('string')\nconst text = initializeFactory('text')\n\n/**\n * @param {'string' | 'text'} field\n *   Field.\n * @returns {InitialConstruct}\n *   Construct.\n */\nfunction initializeFactory(field) {\n  return {\n    resolveAll: createResolver(\n      field === 'text' ? resolveAllLineSuffixes : undefined\n    ),\n    tokenize: initializeText\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Initializer}\n   */\n  function initializeText(effects) {\n    const self = this\n    const constructs = this.parser.constructs[field]\n    const text = effects.attempt(constructs, start, notText)\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      return atBreak(code) ? text(code) : notText(code)\n    }\n\n    /** @type {State} */\n    function notText(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n        effects.consume(code)\n        return\n      }\n\n      effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data)\n      effects.consume(code)\n      return data\n    }\n\n    /** @type {State} */\n    function data(code) {\n      if (atBreak(code)) {\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data)\n        return text(code)\n      }\n\n      // Data.\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * @param {Code} code\n     *   Code.\n     * @returns {boolean}\n     *   Whether the code is a break.\n     */\n    function atBreak(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n        return true\n      }\n\n      const list = constructs[code]\n      let index = -1\n\n      if (list) {\n        // Always populated by defaults.\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(Array.isArray(list), 'expected `disable.null` to be populated')\n\n        while (++index < list.length) {\n          const item = list[index]\n          if (!item.previous || item.previous.call(self, self.previous)) {\n            return true\n          }\n        }\n      }\n\n      return false\n    }\n  }\n}\n\n/**\n * @param {Resolver | undefined} [extraResolver]\n *   Resolver.\n * @returns {Resolver}\n *   Resolver.\n */\nfunction createResolver(extraResolver) {\n  return resolveAllText\n\n  /** @type {Resolver} */\n  function resolveAllText(events, context) {\n    let index = -1\n    /** @type {number | undefined} */\n    let enter\n\n    // A rather boring computation (to merge adjacent `data` events) which\n    // improves mm performance by 29%.\n    while (++index <= events.length) {\n      if (enter === undefined) {\n        if (events[index] && events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n          enter = index\n          index++\n        }\n      } else if (!events[index] || events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n        // Don’t do anything if there is one data token.\n        if (index !== enter + 2) {\n          events[enter][1].end = events[index - 1][1].end\n          events.splice(enter + 2, index - enter - 2)\n          index = enter + 2\n        }\n\n        enter = undefined\n      }\n    }\n\n    return extraResolver ? extraResolver(events, context) : events\n  }\n}\n\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */\nfunction resolveAllLineSuffixes(events, context) {\n  let eventIndex = 0 // Skip first.\n\n  while (++eventIndex <= events.length) {\n    if (\n      (eventIndex === events.length ||\n        events[eventIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding) &&\n      events[eventIndex - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data\n    ) {\n      const data = events[eventIndex - 1][1]\n      const chunks = context.sliceStream(data)\n      let index = chunks.length\n      let bufferIndex = -1\n      let size = 0\n      /** @type {boolean | undefined} */\n      let tabs\n\n      while (index--) {\n        const chunk = chunks[index]\n\n        if (typeof chunk === 'string') {\n          bufferIndex = chunk.length\n\n          while (chunk.charCodeAt(bufferIndex - 1) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space) {\n            size++\n            bufferIndex--\n          }\n\n          if (bufferIndex) break\n          bufferIndex = -1\n        }\n        // Number\n        else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab) {\n          tabs = true\n          size++\n        } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace) {\n          // Empty\n        } else {\n          // Replacement character, exit.\n          index++\n          break\n        }\n      }\n\n      // Allow final trailing whitespace.\n      if (context._contentTypeTextTrailing && eventIndex === events.length) {\n        size = 0\n      }\n\n      if (size) {\n        const token = {\n          type:\n            eventIndex === events.length ||\n            tabs ||\n            size < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.hardBreakPrefixSizeMin\n              ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineSuffix\n              : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.hardBreakTrailing,\n          start: {\n            _bufferIndex: index\n              ? bufferIndex\n              : data.start._bufferIndex + bufferIndex,\n            _index: data.start._index + index,\n            line: data.end.line,\n            column: data.end.column - size,\n            offset: data.end.offset - size\n          },\n          end: {...data.end}\n        }\n\n        data.end = {...token.start}\n\n        if (data.start.offset === data.end.offset) {\n          Object.assign(data, token)\n        } else {\n          events.splice(\n            eventIndex,\n            0,\n            ['enter', token, context],\n            ['exit', token, context]\n          )\n          eventIndex += 2\n        }\n      }\n\n      eventIndex++\n    }\n  }\n\n  return events\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/parse.js":
/*!*************************************************!*\
  !*** ./node_modules/micromark/dev/lib/parse.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-combine-extensions */ \"(ssr)/./node_modules/micromark-util-combine-extensions/index.js\");\n/* harmony import */ var _initialize_content_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initialize/content.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/content.js\");\n/* harmony import */ var _initialize_document_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initialize/document.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/document.js\");\n/* harmony import */ var _initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./initialize/flow.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/* harmony import */ var _constructs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constructs.js */ \"(ssr)/./node_modules/micromark/dev/lib/constructs.js\");\n/* harmony import */ var _create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./create-tokenizer.js */ \"(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js\");\n/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */\nfunction parse(options) {\n  const settings = options || {}\n  const constructs = /** @type {FullNormalizedExtension} */ (\n    (0,micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__.combineExtensions)([_constructs_js__WEBPACK_IMPORTED_MODULE_1__, ...(settings.extensions || [])])\n  )\n\n  /** @type {ParseContext} */\n  const parser = {\n    constructs,\n    content: create(_initialize_content_js__WEBPACK_IMPORTED_MODULE_2__.content),\n    defined: [],\n    document: create(_initialize_document_js__WEBPACK_IMPORTED_MODULE_3__.document),\n    flow: create(_initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__.flow),\n    lazy: {},\n    string: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.string),\n    text: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.text)\n  }\n\n  return parser\n\n  /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */\n  function create(initial) {\n    return creator\n    /** @type {Create} */\n    function creator(from) {\n      return (0,_create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__.createTokenizer)(parser, initial, from)\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/postprocess.js":
/*!*******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/postprocess.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   postprocess: () => (/* binding */ postprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-subtokenize */ \"(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */\n\n\n\n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */\nfunction postprocess(events) {\n  while (!(0,micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__.subtokenize)(events)) {\n    // Empty\n  }\n\n  return events\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvcG9zdHByb2Nlc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksT0FBTztBQUNuQjs7QUFFc0Q7O0FBRXREO0FBQ0EsV0FBVyxjQUFjO0FBQ3pCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLFVBQVUsdUVBQVc7QUFDckI7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcbm9kZV9tb2R1bGVzXFxtaWNyb21hcmtcXGRldlxcbGliXFxwb3N0cHJvY2Vzcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0V2ZW50fSBmcm9tICdtaWNyb21hcmstdXRpbC10eXBlcydcbiAqL1xuXG5pbXBvcnQge3N1YnRva2VuaXplfSBmcm9tICdtaWNyb21hcmstdXRpbC1zdWJ0b2tlbml6ZSdcblxuLyoqXG4gKiBAcGFyYW0ge0FycmF5PEV2ZW50Pn0gZXZlbnRzXG4gKiAgIEV2ZW50cy5cbiAqIEByZXR1cm5zIHtBcnJheTxFdmVudD59XG4gKiAgIEV2ZW50cy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBvc3Rwcm9jZXNzKGV2ZW50cykge1xuICB3aGlsZSAoIXN1YnRva2VuaXplKGV2ZW50cykpIHtcbiAgICAvLyBFbXB0eVxuICB9XG5cbiAgcmV0dXJuIGV2ZW50c1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/postprocess.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/preprocess.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/preprocess.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   preprocess: () => (/* binding */ preprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */\n\n\n\nconst search = /[\\0\\t\\n\\r]/g\n\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */\nfunction preprocess() {\n  let column = 1\n  let buffer = ''\n  /** @type {boolean | undefined} */\n  let start = true\n  /** @type {boolean | undefined} */\n  let atCarriageReturn\n\n  return preprocessor\n\n  /** @type {Preprocessor} */\n  // eslint-disable-next-line complexity\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = []\n    /** @type {RegExpMatchArray | null} */\n    let match\n    /** @type {number} */\n    let next\n    /** @type {number} */\n    let startPosition\n    /** @type {number} */\n    let endPosition\n    /** @type {Code} */\n    let code\n\n    value =\n      buffer +\n      (typeof value === 'string'\n        ? value.toString()\n        : new TextDecoder(encoding || undefined).decode(value))\n\n    startPosition = 0\n    buffer = ''\n\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.byteOrderMarker) {\n        startPosition++\n      }\n\n      start = undefined\n    }\n\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition\n      match = search.exec(value)\n      endPosition =\n        match && match.index !== undefined ? match.index : value.length\n      code = value.charCodeAt(endPosition)\n\n      if (!match) {\n        buffer = value.slice(startPosition)\n        break\n      }\n\n      if (\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf &&\n        startPosition === endPosition &&\n        atCarriageReturn\n      ) {\n        chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed)\n        atCarriageReturn = undefined\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn)\n          atCarriageReturn = undefined\n        }\n\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition))\n          column += endPosition - startPosition\n        }\n\n        switch (code) {\n          case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.nul: {\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.replacementCharacter)\n            column++\n\n            break\n          }\n\n          case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ht: {\n            next = Math.ceil(column / micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize) * micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab)\n            while (column++ < next) chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace)\n\n            break\n          }\n\n          case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf: {\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed)\n            column = 1\n\n            break\n          }\n\n          default: {\n            atCarriageReturn = true\n            column = 1\n          }\n        }\n      }\n\n      startPosition = endPosition + 1\n    }\n\n    if (end) {\n      if (atCarriageReturn) chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn)\n      if (buffer) chunks.push(buffer)\n      chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof)\n    }\n\n    return chunks\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/preprocess.js\n");

/***/ })

};
;