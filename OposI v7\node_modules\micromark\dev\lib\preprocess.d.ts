/**
 * @returns {Preprocessor}
 *   Preprocess a value.
 */
export function preprocess(): Preprocessor;
/**
 * Preprocess a value.
 */
export type Preprocessor = (value: Value, encoding?: Encoding | null | undefined, end?: boolean | null | undefined) => Array<Chunk>;
import type { Value } from 'micromark-util-types';
import type { Encoding } from 'micromark-util-types';
import type { Chunk } from 'micromark-util-types';
//# sourceMappingURL=preprocess.d.ts.map