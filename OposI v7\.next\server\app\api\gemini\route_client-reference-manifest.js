globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/gemini/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{},"edgeSSRModuleMapping":{},"clientModules":{},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\api\\gemini\\route":[]},"rscModuleMapping":{},"edgeRscModuleMapping":{}}