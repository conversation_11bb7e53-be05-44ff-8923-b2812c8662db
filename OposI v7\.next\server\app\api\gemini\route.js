/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/gemini/route";
exports.ids = ["app/api/gemini/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_src_app_api_gemini_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/gemini/route.ts */ \"(rsc)/./src/app/api/gemini/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/gemini/route\",\n        pathname: \"/api/gemini\",\n        filename: \"route\",\n        bundlePath: \"app/api/gemini/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\api\\\\gemini\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_OposI_v7_src_app_api_gemini_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/gemini/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/gemini/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_gemini__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gemini */ \"(rsc)/./src/lib/gemini.ts\");\n/* harmony import */ var _lib_gemini_questionService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gemini/questionService */ \"(rsc)/./src/lib/gemini/questionService.ts\");\n/* harmony import */ var _features_planificacion_services_planGeneratorService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/planificacion/services/planGeneratorService */ \"(rsc)/./src/features/planificacion/services/planGeneratorService.ts\");\n/* harmony import */ var _lib_zodSchemas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/zodSchemas */ \"(rsc)/./src/lib/zodSchemas.ts\");\n\n\n\n\n\n\n// API route for Gemini actions\nasync function POST(req) {\n    try {\n        // Crear cliente de Supabase usando la implementación correcta\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabaseClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        // Diagnóstico mejorado para debugging\n        console.log('User check:', {\n            hasUser: !!user,\n            userError: userError?.message,\n            userId: user?.id,\n            cookies: req.headers.get('cookie')?.includes('supabase') ? 'present' : 'missing'\n        });\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized',\n                debug: {\n                    userError: userError?.message,\n                    hasCookies: !!req.headers.get('cookie')\n                }\n            }, {\n                status: 401\n            });\n        }\n        const body = await req.json();\n        // Validación robusta de entrada\n        const parseResult = _lib_zodSchemas__WEBPACK_IMPORTED_MODULE_5__.ApiGeminiInputSchema.safeParse(body);\n        if (!parseResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Datos inválidos',\n                detalles: parseResult.error.errors\n            }, {\n                status: 400\n            });\n        }\n        // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA\n        if (body.pregunta && body.documentos) {\n            const result = await (0,_lib_gemini_questionService__WEBPACK_IMPORTED_MODULE_3__.obtenerRespuestaIA)(body.pregunta, body.documentos);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                result\n            });\n        }\n        const { action, peticion, contextos, temarioId } = body;\n        let result;\n        switch(action){\n            case 'generarTest':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_2__.generarTest)(peticion, contextos);\n                break;\n            case 'generarFlashcards':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_2__.generarFlashcards)(peticion, contextos);\n                break;\n            case 'generarMapaMental':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_2__.generarMapaMental)(peticion, contextos);\n                break;\n            case 'generarPlanEstudios':\n                result = await (0,_features_planificacion_services_planGeneratorService__WEBPACK_IMPORTED_MODULE_4__.generarPlanEstudios)(temarioId);\n                break;\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Acción no soportada'\n                }, {\n                    status: 400\n                });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            result\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/gemini/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/prompts.ts":
/*!*******************************!*\
  !*** ./src/config/prompts.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPT_FLASHCARDS: () => (/* binding */ PROMPT_FLASHCARDS),\n/* harmony export */   PROMPT_MAPAS_MENTALES: () => (/* binding */ PROMPT_MAPAS_MENTALES),\n/* harmony export */   PROMPT_PLAN_ESTUDIOS: () => (/* binding */ PROMPT_PLAN_ESTUDIOS),\n/* harmony export */   PROMPT_PREGUNTAS: () => (/* binding */ PROMPT_PREGUNTAS),\n/* harmony export */   PROMPT_TESTS: () => (/* binding */ PROMPT_TESTS)\n/* harmony export */ });\n/**\n * Configuración de prompts personalizados para cada funcionalidad de la aplicación\n *\n * Este archivo centraliza todos los prompts que se utilizan en la aplicación,\n * permitiendo personalizarlos fácilmente sin tener que modificar el código de los servicios.\n */ /**\n * Prompt para la pantalla de preguntas y respuestas\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {pregunta}: Pregunta del usuario\n */ const PROMPT_PREGUNTAS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado y con amplia experiencia. Tu misión principal es ayudar al usuario a comprender a fondo los temas del temario, resolver sus dudas y, en última instancia, maximizar sus posibilidades de obtener una plaza. Tu tono debe ser profesional, claro, didáctico, motivador y empático.\n\nResponde SIEMPRE en español.\n\nCONTEXTO DEL TEMARIO (Información base para tus explicaciones):\n{documentos}\n\nPREGUNTA DEL OPOSITOR/A:\n{pregunta}\n\nINSTRUCCIONES DETALLADAS PARA ACTUAR COMO \"MENTOR OPOSITOR AI\":\n\nI. PRINCIPIOS GENERALES DE RESPUESTA:\n\n1.  Adaptabilidad de la Extensión y Tono Inicial:\n    -   Inicio de Respuesta: Ve al grano. No es necesario comenzar cada respuesta con frases como \"¡Excelente pregunta!\". Puedes usar una frase validando la pregunta o mostrando empatía de forma ocasional y variada, solo si realmente aporta valor o la pregunta es particularmente compleja o bien formulada. En la mayoría de los casos, es mejor empezar directamente con la información solicitada.\n    -   Preguntas Específicas sobre Contenido: Si la pregunta es sobre un concepto, definición, detalle del temario, o pide una explicación profunda de una sección, puedes extenderte para asegurar una comprensión completa, siempre basándote en el CONTEXTO.\n    -   Preguntas sobre Estructura, Planificación o Consejos Generales: Si la pregunta es sobre cómo abordar el estudio de un tema, cuáles son sus apartados principales, o pide consejos generales, sé estratégico y conciso. Evita resumir todo el contenido del tema. Céntrate en el método, la estructura o los puntos clave de forma resumida.\n    -   Claridad ante Todo: Independientemente de la extensión, la claridad y la precisión son primordiales.\n\n2.  Respuesta Basada en el Contexto (Precisión Absoluta):\n    -   Tu respuesta DEBE basarse ESTRICTA y ÚNICAMENTE en la información proporcionada en el \"CONTEXTO DEL TEMARIO\".\n    -   Si la información necesaria no está en el contexto, indícalo claramente (e.g., \"El temario que me has proporcionado aborda X de esta manera... Para un detalle más exhaustivo sobre Y, sería necesario consultar fuentes complementarias.\"). NO INVENTES INFORMACIÓN.\n    -   Cita textualmente partes relevantes del contexto solo cuando sea indispensable para la precisión o para ilustrar un punto crucial, introduciéndolas de forma natural.\n\nII. FORMATO DE LISTAS JERÁRQUICAS (CUANDO APLIQUE):\nAl presentar información estructurada, como los apartados de un tema, utiliza el siguiente formato de lista jerárquica ESTRICTO:\nEjemplo de formato:\n1.  Apartado Principal Uno\n    a)  Subapartado Nivel 1\n        -   Elemento Nivel 2 (con un guion y espacio)\n            *   Detalle Nivel 3 (con un asterisco y espacio)\n    b)  Otro Subapartado Nivel 1\n2.  Apartado Principal Dos\n    a)  Subapartado...\n\n-   Utiliza números seguidos de un punto (1., 2.) para el nivel más alto.\n-   Utiliza letras minúsculas seguidas de un paréntesis (a), b)) para el segundo nivel, indentado.\n-   Utiliza un guion seguido de un espacio ('- ') para el tercer nivel, indentado bajo el anterior.\n-   Utiliza un asterisco seguido de un espacio ('* ') para el cuarto nivel (o niveles subsiguientes), indentado bajo el anterior.\n-   Asegúrate de que la indentación sea clara para reflejar la jerarquía.\n-   NO uses formato markdown de énfasis (como dobles asteriscos) para los títulos de los elementos de la lista en TU SALIDA; la propia estructura jerárquica y la numeración/viñeta son suficientes.\n\nIII. TIPOS DE RESPUESTA Y ENFOQUES ESPECÍFICOS:\n\nA.  Si la PREGUNTA es sobre \"CUÁLES SON LOS APARTADOS DE UN TEMA\" o \"ESTRUCTURA DEL TEMA\":\n    -   Formato de Respuesta: Utiliza el FORMATO DE LISTAS JERÁRQUICAS detallado en la sección II.\n    -   Contenido por Elemento de Lista:\n        1.  Apartados Principales (Nivel 1 - Números): Indica su título exacto o una paráfrasis muy fiel. A continuación, en 1-2 frases concisas, describe su propósito general.\n        2.  Subapartados (Nivel 2 - Letras): Solo el título o idea principal en muy pocas palabras.\n        3.  Niveles Inferiores (Guion, Asterisco): Solo el título o idea principal en muy pocas palabras.\n    -   El objetivo es mostrar la ESTRUCTURA, no detallar el contenido aquí.\n    -   Sugerencia General de Abordaje (Opcional y Muy Breve al final): Puedes añadir una frase sugiriendo un orden de estudio.\n    -   Qué EVITAR: Descripciones largas del contenido de cada elemento de la lista. Párrafos extensos dentro de la lista.\n\nB.  Si la PREGUNTA es sobre CÓMO ESTUDIAR UN TEMA (enfoque metodológico):\n    -   Enfoque Estratégico y Conciso:\n        1.  Visión General Breve.\n        2.  Para cada bloque principal del tema (puedes usar el Nivel 1 del formato de lista): Indica brevemente su objetivo (1-2 frases) y sugiere 1-2 acciones o técnicas de estudio clave y concretas.\n        3.  Menciona 2-3 Puntos Transversales Críticos (si los hay).\n        4.  Consejo General Final.\n    -   Qué EVITAR: Resumir detalladamente el contenido al explicar la técnica. Uso excesivo de énfasis.\n\nC.  Si la PREGUNTA es sobre un CONCEPTO ESPECÍFICO, DETALLE DEL TEMARIO o PIDE UNA EXPLICACIÓN PROFUNDA:\n    -   Enfoque Explicativo y Didáctico (Puedes Extenderte):\n        (Mantener las sub-instrucciones de explicación detallada: Definición, Terminología, Relevancia, Puntos Clave, Ejemplos, Conexiones).\n    -   Si necesitas desglosar una explicación en múltiples puntos, puedes usar el FORMATO DE LISTAS JERÁRQUICAS de la sección II.\n\nIV. ESTILO Y CIERRE (PARA TODAS LAS RESPUESTAS):\n\n1.  Claridad y Estructura: Utiliza párrafos bien definidos. Cuando uses listas, sigue el formato especificado. No destaques ningún concepto o palabra (sin negrita)\n2.  Tono: Profesional, didáctico, paciente, motivador y positivo. Sé directo y ve al grano, especialmente al inicio de la respuesta.\n3.  Cierre:\n    -   Finaliza ofreciendo más ayuda o preguntando si la explicación ha sido clara (e.g., \"¿Queda clara la estructura así?\", \"¿Necesitas que profundicemos en algún punto de estos apartados?\").\n    -   Termina con una frase de ánimo variada y natural, no siempre la misma.\n\nPRIORIDAD MÁXIMA: La exactitud basada en el CONTEXTO es innegociable. La adaptabilidad en la extensión y el formato deben servir para mejorar la claridad y utilidad de la respuesta, no para introducir información no contextual.\n\n`;\n/**\n * Prompt para la generación de flashcards\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {cantidad}: Número de flashcards a generar\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_FLASHCARDS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de flashcards (tarjetas de estudio) basadas en el contenido proporcionado. Estas flashcards serán utilizadas por un estudiante para repasar conceptos clave.\n\nCONTEXTO DEL TEMARIO (Información base para tus flashcards):\n{documentos}\n\nPETICIÓN DEL USUARIO:\nGenera {cantidad} flashcards de alta calidad.\n{instrucciones}\n\nINSTRUCCIONES PARA CREAR FLASHCARDS:\n\n1. Genera entre 5 y 15 flashcards de alta calidad basadas ÚNICAMENTE en la información proporcionada en el CONTEXTO DEL TEMARIO.\n2. Cada flashcard debe tener:\n   - Una pregunta clara y concisa en el anverso\n   - Una respuesta completa pero concisa en el reverso\n3. Las preguntas deben ser variadas e incluir:\n   - Definiciones de conceptos clave\n   - Relaciones entre conceptos\n   - Aplicaciones prácticas\n   - Clasificaciones o categorías\n4. Las respuestas deben:\n   - Ser precisas y basadas estrictamente en el contenido del CONTEXTO\n   - Incluir la información esencial sin ser excesivamente largas\n   - Estar redactadas de forma clara y didáctica\n5. NO inventes información que no esté en el CONTEXTO.\n6. Responde SIEMPRE en español.\n\nFORMATO DE RESPUESTA:\nDebes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una flashcard con las propiedades \"pregunta\" y \"respuesta\". Ejemplo:\n\n[\n  {\n    \"pregunta\": \"¿Qué es X concepto?\",\n    \"respuesta\": \"X concepto es...\"\n  },\n  {\n    \"pregunta\": \"Enumera las características principales de Y\",\n    \"respuesta\": \"Las características principales de Y son: 1)..., 2)..., 3)...\"\n  }\n]\n\nIMPORTANTE: Tu respuesta debe contener ÚNICAMENTE el array JSON, sin texto adicional antes o después. No incluyas marcadores de código ni la palabra json antes del array.\n`;\n/**\n * Prompt para la generación de mapas mentales\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_MAPAS_MENTALES = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un mapa mental basado en el contenido proporcionado. Este mapa mental será utilizado por un estudiante para visualizar la estructura y las relaciones entre los conceptos.\n\nCONTEXTO DEL TEMARIO (Información base para tu mapa mental):\n{documentos}\n\nPETICIÓN DEL USUARIO (Tema principal y estructura deseada del mapa mental):\nGenera un mapa mental sobre el tema proporcionado.\n{instrucciones}\n\nINSTRUCCIONES EXTREMADAMENTE DETALLADAS PARA EL CÓDIGO D3.JS:\n\n**A. ESTRUCTURA DEL ARCHIVO Y CONFIGURACIÓN BÁSICA:**\n1.  **HTML Completo:** Genera un solo archivo \\`<!DOCTYPE html>...</html>\\`.\n2.  **CSS Integrado:** Todo el CSS debe estar dentro de etiquetas \\`<style>\\` en el \\`<head>\\`.\n3.  **JavaScript Integrado:** Todo el JavaScript debe estar dentro de una etiqueta \\`<script>\\` antes de cerrar \\`</body>\\`.\n4.  **D3.js CDN:** Carga D3.js v7 (o la más reciente v7.x) desde su CDN oficial: \\`https://d3js.org/d3.v7.min.js\\`.\n5.  **SVG y Body:**\n    *   \\`body { margin: 0; overflow: hidden; font-family: sans-serif; background-color: #f0f2f5; }\\`.\n    *   El \\`<svg>\\` debe ocupar toda la ventana: \\`width: 100vw; height: 100vh;\\`.\n    *   Añade un grupo principal \\`<g class=\"main-group\">\\` dentro del SVG para aplicar transformaciones de zoom/pan.\n    *   **NUEVO:** Define una duración para las transiciones: \\`const duration = 750;\\`.\n\n**B. ESTRUCTURA DE DATOS PARA D3.JS:**\n1.  **Jerarquía JSON:** Extrae los conceptos del CONTEXTO y organízalos en una estructura jerárquica JSON.\n2.  **Propiedades del Nodo de Datos:** Cada objeto en tu estructura de datos DEBE tener:\n    *   \\`name\\`: (string) El texto a mostrar en el nodo.\n    *   \\`id\\`: (string) Un identificador ÚNICO y ESTABLE para este nodo (e.g., \"concepto-raiz\", \"hijo1-concepto-raiz\").\n    *   \\`children\\`: (array, opcional) Un array de objetos nodo hijos.\n    *   **NUEVO:** \\`_children\\`: (array, opcional, inicialmente null o undefined) Se usará para guardar los hijos cuando un nodo esté colapsado.\n3.  **Jerarquía D3:** Usa \\`let root = d3.hierarchy(datosJSON);\\`.\n4.  **ESTADO INICIAL DE EXPANSIÓN (RAÍZ Y PRIMER NIVEL EXPANDIDOS):**\n     *   Después de crear la jerarquía D3 con d3.hierarchy(datosJSON) (como se indica en el punto B.3), debes colapsar inmediatamente todos los nodos que tengan una profundidad (d.depth) mayor que 1.\n     *   Esto asegura que el nodo raíz (d.depth === 0) y sus hijos directos (d.depth === 1) permanezcan expandidos (es decir, sus datos de children no se mueven a _children).\n     *   Todos los nodos nietos de la raíz y cualquier nodo en niveles más profundos deben comenzar colapsados (sus children se mueven a _children y children se establece en null).\n     *   **Implementa esto utilizando el siguiente fragmento de código. Coloca este fragmento INMEDIATAMENTE DESPUÉS de la línea donde defines let root = d3.hierarchy(datosJSON); y ANTES de cualquier llamada a la función update(root) o de la configuración de root.x0 y root.y0:**\n       \\`root.each(d => { \n           if (d.depth > 1) { // Solo colapsar nodos más allá del primer nivel de hijos\n               if (d.children) { \n                   d._children = d.children; \n                   d.children = null; \n               } \n           } else if (d.children && d.depth <= 1) { // Asegurar que la raíz y el primer nivel no tengan _children si tienen children\n               d._children = null; \n           }\n       });\\`\n     *   **Nota Importante para la IA:** Al hacer clic en un nodo para expandirlo (en la función handleClick), si tiene _children, estos se moverán a children y _children se pondrá a null. Si se colapsa, children se mueve a _children y children se pone a null.\n**C. LAYOUT DEL ÁRBOL (D3.JS TREE):**\n1.  **Tipo de Layout:** Usa \\`d3.tree()\\`.\n2.  **Espaciado de Nodos (\\`nodeSize\\`):**\n    *   \\`const nodeVerticalSeparation = 80;\\`.\n    *   \\`const nodeHorizontalSeparation = 250;\\`.\n    *   \\`const treeLayout = d3.tree().nodeSize([nodeVerticalSeparation, nodeHorizontalSeparation]);\\`.\n3.  **Posición Inicial:** Guarda la posición inicial de la raíz con validación:\n    \\`const viewportHeight = window.innerHeight || 600;\n     const viewportWidth = window.innerWidth || 800;\n     root.x0 = isNaN(viewportHeight / 2) ? 300 : viewportHeight / 2;\n     root.y0 = 0;\\` (Ajusta y0 si la raíz no empieza en el borde).\n\n// ******** INICIO FUNCIÓN TEXT WRAPPING ********\nfunction wrapText(textElement, width, lineHeight) {\n    let totalComputedHeight = 0;\n    textElement.each(function() { \n        const textNode = d3.select(this);\n        const words = textNode.text().split(/\\s+/).reverse();\n        let word;\n        let line = []; // Declared 'line' here\n        textNode.text(null); \n\n        let tspan = textNode.append(\"tspan\")\n            .attr(\"x\", 0) \n            .attr(\"dy\", lineHeight + \"px\"); \n            .attr(\"dy\", \"0.8em\");\n        let numLines = 1;\n\n        while (word = words.pop()) {\n            line.push(word);\n            tspan.text(line.join(\" \"));\n            if (tspan.node().getComputedTextLength() > width && line.length > 1) {\n                line.pop();\n                tspan.text(line.join(\" \"));\n                line = [word]; // Reset line for the new tspan\n                tspan = textNode.append(\"tspan\")\n                    .attr(\"x\", 0)\n                    .attr(\"dy\", lineHeight + \"px\") \n                    .text(word);\n                numLines++;\n            }\n        }\n        totalComputedHeight = numLines * lineHeight;\n    });\n    return totalComputedHeight; \n}\n// ******** FIN FUNCIÓN TEXT WRAPPING ********\n\n**D. FUNCIÓN \\`update(sourceNode)\\` (VITAL PARA INTERACTIVIDAD):**\n   Esta función será la responsable de renderizar/actualizar el árbol cada vez que se expanda/colapse un nodo.\n   \\`sourceNode\\` es el nodo que fue clickeado.\n\n1.  **Calcular Nuevo Layout:**\n    *   \\`const treeData = treeLayout(root);\\`.\n    *   \\`const nodes = treeData.descendants();\\`.\n    *   \\`const links = treeData.links();\\`.\n    *   **Orientación (Ajustar Coordenadas):** Asegúrate de que después del layout, los nodos se posicionen horizontalmente. \\`nodes.forEach(d => { d.y = d.depth * nodeHorizontalSeparation; });\\` (Si \\`nodeSize\\` no lo hace directamente, o si quieres controlar la separación de niveles manualmente).\n    *   **VALIDACIÓN CRÍTICA:** Asegúrate de que todas las coordenadas sean números válidos:\n        \\`nodes.forEach(d => {\n          d.x = isNaN(d.x) ? 0 : d.x;\n          d.y = isNaN(d.y) ? d.depth * nodeHorizontalSeparation : d.y;\n          d.x0 = d.x0 || d.x;\n          d.y0 = d.y0 || d.y;\n        });\\`\n\n2.  **NODOS:**\n    * // **OBJETIVOS ADICIONALES PARA NODOS:**\n        *INDICADOR DE EXPANSIÓN:** Los nodos con hijos (visibles u ocultos en _children) deben mostrar un pequeño círculo a su derecha. Este círculo contendrá un texto \"+\" si el nodo está colapsado y tiene _children, o \"-\" si está expandido y tiene children. El círculo debe ser visible solo si hay hijos/_children. El color del círculo puede cambiar para reflejar el estado (ej. naranja para colapsado, azul para expandido).\n        *CENTRADO VERTICAL DEL TEXTO:** El texto de varias líneas dentro de cada nodo rectangular debe estar lo más centrado verticalmente posible. Ajusta el atributo 'y' del elemento <text> y/o el 'dy' del primer <tspan> en la función wrapText para lograrlo, considerando la computedTextHeight y lineHeight.\n        *ANCHO DE NODO DINÁMICO:** El ancho de los rectángulos de los nodos (d.rectWidth) debe ajustarse al ancho real del texto envuelto (con un mínimo y máximo), en lugar de usar siempre maxNodeTextWidth. La función wrapText debe ayudar a determinar este ancho real.\n    *   **NOTA IMPORTANTE SOBRE EL COLOR DE NODOS:** El color de fondo (fill) de los rectángulos de los nodos se definirá exclusivamente a través de las clases CSS .node.depth-X especificadas en la Sección I. Por lo tanto, NO se debe establecer un estilo fill en línea en JavaScript para los elementos rect (ni en nodeEnter ni en nodeUpdate). El JavaScript solo se encarga de la estructura y los atributos como width, height, stroke, pero el fill principal vendrá del CSS.\n    *   Selección: \\`const node = g.selectAll(\"g.node\").data(nodes, d => d.data.id);\\`.\n    *   **Nodos Entrantes (\\`nodeEnter\\`):**\n        *   Crea el grupo principal del nodo:\n            \\`const nodeEnter = node.enter().append(\"g\")\n                .attr(\"class\", d => \"node depth-\" + d.depth) // Añade clase de profundidad\n                .attr(\"transform\", d => \\`translate(\\${sourceNode.y0 || 0},\\${sourceNode.x0 || 0})\\`) // Posición inicial validada\n                .on(\"click\", handleClick);\\`\n        *   **Cálculo de Dimensiones, Text Wrapping y Creación de Elementos Internos (Rect y Text):**\n            \\`nodeEnter.each(function(d) {\n                const nodeGroup = d3.select(this);\n                const horizontalPadding = 12;\n                const verticalPadding = 8;\n                const maxNodeTextWidth = 150;\n                const lineHeight = 12; // Asumiendo font-size 12px, so 1.2em = 12px\n\n                d.rectWidth = maxNodeTextWidth + 2 * horizontalPadding;\n\n                // Añade el rectángulo primero (visualmente detrás del texto)\n                const rectElement = nodeGroup.append(\"rect\")\n                    .attr(\"rx\", \"3\")\n                    .attr(\"ry\", \"3\")\n                    .style(\"stroke-width\", \"1px\")\n                    .style(\"stroke\", \"#777\");\n\n                // Añade el elemento de texto\n                const textElement = nodeGroup.append(\"text\")\n                    .attr(\"text-anchor\", \"middle\")\n                    .style(\"font-size\", \"10px\") \n                    .style(\"fill\", \"#333\")\n                    .text(d.data.name); // Nombre del nodo\n\n                // Aplica text wrapping y obtén la altura calculada del texto\n                const computedTextHeight = wrapText(textElement, maxNodeTextWidth, lineHeight);\n                \n                // Calcula la altura final del rectángulo\n                d.rectHeight = Math.max(computedTextHeight + 2 * verticalPadding, 30); // Altura mínima de 30px\n\n                // Ajusta la posición Y del elemento de texto para centrarlo verticalmente\n                // El primer tspan dentro de wrapText tendrá un dy de 'lineHeight'\n                // por lo que el 'y' del textElement debe ser la parte superior del área de texto.\n                const textBlockYOffset = -d.rectHeight / 2 + verticalPadding;\n                textElement.attr(\"y\", textBlockYOffset);\n\n                // Ahora establece las dimensiones y posición del rectángulo\n                rectElement\n                    .attr(\"width\", d.rectWidth)\n                    .attr(\"height\", d.rectHeight)\n                    .attr(\"x\", -d.rectWidth / 2)\n                    .attr(\"y\", -d.rectHeight / 2);\n            });\\`\n    *   **Nodos Actualizados (\\`nodeUpdate\\`):**\n        *   **VALIDACIÓN DE COORDENADAS:** \\`const validX = isNaN(d.x) ? 0 : d.x; const validY = isNaN(d.y) ? 0 : d.y;\\`\n        *   Transición a la nueva posición: \\`node.merge(nodeEnter).transition().duration(duration).attr(\"transform\", d => \\`translate(\\${isNaN(d.y) ? 0 : d.y},\\${isNaN(d.x) ? 0 : d.x})\\`);\\`.\n     *   **Actualizar atributos del rectángulo (CRÍTICO: seleccionar el 'rect' correctamente):**\n            \\`node.merge(nodeEnter).each(function(d) {\n                const currentRect = d3.select(this).select(\"rect\"); // Selecciona el rect dentro del grupo 'this'\n                if (currentRect.node()) { // Asegura que el rect exista\n                    currentRect.transition().duration(duration) // Añadir transición también al cambio de color\n                    // Si necesitaras actualizar width/height aquí, también deberían transicionar:\n                     currentRect.transition().duration(duration)\n                        .attr(\"width\", d.rectWidth)\n                        .attr(\"height\", d.rectHeight);\n                } else {\n                    console.warn(\"Rectángulo no encontrado para actualizar en nodo:\", d.data.name);\n                }\n            });\\`    *   **Nodos Salientes (\\`nodeExit\\`):**\n        *   **VALIDACIÓN DE POSICIÓN FINAL:** \\`const finalX = isNaN(sourceNode.x) ? 0 : sourceNode.x; const finalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;\\`\n        *   Transición a la posición del nodo padre: \\`nodeExit.transition().duration(duration).attr(\"transform\", \\`translate(\\${finalY},\\${finalX})\\`).remove();\\`.\n        *   Reduce la opacidad del rectángulo y texto a 0.\n\n3.  **ENLACES:**\n    *   Selección: \\`const link = g.selectAll(\"path.link\").data(links, d => d.target.data.id);\\`.\n    *   **Enlaces Entrantes (\\`linkEnter\\`):**\n        *   Añade \\`<path class=\"link\">\\`.\n        *   **VALIDACIÓN DE POSICIÓN INICIAL:**\n            \\`const sourceInitialX = isNaN(sourceNode.x0) ? 0 : sourceNode.x0;\n             const sourceInitialY = isNaN(sourceNode.y0) ? 0 : sourceNode.y0;\n             const sourceInitialWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\\`\n        *   Posición inicial desde el padre: \\`linkEnter.insert(\"path\", \"g\").attr(\"class\", \"link\").attr(\"d\", d => { const o = {x: sourceInitialX, y: sourceInitialY, rectWidth: sourceInitialWidth }; return diagonal({source: o, target: o}); }).style(\"fill\", \"none\").style(\"stroke\", \"#ccc\").style(\"stroke-width\", \"1.5px\");\\`\n    *   **Enlaces Actualizados (\\`linkUpdate\\`):**\n        *   Transición a la nueva posición: \\`link.merge(linkEnter).transition().duration(duration).attr(\"d\", diagonal);\\`.\n    *   **Enlaces Salientes (\\`linkExit\\`):**\n        *   **VALIDACIÓN DE POSICIÓN FINAL:**\n            \\`const sourceFinalX = isNaN(sourceNode.x) ? 0 : sourceNode.x;\n             const sourceFinalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;\n             const sourceFinalWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\\`\n        *   Transición a la posición del padre y remove: \\`linkExit.transition().duration(duration).attr(\"d\", d => { const o = {x: sourceFinalX, y: sourceFinalY, rectWidth: sourceFinalWidth }; return diagonal({source: o, target: o}); }).remove();\\`.\n\n4.  **Guardar Posiciones Antiguas:**\n    *   Al final de \\`update\\`: \\`nodes.forEach(d => { d.x0 = d.x; d.y0 = d.y; });\\`.\n\n**E. FUNCIÓN \\`diagonal(linkObject)\\` (PARA DIBUJAR ENLACES A BORDES DE RECTÁNGULOS):**\n   Debe generar un path string para el atributo \\`d\\` del path.\n   \\`\\`\\`javascript\n   function diagonal({ source, target }) {\n     // source y target son nodos con propiedades x, y, rectWidth\n     // VALIDACIÓN CRÍTICA: Asegurar que todos los valores sean números válidos\n     const sourceX = isNaN(source.x) ? 0 : source.x;\n     const sourceY = isNaN(source.y) ? 0 : source.y;\n     const targetX = isNaN(target.x) ? 0 : target.x;\n     const targetY = isNaN(target.y) ? 0 : target.y;\n     const sourceWidth = isNaN(source.rectWidth) ? 20 : (source.rectWidth || 20);\n     const targetWidth = isNaN(target.rectWidth) ? 20 : (target.rectWidth || 20);\n\n     const sx = sourceY + sourceWidth / 2;\n     const sy = sourceX;\n     const tx = targetY - targetWidth / 2;\n     const ty = targetX;\n\n     // Validar que los puntos calculados sean números válidos\n     const validSx = isNaN(sx) ? 0 : sx;\n     const validSy = isNaN(sy) ? 0 : sy;\n     const validTx = isNaN(tx) ? 0 : tx;\n     const validTy = isNaN(ty) ? 0 : ty;\n\n     // Path curvado simple\n     return \\`M \\${validSx} \\${validSy}\n             C \\${(validSx + validTx) / 2} \\${validSy},\n               \\${(validSx + validTx) / 2} \\${validTy},\n               \\${validTx} \\${validTy}\\`;\n   }\n   \\`\\`\\`\n\n**F. FUNCIÓN \\`handleClick(event, d)\\` (MANEJADOR DE CLIC EN NODO):**\n   \\`\\`\\`javascript\n   function handleClick(event, d) {\n     if (d.children) { // Si está expandido, colapsar\n       d._children = d.children;\n       d.children = null;\n     } else if (d._children) { // Si está colapsado y tiene hijos ocultos, expandir\n       d.children = d._children;\n       d._children = null;\n     }\n     // Si es un nodo hoja (sin d.children ni d._children), no hacer nada o una acción específica.\n     // Para este caso, solo expandir/colapsar.\n     update(d); // Llama a update con el nodo clickeado como 'sourceNode'\n   }\n   \\`\\`\\`\n\n**G. VISUALIZACIÓN INICIAL Y ZOOM/PAN:**\n1.  Llama a \\`update(root);\\` para el primer renderizado.\n2.  **Cálculo de Extensiones y Escala Inicial (Adaptar del prompt anterior):**\n    *   NECESITAS calcular las dimensiones del árbol DESPUÉS de que el layout inicial (\\`update(root)\\`) haya asignado \\`rectWidth\\` y \\`rectHeight\\` a los nodos visibles.\n    *   Obtén minX, maxX, minYActual, maxYActual de los nodos en root.descendants() que no estén colapsados (o de todos para un cálculo más simple que puede ser ajustado por el zoom).\n    *   Considera el \\`rectWidth/2\\` y \\`rectHeight/2\\` para los bordes.\n3.  **Traslación y Escala:**\n    *   Calcula \\`initialScale\\`, \\`initialTranslateX\\`, \\`initialTranslateY\\` como en el prompt anterior, pero usando el \\`<g class=\"main-group\">\\` para el zoom.\n    *   \\`const zoom = d3.zoom().scaleExtent([0.1, 3]).on(\"zoom\", (event) => mainGroup.attr(\"transform\", event.transform));\\`\n    *   \\`svg.call(zoom);\\`.\n    *   \\`svg.call(zoom.transform, d3.zoomIdentity.translate(initialTranslateX, initialTranslateY).scale(initialScale));\\`.\n\n**H. MANEJO DE REDIMENSIONAMIENTO DE VENTANA (Como en el prompt anterior):**\n    *   Reajusta el SVG y recalcula la transformación de zoom/pan para centrar.\n\n**I. ESTILO CSS:**\n   \\`\\`\\`css\n   .node text { font: 10px sans-serif; pointer-events: none; }\n   .link { fill: none; stroke: #ccc; stroke-width: 1.5px; }\n   .node rect { cursor: pointer; }\n   .node rect:hover { stroke-opacity: 1; stroke-width: 2px; }\n   /* Colores por profundidad (opcional) */\n   .node.depth-0 rect { fill: #d1e5f0; stroke: #67a9cf; }\n   .node.depth-1 rect { fill: #fddbc7; stroke: #ef8a62; }\n   .node.depth-2 rect { fill: #e0f3f8; stroke: #92c5de; }\n   .node.depth-3 rect { fill: #f7f7f7; stroke: #bababa; }\n   \\`\\`\\`\n   Asegúrate de añadir la clase de profundidad al grupo del nodo:\n   \\`nodeEnter.attr(\"class\", d => \"node depth-\" + d.depth)\\`\n\n**J. REVISIÓN FINAL ANTES DE GENERAR (PARA LA IA):**\n*   ¿La variable zoom (que contiene d3.zoom()) se define e inicializa ANTES de que cualquier función (como centerAndFitView o la lógica de transformación inicial) intente usarla para llamar a zoom.transform?\n*   ¿Se usa una función \\`update(sourceNode)\\` para manejar todas las actualizaciones del DOM? SÍ.\n*   ¿La función \\`handleClick\\` alterna entre \\`d.children\\` y \\`d._children\\` y luego llama a \\`update(d)\\`? SÍ.\n*   ¿Los nodos y enlaces entrantes aparecen desde la posición del padre (\\`sourceNode\\`)? SÍ.\n*   ¿Los nodos y enlaces salientes se mueven hacia la posición del padre antes de eliminarse? SÍ.\n*   ¿Se usan transiciones D3 con una \\`duration\\` constante? SÍ.\n*   ¿Se almacenan y usan \\`x0\\`, \\`y0\\` para las posiciones iniciales/finales de las transiciones? SÍ.\n*   ¿La función \\`diagonal\\` calcula correctamente los puntos de inicio/fin en los bordes de los rectángulos? SÍ.\n*   ¿El cálculo dinámico de \\`rectWidth\\` y \\`rectHeight\\` se realiza para cada nodo al entrar? SÍ.\n\n**RESTRICCIONES IMPORTANTES:**\n-   Tu respuesta DEBE SER ÚNICAMENTE el código HTML completo. Sin explicaciones, comentarios introductorios o finales fuera del código.\n-   Sigue las instrucciones de D3.js al pie de la letra, especialmente el patrón Enter-Update-Exit dentro de la función \\`update\\`.\n-   **CRÍTICO:** SIEMPRE valida que las coordenadas y dimensiones sean números válidos usando \\`isNaN()\\` antes de usarlas en transformaciones SVG. Esto evita errores como \\`translate(NaN,NaN)\\` o \\`scale(NaN)\\`.\n-   **CRÍTICO:** Usa valores por defecto seguros (como 0, 20, 300) cuando los cálculos resulten en NaN o undefined.\n\n`;\n/**\n * Prompt para la generación de tests\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {cantidad}: Número de preguntas a generar\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_TESTS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de preguntas de test de opción múltiple (4 opciones, 1 correcta) basadas en el contenido proporcionado. Estas preguntas serán utilizadas por un estudiante para evaluar su comprensión del temario.\n\nCONTEXTO DEL TEMARIO (Información base para tus preguntas):\n{documentos}\n\nPETICIÓN DEL USUARIO:\nGenera {cantidad} preguntas de test de alta calidad.\nInstrucciones específicas del usuario: {instrucciones}\n\nINSTRUCCIONES PARA CREAR PREGUNTAS DE TEST:\n\n1.  Genera EXACTAMENTE la {cantidad}, que solicite el usuario, de preguntas de test de alta calidad.\n2.  BASA TODAS las preguntas y opciones de respuesta ESTRICTA y ÚNICAMENTE en la información proporcionada en el \"CONTEXTO DEL TEMARIO\".\n3.  ENFOCA cada pregunta según las \"Instrucciones específicas del usuario\" ({instrucciones}). Si las instrucciones piden centrarse en \"artículos, sus números y su contenido\", entonces CADA pregunta debe tratar directamente sobre:\n    a)  El número de un artículo específico y lo que establece.\n    b)  El contenido principal de un artículo específico, preguntando a qué artículo pertenece o detalles clave.\n    c)  La relación entre un concepto y el artículo que lo regula.\n    EVITA preguntas generales sobre historia, contexto de aprobación de leyes, o interpretaciones amplias a menos que las \"Instrucciones específicas del usuario\" ({instrucciones}) lo indiquen explícitamente.\n4.  Cada objeto de pregunta en el array JSON resultante debe tener las siguientes propiedades DIRECTAS:\n    -   \"pregunta\": (string) El texto de la pregunta.\n    -   \"opcion_a\": (string) El texto para la opción A.\n    -   \"opcion_b\": (string) El texto para la opción B.\n    -   \"opcion_c\": (string) El texto para la opción C.\n    -   \"opcion_d\": (string) El texto para la opción D.\n    -   \"respuesta_correcta\": (string) Debe ser 'a', 'b', 'c', o 'd', indicando cuál de las opciones es la correcta.\n    NO anides las opciones (opcion_a, opcion_b, etc.) dentro de otro objeto llamado \"opciones\". Deben ser propiedades directas del objeto de la pregunta.\n5.  Las preguntas deben ser claras, concisas y evaluar la comprensión de conceptos clave, detalles importantes, relaciones, etc., SIEMPRE dentro del enfoque solicitado en {instrucciones}.\n6.  Las opciones de respuesta deben ser plausibles y estar basadas en el contexto, pero solo una debe ser inequívocamente correcta según el temario proporcionado y el enfoque de la pregunta.\n7.  NO inventes información que no esté en el CONTEXTO.\n8.  Responde SIEMPRE en español.\n\nFORMATO DE RESPUESTA:\nDebes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una pregunta con las propiedades directas \"pregunta\", \"opcion_a\", \"opcion_b\", \"opcion_c\", \"opcion_d\" y \"respuesta_correcta\". Ejemplo:\n\n[\n  {\n    \"pregunta\": \"¿Qué establece el Artículo X de la Ley Y sobre Z?\",\n    \"opcion_a\": \"Opción A relacionada con el artículo X\",\n    \"opcion_b\": \"Opción B relacionada con el artículo X (correcta)\",\n    \"opcion_c\": \"Opción C relacionada con el artículo X\",\n    \"opcion_d\": \"Opción D relacionada con el artículo X\",\n    \"respuesta_correcta\": \"b\"\n  },\n  {\n    \"pregunta\": \"El concepto de [concepto clave] se regula principalmente en el artículo:\",\n    \"opcion_a\": \"Artículo A\",\n    \"opcion_b\": \"Artículo B\",\n    \"opcion_c\": \"Artículo C (correcta)\",\n    \"opcion_d\": \"Artículo D\",\n    \"respuesta_correcta\": \"c\"\n  }\n]\n\nIMPORTANTE: Tu respuesta debe contener ÚNICAMENTE el array JSON, sin texto adicional antes o después. No incluyas marcadores de código ni la palabra json antes del array.\n`;\n/**\n * Prompt para la generación de planes de estudio personalizados\n *\n * Variables disponibles:\n * - {informacionUsuario}: Información completa del usuario (planificación, temas, estimaciones)\n */ const PROMPT_PLAN_ESTUDIOS = `Eres \"Mentor Opositor AI\", un preparador de oposiciones virtual excepcionalmente experimentado, organizado, empático y con una metodología de estudio probada. Tu misión es crear una propuesta de plan de estudio inicial, altamente personalizada y realista para el opositor, basándote en la información que te proporcionará y los principios de una preparación de oposiciones de élite.\n\n**Información Clave Recopilada del Opositor:**\n\n{informacionUsuario}\n\n**Principios Fundamentales para la Creación de tu Propuesta de Plan de Estudio:**\n\nDebes internalizar y aplicar los siguientes principios como si fueras un preparador humano experto:\n\n1. **Organización y Realismo Absoluto:**\n   - Calcula el tiempo total disponible hasta el examen, considerando la disponibilidad diaria REAL del opositor.\n   - Compara este tiempo con la suma de las estimaciones de horas por tema (ajustadas por dificultad/importancia).\n   - **Si el tiempo es claramente insuficiente para cubrir todo el temario de forma realista, indícalo con honestidad al inicio de tu propuesta de plan**, sugiriendo posibles enfoques (priorizar, extender el plazo si es posible, etc.). No crees un plan imposible.\n   - Distribuye el temario de forma lógica a lo largo del tiempo, dejando márgenes para imprevistos.\n\n2. **Metodología Probada y Adaptable (Tu Enfoque):**\n   - **Estimación de Tiempo por Tema:** Si el usuario no proporcionó una estimación para un tema, usa tu \"experiencia\" para asignar una base (ej. temas cortos 2-3h, medios 5-7h, largos/densos 8-12h). Ajusta esto según la \"familiaridad general\" y las \"característicasUsuario\" (dificultad, importancia). Los temas difíciles o muy importantes deben tener más tiempo asignado.\n   - **Orden de Estudio:** Generalmente, sugiere empezar por los temas marcados como \"difíciles\" o \"muy importantes\". Puedes proponer alternar temas densos con otros más ligeros para mantener la motivación.\n   - **Bloques Temáticos:** Si identificas temas muy relacionados entre sí en el índice, considera agruparlos en bloques de estudio.\n   - **Repasos Sistemáticos:**\n     - **Post-Tema:** Incluye un breve repaso al finalizar cada tema.\n     - **Periódicos:** Integra repasos acumulativos (ej. semanales o quincenales, según la frecuenciaRepasoDeseada si el usuario la especificó, o tu recomendación experta). Una buena regla general es dedicar ~30% del tiempo total de estudio a repasos.\n     - **Fase Final de Repaso:** Reserva un periodo significativo antes del examen (ej. las últimas 3-6 semanas, dependiendo del tiempo total) EXCLUSIVAMENTE para repasos generales, simulacros y consolidación. No se debe introducir material nuevo aquí.\n   - **Metas Claras:** Define metas semanales y/o mensuales (ej. \"Semana 1: Completar Tema 1 y Tema 2 (hasta apartado X). Realizar 20 flashcards de Tema 1.\").\n\n3. **Experiencia y Conocimiento (Tu Rol):**\n   - Al presentar el plan, puedes añadir breves comentarios estratégicos, como: \"Dado que el Tema X es fundamental y lo has marcado como difícil, le hemos asignado más tiempo y lo abordaremos pronto para tener margen de repaso\".\n   - Si el usuario no marcó temas como importantes, usa tu \"experiencia\" para identificar temas que suelen ser cruciales en oposiciones similares (si el contexto del temario te da alguna pista, si no, sé general).\n\n4. **Flexibilidad (Implícita en la Propuesta):**\n   - Aunque generes un plan estructurado, en tu introducción al plan puedes mencionar que es una \"propuesta inicial\" y que se podrá ajustar según el progreso real.\n\n**Formato de Salida de la Propuesta del Plan:**\n\nGenera una respuesta en **texto claro y estructurado (Markdown es ideal)** que el opositor pueda entender fácilmente. Considera las siguientes secciones:\n\n1. **Introducción y Evaluación Inicial de Viabilidad:**\n   - Un saludo y una breve valoración de la situación.\n   - Si el plan es muy ajustado o potencialmente irrealizable, **menciónalo aquí con tacto y sugiere alternativas**.\n\n2. **Resumen del Plan:**\n   - Tiempo total de estudio estimado.\n   - Número de temas a cubrir.\n   - Duración de la fase de estudio de nuevo material.\n   - Duración de la fase de repaso final.\n\n3. **Cronograma Semanal Detallado:**\n   - Para cada semana:\n     - **Semana X (Fechas: DD/MM/YY - DD/MM/YY)**\n     - **Objetivo Principal de la Semana:**\n     - **Distribución Sugerida por Días:**\n       - **Lunes ([X]h):** Actividades específicas.\n       - **Martes ([X]h):** Actividades específicas.\n       - etc.\n\n4. **Estrategia de Repasos:**\n   - Explica brevemente cómo se integrarán los repasos.\n\n5. **Próximos Pasos y Consejos:**\n   - Consejos motivacionales y de seguimiento.\n\n**Consideraciones para la IA al Generar la Respuesta:**\n\n- **Lenguaje:** Empático, profesional, claro y motivador.\n- **Personalización:** Usa la información del usuario para que el plan se sienta realmente adaptado a él/ella.\n- **Realismo:** Evita sobrecargar las semanas. Es mejor un progreso constante que picos de trabajo insostenibles.\n- **Accionable:** El plan debe darle al usuario una idea clara de qué hacer cada semana/día.\n\nGenera el plan de estudios personalizado basándote en toda esta información.`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/prompts.ts\n");

/***/ }),

/***/ "(rsc)/./src/features/auth/services/authService.ts":
/*!***************************************************!*\
  !*** ./src/features/auth/services/authService.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n\n/**\n * Inicia sesión con email y contraseña\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            error: errorMessage\n        };\n    }\n}\n/**\n * Cierra la sesión del usuario actual\n */ async function cerrarSesion() {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            return {\n                error: error.message\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\n * Obtiene la sesión actual del usuario\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\n * Obtiene el usuario actual\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\n * Verifica si el usuario está autenticado\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/auth/services/authService.ts\n");

/***/ }),

/***/ "(rsc)/./src/features/planificacion/services/planGeneratorService.ts":
/*!*********************************************************************!*\
  !*** ./src/features/planificacion/services/planGeneratorService.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarPlanEstudios: () => (/* binding */ generarPlanEstudios)\n/* harmony export */ });\n/* harmony import */ var _lib_gemini_geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/gemini/geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _planificacionService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./planificacionService */ \"(rsc)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(rsc)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n\n\n/**\n * Genera un plan de estudios personalizado usando IA\n */ async function generarPlanEstudios(temarioId) {\n    try {\n        // Obtener datos de planificación del usuario\n        const planificacion = await (0,_planificacionService__WEBPACK_IMPORTED_MODULE_1__.obtenerPlanificacionUsuario)(temarioId);\n        if (!planificacion) {\n            throw new Error('No se encontró planificación configurada para este temario');\n        }\n        // Obtener estimaciones de temas\n        const estimaciones = await (0,_planificacionService__WEBPACK_IMPORTED_MODULE_1__.obtenerEstimacionesTemas)(planificacion.id);\n        // Obtener temas del temario\n        const temas = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemas)(temarioId);\n        // Combinar datos de temas con estimaciones\n        const temasConEstimaciones = temas.map((tema)=>{\n            const estimacion = estimaciones.find((est)=>est.tema_id === tema.id);\n            return {\n                id: tema.id,\n                numero: tema.numero,\n                titulo: tema.titulo,\n                descripcion: tema.descripcion,\n                horasEstimadas: estimacion?.horas_estimadas || 0,\n                esDificil: estimacion?.es_dificil || false,\n                esMuyImportante: estimacion?.es_muy_importante || false,\n                yaDominado: estimacion?.ya_dominado || false,\n                notas: estimacion?.notas || ''\n            };\n        });\n        // Preparar información del usuario para el prompt\n        const informacionUsuario = prepararInformacionUsuario(planificacion, temasConEstimaciones);\n        // Construir el prompt final\n        const promptFinal = _config_prompts__WEBPACK_IMPORTED_MODULE_3__.PROMPT_PLAN_ESTUDIOS.replace('{informacionUsuario}', informacionUsuario);\n        // Generar el plan con Gemini\n        const result = await _lib_gemini_geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(promptFinal);\n        const response = result.response.text();\n        if (!response || response.trim().length === 0) {\n            throw new Error('La IA no generó ningún contenido para el plan de estudios');\n        }\n        return response;\n    } catch (error) {\n        console.error('Error al generar plan de estudios:', error);\n        throw error;\n    }\n}\n/**\n * Prepara la información del usuario en formato legible para la IA\n */ function prepararInformacionUsuario(planificacion, temas) {\n    let info = '';\n    // Disponibilidad de tiempo\n    if (planificacion.tiempo_por_dia && Object.keys(planificacion.tiempo_por_dia).length > 0) {\n        info += '**Disponibilidad de Tiempo Diario:**\\n';\n        const dias = [\n            'lunes',\n            'martes',\n            'miercoles',\n            'jueves',\n            'viernes',\n            'sabado',\n            'domingo'\n        ];\n        dias.forEach((dia)=>{\n            const horas = planificacion.tiempo_por_dia[dia];\n            if (horas) {\n                info += `- ${dia.charAt(0).toUpperCase() + dia.slice(1)}: ${horas}h\\n`;\n            }\n        });\n    } else if (planificacion.tiempo_diario_promedio) {\n        info += `**Disponibilidad de Tiempo Diario:** Promedio ${planificacion.tiempo_diario_promedio}h/día\\n`;\n    }\n    // Fecha del examen\n    if (planificacion.fecha_examen) {\n        info += `\\n**Fecha del Examen:** ${planificacion.fecha_examen}\\n`;\n    } else if (planificacion.fecha_examen_aproximada) {\n        info += `\\n**Fecha del Examen (aproximada):** ${planificacion.fecha_examen_aproximada}\\n`;\n    }\n    // Familiaridad general\n    if (planificacion.familiaridad_general) {\n        info += `\\n**Familiaridad General con el Temario (1-5):** ${planificacion.familiaridad_general}\\n`;\n    }\n    // Preferencias de horario\n    if (planificacion.preferencias_horario && planificacion.preferencias_horario.length > 0) {\n        info += `\\n**Preferencias de Horario:** ${planificacion.preferencias_horario.join(', ')}\\n`;\n    }\n    // Frecuencia de repasos\n    if (planificacion.frecuencia_repasos) {\n        info += `\\n**Frecuencia de Repasos Deseada:** ${planificacion.frecuencia_repasos}\\n`;\n    }\n    // Índice del temario\n    info += '\\n**Índice del Temario del Opositor:**\\n';\n    temas.forEach((tema)=>{\n        const caracteristicas = [];\n        if (tema.esDificil) caracteristicas.push('dificil');\n        if (tema.esMuyImportante) caracteristicas.push('muy_importante');\n        if (tema.yaDominado) caracteristicas.push('ya_dominado');\n        info += `- **Tema ${tema.numero}: ${tema.titulo}**\\n`;\n        if (tema.descripcion) {\n            info += `  - Descripción: ${tema.descripcion}\\n`;\n        }\n        if (tema.horasEstimadas && tema.horasEstimadas > 0) {\n            info += `  - Estimación de horas: ${tema.horasEstimadas}h\\n`;\n        }\n        if (caracteristicas.length > 0) {\n            info += `  - Características: ${caracteristicas.join(', ')}\\n`;\n        }\n        if (tema.notas) {\n            info += `  - Notas: ${tema.notas}\\n`;\n        }\n        info += '\\n';\n    });\n    return info;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/planificacion/services/planGeneratorService.ts\n");

/***/ }),

/***/ "(rsc)/./src/features/planificacion/services/planificacionService.ts":
/*!*********************************************************************!*\
  !*** ./src/features/planificacion/services/planificacionService.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eliminarPlanificacion: () => (/* binding */ eliminarPlanificacion),\n/* harmony export */   guardarEstimacionesTemas: () => (/* binding */ guardarEstimacionesTemas),\n/* harmony export */   guardarPlanificacionUsuario: () => (/* binding */ guardarPlanificacionUsuario),\n/* harmony export */   obtenerEstimacionesTemas: () => (/* binding */ obtenerEstimacionesTemas),\n/* harmony export */   obtenerPlanificacionUsuario: () => (/* binding */ obtenerPlanificacionUsuario),\n/* harmony export */   tienePlanificacionConfigurada: () => (/* binding */ tienePlanificacionConfigurada)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(rsc)/./src/features/auth/services/authService.ts\");\n\n\n/**\n * Verifica si el usuario tiene una planificación configurada\n */ async function tienePlanificacionConfigurada(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.log('No hay usuario autenticado o error:', authError);\n            return false;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planificacion_usuario').select('id').eq('user_id', user.id).eq('temario_id', temarioId).eq('completado', true).limit(1);\n        if (error) {\n            console.error('Error al verificar planificación:', error);\n            return false;\n        }\n        return data && data.length > 0;\n    } catch (error) {\n        console.error('Error al verificar planificación:', error);\n        return false;\n    }\n}\n/**\n * Obtiene la planificación del usuario para un temario\n */ async function obtenerPlanificacionUsuario(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planificacion_usuario').select('*').eq('user_id', user.id).eq('temario_id', temarioId).single();\n        if (error) {\n            if (error.code === 'PGRST116') {\n                return null; // No hay planificación\n            }\n            console.error('Error al obtener planificación:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener planificación:', error);\n        return null;\n    }\n}\n/**\n * Crea o actualiza la planificación del usuario\n */ async function guardarPlanificacionUsuario(temarioId, planificacion) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        // Verificar si ya existe una planificación\n        const planificacionExistente = await obtenerPlanificacionUsuario(temarioId);\n        if (planificacionExistente) {\n            // Actualizar planificación existente\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planificacion_usuario').update({\n                ...planificacion,\n                completado: true,\n                actualizado_en: new Date().toISOString()\n            }).eq('id', planificacionExistente.id).select().single();\n            if (error) {\n                console.error('Error al actualizar planificación:', error);\n                return null;\n            }\n            return data.id;\n        } else {\n            // Crear nueva planificación\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planificacion_usuario').insert([\n                {\n                    user_id: user.id,\n                    temario_id: temarioId,\n                    ...planificacion,\n                    completado: true\n                }\n            ]).select().single();\n            if (error) {\n                console.error('Error al crear planificación:', error);\n                return null;\n            }\n            return data.id;\n        }\n    } catch (error) {\n        console.error('Error al guardar planificación:', error);\n        return null;\n    }\n}\n/**\n * Obtiene las estimaciones de temas para una planificación\n */ async function obtenerEstimacionesTemas(planificacionId) {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estimaciones_temas').select('*').eq('planificacion_id', planificacionId);\n        if (error) {\n            console.error('Error al obtener estimaciones de temas:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener estimaciones de temas:', error);\n        return [];\n    }\n}\n/**\n * Guarda las estimaciones de temas\n */ async function guardarEstimacionesTemas(planificacionId, estimaciones) {\n    try {\n        // Eliminar estimaciones existentes\n        await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estimaciones_temas').delete().eq('planificacion_id', planificacionId);\n        // Insertar nuevas estimaciones\n        const estimacionesConPlanificacion = estimaciones.map((est)=>({\n                ...est,\n                planificacion_id: planificacionId\n            }));\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estimaciones_temas').insert(estimacionesConPlanificacion);\n        if (error) {\n            console.error('Error al guardar estimaciones de temas:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al guardar estimaciones de temas:', error);\n        return false;\n    }\n}\n/**\n * Elimina una planificación y todas sus estimaciones\n */ async function eliminarPlanificacion(planificacionId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planificacion_usuario').delete().eq('id', planificacionId);\n        if (error) {\n            console.error('Error al eliminar planificación:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar planificación:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/planificacion/services/planificacionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/features/temario/services/temarioService.ts":
/*!*********************************************************!*\
  !*** ./src/features/temario/services/temarioService.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarEstadoTema: () => (/* binding */ actualizarEstadoTema),\n/* harmony export */   actualizarTema: () => (/* binding */ actualizarTema),\n/* harmony export */   actualizarTemario: () => (/* binding */ actualizarTemario),\n/* harmony export */   crearTemario: () => (/* binding */ crearTemario),\n/* harmony export */   crearTemas: () => (/* binding */ crearTemas),\n/* harmony export */   eliminarTema: () => (/* binding */ eliminarTema),\n/* harmony export */   eliminarTemario: () => (/* binding */ eliminarTemario),\n/* harmony export */   obtenerEstadisticasTemario: () => (/* binding */ obtenerEstadisticasTemario),\n/* harmony export */   obtenerTemarioUsuario: () => (/* binding */ obtenerTemarioUsuario),\n/* harmony export */   obtenerTemas: () => (/* binding */ obtenerTemas),\n/* harmony export */   tieneTemarioConfigurado: () => (/* binding */ tieneTemarioConfigurado)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(rsc)/./src/features/auth/services/authService.ts\");\n\n\n/**\n * Verifica si el usuario tiene un temario configurado\n */ async function tieneTemarioConfigurado() {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.log('No hay usuario autenticado o error:', authError);\n            return false;\n        }\n        console.log('Verificando temario para usuario:', user.id);\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').select('id').eq('user_id', user.id).limit(1);\n        if (error) {\n            console.error('Error al verificar temario en Supabase:', error);\n            // Si es un error de tabla no encontrada, devolver false sin error\n            if (error.code === 'PGRST116' || error.message?.includes('relation') || error.message?.includes('does not exist')) {\n                console.log('Tabla temarios no encontrada, devolviendo false');\n                return false;\n            }\n            return false;\n        }\n        const tieneTemario = data && data.length > 0;\n        console.log('Resultado verificación temario:', tieneTemario);\n        return tieneTemario;\n    } catch (error) {\n        console.error('Error general al verificar temario:', error);\n        return false;\n    }\n}\n/**\n * Obtiene el temario del usuario actual\n */ async function obtenerTemarioUsuario() {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.log('No hay usuario autenticado para obtener temario o error:', authError);\n            return null;\n        }\n        console.log('Obteniendo temario para usuario:', user.id);\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').select('*').eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener temario en Supabase:', error);\n            // Si no hay datos, es normal (usuario sin temario)\n            if (error.code === 'PGRST116') {\n                console.log('Usuario no tiene temario configurado');\n                return null;\n            }\n            return null;\n        }\n        console.log('Temario obtenido:', data);\n        return data;\n    } catch (error) {\n        console.error('Error general al obtener temario:', error);\n        return null;\n    }\n}\n/**\n * Crea un nuevo temario\n */ async function crearTemario(titulo, descripcion, tipo) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.error('No hay usuario autenticado o error:', authError);\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').insert([\n            {\n                titulo,\n                descripcion,\n                tipo,\n                user_id: user.id\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al crear temario:', error);\n            return null;\n        }\n        return data.id;\n    } catch (error) {\n        console.error('Error al crear temario:', error);\n        return null;\n    }\n}\n/**\n * Obtiene los temas de un temario\n */ async function obtenerTemas(temarioId) {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').select('*').eq('temario_id', temarioId).order('orden', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener temas:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener temas:', error);\n        return [];\n    }\n}\n/**\n * Crea múltiples temas para un temario\n */ async function crearTemas(temarioId, temas) {\n    try {\n        const temasConTemarioId = temas.map((tema)=>({\n                ...tema,\n                temario_id: temarioId\n            }));\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').insert(temasConTemarioId);\n        if (error) {\n            console.error('Error al crear temas:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al crear temas:', error);\n        return false;\n    }\n}\n/**\n * Actualiza los datos básicos de un temario\n */ async function actualizarTemario(temarioId, titulo, descripcion) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').update({\n            titulo,\n            descripcion,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', temarioId);\n        if (error) {\n            console.error('Error al actualizar temario:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar temario:', error);\n        return false;\n    }\n}\n/**\n * Actualiza los datos de un tema\n */ async function actualizarTema(temaId, titulo, descripcion) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').update({\n            titulo,\n            descripcion,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', temaId);\n        if (error) {\n            console.error('Error al actualizar tema:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar tema:', error);\n        return false;\n    }\n}\n/**\n * Elimina un tema\n */ async function eliminarTema(temaId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').delete().eq('id', temaId);\n        if (error) {\n            console.error('Error al eliminar tema:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar tema:', error);\n        return false;\n    }\n}\n/**\n * Actualiza el estado de completado de un tema\n */ async function actualizarEstadoTema(temaId, completado) {\n    try {\n        const updateData = {\n            completado,\n            actualizado_en: new Date().toISOString()\n        };\n        if (completado) {\n            updateData.fecha_completado = new Date().toISOString();\n        } else {\n            updateData.fecha_completado = null;\n        }\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').update(updateData).eq('id', temaId);\n        if (error) {\n            console.error('Error al actualizar estado del tema:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar estado del tema:', error);\n        return false;\n    }\n}\n/**\n * Obtiene estadísticas del temario\n */ async function obtenerEstadisticasTemario(temarioId) {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').select('completado').eq('temario_id', temarioId);\n        if (error) {\n            console.error('Error al obtener estadísticas del temario:', error);\n            return null;\n        }\n        const totalTemas = data.length;\n        const temasCompletados = data.filter((tema)=>tema.completado).length;\n        const porcentajeCompletado = totalTemas > 0 ? temasCompletados / totalTemas * 100 : 0;\n        return {\n            totalTemas,\n            temasCompletados,\n            porcentajeCompletado\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas del temario:', error);\n        return null;\n    }\n}\n/**\n * Elimina un temario y todos sus temas asociados\n */ async function eliminarTemario(temarioId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').delete().eq('id', temarioId);\n        if (error) {\n            console.error('Error al eliminar temario:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar temario:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/temario/services/temarioService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini.ts":
/*!***************************!*\
  !*** ./src/lib/gemini.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.genAI),\n/* harmony export */   generarFlashcards: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarFlashcards),\n/* harmony export */   generarMapaMental: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarMapaMental),\n/* harmony export */   generarTest: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarTest),\n/* harmony export */   model: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.model),\n/* harmony export */   obtenerRespuestaIA: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.obtenerRespuestaIA),\n/* harmony export */   prepararDocumentos: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _gemini_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gemini/index */ \"(rsc)/./src/lib/gemini/index.ts\");\n// Este archivo es un punto de entrada para mantener la compatibilidad con el código existente\n// Redirige todas las exportaciones a la nueva estructura modular\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dlbWluaS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQSw4RkFBOEY7QUFDOUYsaUVBQWlFO0FBRWxDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXGdlbWluaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFc3RlIGFyY2hpdm8gZXMgdW4gcHVudG8gZGUgZW50cmFkYSBwYXJhIG1hbnRlbmVyIGxhIGNvbXBhdGliaWxpZGFkIGNvbiBlbCBjw7NkaWdvIGV4aXN0ZW50ZVxuLy8gUmVkaXJpZ2UgdG9kYXMgbGFzIGV4cG9ydGFjaW9uZXMgYSBsYSBudWV2YSBlc3RydWN0dXJhIG1vZHVsYXJcblxuZXhwb3J0ICogZnJvbSBcIi4vZ2VtaW5pL2luZGV4XCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/flashcardGenerator.ts":
/*!**********************************************!*\
  !*** ./src/lib/gemini/flashcardGenerator.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarFlashcards: () => (/* binding */ generarFlashcards)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera flashcards a partir de los documentos\n */ async function generarFlashcards(documentos, cantidad = 10, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar flashcards.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_FLASHCARDS.replace('{documentos}', contenidoDocumentos).replace('{cantidad}', cantidad.toString());\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar las flashcards\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el JSON de la respuesta\n        const jsonMatch = response.match(/\\[\\s*\\{[\\s\\S]*\\}\\s*\\]/);\n        if (!jsonMatch) {\n            throw new Error(\"No se pudo extraer el formato JSON de la respuesta.\");\n        }\n        const flashcardsJson = jsonMatch[0];\n        const flashcards = JSON.parse(flashcardsJson);\n        // Validar el formato\n        if (!Array.isArray(flashcards) || flashcards.length === 0) {\n            throw new Error(\"El formato de las flashcards generadas no es válido.\");\n        }\n        return flashcards;\n    } catch (error) {\n        console.error('Error al generar flashcards:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/flashcardGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/geminiClient.ts":
/*!****************************************!*\
  !*** ./src/lib/gemini/geminiClient.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* binding */ genAI),\n/* harmony export */   model: () => (/* binding */ model),\n/* harmony export */   prepararDocumentos: () => (/* binding */ prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* binding */ truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Configuración de la API de Gemini\nconst API_KEY = process.env.GEMINI_API_KEY || '';\nconst MODEL_NAME = 'gemini-2.5-flash-preview-05-20';\n// Verificar que la API key esté configurada\nif (!API_KEY) {\n    console.error('GEMINI_API_KEY no está configurada en las variables de entorno');\n}\n// Inicializar el cliente de Gemini\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(API_KEY);\nconst model = genAI.getGenerativeModel({\n    model: MODEL_NAME,\n    safetySettings: [\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_HARASSMENT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_HATE_SPEECH,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        }\n    ]\n});\n// Ya no usamos instrucciones base globales, cada servicio usa su propio prompt personalizado\n/**\n * Trunca el contenido de un documento si es demasiado largo\n */ function truncarContenido(contenido, maxLength = 25000) {\n    // Verificar que el contenido sea una cadena válida\n    if (contenido === undefined || contenido === null) {\n        console.warn('Se intentó truncar un contenido undefined o null');\n        return '';\n    }\n    // Asegurarse de que el contenido sea una cadena\n    const contenidoStr = String(contenido);\n    if (contenidoStr.length <= maxLength) {\n        return contenidoStr;\n    }\n    return contenidoStr.substring(0, maxLength) + `\\n\\n[CONTENIDO TRUNCADO: El documento original es más largo. Esta es una versión reducida para procesamiento.]`;\n}\n// --- Nueva lógica de Chunking ---\nconst CHUNK_SIZE = 5000; // Caracteres\nconst CHUNK_OVERLAP = 200; // Caracteres\nconst MAX_TOTAL_CONTEXT_LENGTH = 50000; // Caracteres\nfunction createTextChunks(documentTitle, content) {\n    if (!content) {\n        return [];\n    }\n    const contentStr = String(content);\n    const chunks = [];\n    let chunkIndex = 0;\n    let currentIndex = 0;\n    while(currentIndex < contentStr.length){\n        const endIndex = Math.min(currentIndex + CHUNK_SIZE, contentStr.length);\n        const text = contentStr.substring(currentIndex, endIndex);\n        chunks.push({\n            originalDocumentTitle: documentTitle,\n            chunkIndex: chunkIndex + 1,\n            text\n        });\n        chunkIndex++;\n        if (endIndex === contentStr.length) {\n            break;\n        }\n        currentIndex += CHUNK_SIZE - CHUNK_OVERLAP;\n        // Asegurar que no haya un bucle infinito si CHUNK_OVERLAP >= CHUNK_SIZE\n        if (currentIndex >= endIndex && endIndex < contentStr.length) {\n            currentIndex = endIndex; // Avanzar al menos hasta el final del chunk actual\n        }\n    }\n    return chunks;\n}\n/**\n * Prepara los documentos para enviarlos al modelo, dividiéndolos en chunks.\n */ function prepararDocumentos(documentos) {\n    if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {\n        console.warn('No se proporcionaron documentos válidos para prepararDocumentos');\n        return '';\n    }\n    try {\n        const allChunks = [];\n        for (const doc of documentos){\n            if (!doc || typeof doc !== 'object' || !doc.titulo || doc.contenido === undefined || doc.contenido === null) {\n                console.warn('Documento inválido, sin título o contenido en prepararDocumentos:', doc);\n                continue;\n            }\n            const fullOriginalTitle = `${doc.categoria ? `[${doc.categoria}] ` : ''}${doc.numero_tema ? `Tema ${doc.numero_tema}: ` : ''}${doc.titulo}`;\n            const documentChunks = createTextChunks(fullOriginalTitle.trim(), doc.contenido);\n            allChunks.push(...documentChunks);\n        }\n        if (allChunks.length === 0) {\n            console.warn('No se generaron chunks a partir de los documentos proporcionados.');\n            return '';\n        }\n        let fullContext = allChunks.map((chunk)=>{\n            return `\n=== INICIO CHUNK: ${chunk.originalDocumentTitle} - Parte ${chunk.chunkIndex} ===\n${chunk.text}\n=== FIN CHUNK: ${chunk.originalDocumentTitle} - Parte ${chunk.chunkIndex} ===\n`;\n        }).join('\\n\\n');\n        if (fullContext.length > MAX_TOTAL_CONTEXT_LENGTH) {\n            console.warn(`El contexto combinado (${fullContext.length} caracteres) excede el máximo de ${MAX_TOTAL_CONTEXT_LENGTH}. Se truncará.`);\n            fullContext = fullContext.substring(0, MAX_TOTAL_CONTEXT_LENGTH) + `\\n\\n[CONTEXTO GENERAL TRUNCADO: El contenido combinado de todos los chunks excedió el límite máximo (${MAX_TOTAL_CONTEXT_LENGTH} caracteres).]`;\n        }\n        return fullContext;\n    } catch (error) {\n        console.error('Error al preparar documentos con chunks:', error);\n        return '';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/geminiClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/index.ts":
/*!*********************************!*\
  !*** ./src/lib/gemini/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.genAI),\n/* harmony export */   generarFlashcards: () => (/* binding */ generarFlashcards),\n/* harmony export */   generarMapaMental: () => (/* binding */ generarMapaMental),\n/* harmony export */   generarTest: () => (/* binding */ generarTest),\n/* harmony export */   model: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model),\n/* harmony export */   obtenerRespuestaIA: () => (/* reexport safe */ _questionService__WEBPACK_IMPORTED_MODULE_1__.obtenerRespuestaIA),\n/* harmony export */   prepararDocumentos: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _questionService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./questionService */ \"(rsc)/./src/lib/gemini/questionService.ts\");\n/* harmony import */ var _flashcardGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./flashcardGenerator */ \"(rsc)/./src/lib/gemini/flashcardGenerator.ts\");\n/* harmony import */ var _testGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./testGenerator */ \"(rsc)/./src/lib/gemini/testGenerator.ts\");\n/* harmony import */ var _mindMapGenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mindMapGenerator */ \"(rsc)/./src/lib/gemini/mindMapGenerator.ts\");\n// Exportar todo desde los archivos individuales\n\n\n\n\n\n// Función adaptadora para compatibilidad con la interfaz anterior de flashcards\nasync function generarFlashcards(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./flashcardGenerator */ \"(rsc)/./src/lib/gemini/flashcardGenerator.ts\")).then((module)=>module.generarFlashcards(documentos, 10, peticion));\n}\n// Función adaptadora para compatibilidad con la interfaz anterior de mapas mentales\nasync function generarMapaMental(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./mindMapGenerator */ \"(rsc)/./src/lib/gemini/mindMapGenerator.ts\")).then((module)=>module.generarMapaMental(documentos, peticion));\n}\n// Función adaptadora para compatibilidad con la interfaz anterior de tests\nasync function generarTest(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    const result = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./testGenerator */ \"(rsc)/./src/lib/gemini/testGenerator.ts\")).then((module)=>module.generarTest(documentos, 10, peticion));\n    // Convertir el formato de la respuesta al formato esperado por el componente\n    return result.map((item)=>({\n            pregunta: item.pregunta,\n            opciones: {\n                a: item.opcion_a,\n                b: item.opcion_b,\n                c: item.opcion_c,\n                d: item.opcion_d\n            },\n            respuesta_correcta: item.respuesta_correcta\n        }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/mindMapGenerator.ts":
/*!********************************************!*\
  !*** ./src/lib/gemini/mindMapGenerator.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarMapaMental: () => (/* binding */ generarMapaMental)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera un mapa mental a partir de los documentos\n */ async function generarMapaMental(documentos, instrucciones) {\n    try {\n        // Validar entrada\n        if (!documentos || documentos.length === 0) {\n            throw new Error(\"No se han proporcionado documentos para generar el mapa mental.\");\n        }\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos || contenidoDocumentos.trim().length === 0) {\n            throw new Error(\"El contenido de los documentos está vacío o no es válido.\");\n        }\n        // Validar y limpiar instrucciones\n        const instruccionesLimpias = instrucciones?.trim() || 'Crea un mapa mental que organice los conceptos principales del contenido.';\n        // Construir el prompt final con validación\n        let finalPrompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_MAPAS_MENTALES.replace('{documentos}', contenidoDocumentos);\n        finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);\n        // Generar el mapa mental con timeout\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(finalPrompt);\n        const response = result.response.text();\n        // Validar que la respuesta no esté vacía\n        if (!response || response.trim().length === 0) {\n            throw new Error(\"La IA no generó ningún contenido para el mapa mental.\");\n        }\n        // Extraer y limpiar la respuesta (sin validaciones restrictivas)\n        let htmlContent = response.trim();\n        // Buscar el HTML en la respuesta (puede estar envuelto en markdown)\n        const htmlMatch = htmlContent.match(/<!DOCTYPE html>[\\s\\S]*<\\/html>/i);\n        if (htmlMatch) {\n            htmlContent = htmlMatch[0];\n        }\n        // Limpiar marcadores de código markdown si existen\n        htmlContent = htmlContent.replace(/```html/gi, '').replace(/```/g, '').trim();\n        // Log para debugging - mostrar lo que generó la IA\n        console.log('Contenido generado por la IA (primeros 500 caracteres):', htmlContent.substring(0, 500));\n        console.log('Longitud total del contenido:', htmlContent.length);\n        // Retornar el contenido tal como lo generó la IA (sin validaciones)\n        return htmlContent;\n    } catch (error) {\n        console.error('Error al generar mapa mental:', error);\n        // Proporcionar un mensaje de error más descriptivo si es posible\n        if (error instanceof Error) {\n            throw new Error(`Error al generar el mapa mental: ${error.message}`);\n        }\n        throw new Error(\"Ha ocurrido un error inesperado al generar el mapa mental.\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/mindMapGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/questionService.ts":
/*!*******************************************!*\
  !*** ./src/lib/gemini/questionService.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   obtenerRespuestaIA: () => (/* binding */ obtenerRespuestaIA)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Obtiene una respuesta de la IA a una pregunta sobre los documentos\n */ async function obtenerRespuestaIA(pregunta, documentos) {\n    try {\n        // Verificar que la pregunta sea válida\n        if (!pregunta || typeof pregunta !== 'string' || pregunta.trim() === '') {\n            console.warn('Se recibió una pregunta vacía o inválida');\n            return \"Por favor, proporciona una pregunta válida.\";\n        }\n        // Verificar que los documentos sean válidos\n        if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {\n            console.warn('No se proporcionaron documentos válidos para obtenerRespuestaIA');\n            return \"No se han proporcionado documentos para responder a esta pregunta.\";\n        }\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            console.warn('No se pudo preparar el contenido de los documentos');\n            return \"No se han podido procesar los documentos proporcionados. Por favor, verifica que los documentos contengan información válida.\";\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        const prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_PREGUNTAS.replace('{documentos}', contenidoDocumentos).replace('{pregunta}', pregunta);\n        // Generar la respuesta\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response;\n        return response.text();\n    } catch (error) {\n        console.error('Error al obtener respuesta de la IA:', error);\n        // Proporcionar un mensaje de error más descriptivo si es posible\n        if (error instanceof Error) {\n            return `Lo siento, ha ocurrido un error al procesar tu pregunta: ${error.message}. Por favor, inténtalo de nuevo más tarde.`;\n        }\n        return \"Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo más tarde.\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/questionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/testGenerator.ts":
/*!*****************************************!*\
  !*** ./src/lib/gemini/testGenerator.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarTest: () => (/* binding */ generarTest)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera un test con preguntas de opción múltiple a partir de los documentos\n */ async function generarTest(documentos, cantidad = 10, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar el test.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_TESTS.replace('{documentos}', contenidoDocumentos).replace('{cantidad}', cantidad.toString());\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar el test\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el JSON de la respuesta\n        const jsonMatch = response.match(/\\[\\s*\\{[\\s\\S]*\\}\\s*\\]/);\n        if (!jsonMatch) {\n            throw new Error(\"No se pudo extraer el formato JSON de la respuesta.\");\n        }\n        const testJson = jsonMatch[0];\n        const preguntas = JSON.parse(testJson);\n        // Validar el formato\n        if (!Array.isArray(preguntas) || preguntas.length === 0) {\n            throw new Error(\"El formato de las preguntas generadas no es válido.\");\n        }\n        // Validar que cada pregunta tiene el formato correcto\n        preguntas.forEach((pregunta, index)=>{\n            if (!pregunta.pregunta || !pregunta.opcion_a || !pregunta.opcion_b || !pregunta.opcion_c || !pregunta.opcion_d || !pregunta.respuesta_correcta) {\n                throw new Error(`La pregunta ${index + 1} no tiene el formato correcto.`);\n            }\n            // Asegurarse de que la respuesta correcta es una de las opciones válidas\n            if (![\n                'a',\n                'b',\n                'c',\n                'd'\n            ].includes(pregunta.respuesta_correcta)) {\n                throw new Error(`La respuesta correcta de la pregunta ${index + 1} no es válida.`);\n            }\n        });\n        return preguntas;\n    } catch (error) {\n        console.error('Error al generar test:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/testGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\");\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0Q7QUFFcEQsc0RBQXNEO0FBQy9DLFNBQVNDO0lBQ2QsT0FBT0Qsa0VBQW1CQSxDQUN4QkUsMENBQW9DLEVBQ3BDQSxrTkFBeUM7QUFFN0M7QUFFQSwrQ0FBK0M7QUFDeEMsTUFBTUksV0FBV0wsZUFBZSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXHNyY1xcbGliXFxzdXBhYmFzZVxcY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcblxuLy8gQ2xpZW50ZSBwYXJhIGVsIG5hdmVnYWRvciAoY29tcG9uZW50ZXMgZGVsIGNsaWVudGUpXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuICApO1xufVxuXG4vLyBNYW50ZW5lciBjb21wYXRpYmlsaWRhZCBjb24gY8OzZGlnbyBleGlzdGVudGVcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJjcmVhdGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJzdXBhYmFzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// Cliente para el servidor (componentes del servidor, API routes)\nasync function createServerSupabaseClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUQ7QUFDWjtBQUV2QyxrRUFBa0U7QUFDM0QsZUFBZUU7SUFDcEIsTUFBTUMsY0FBYyxNQUFNRixxREFBT0E7SUFFakMsT0FBT0QsaUVBQWtCQSxDQUN2QkksMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7UUFDRUgsU0FBUztZQUNQTztnQkFDRSxPQUFPTCxZQUFZSyxNQUFNO1lBQzNCO1lBQ0FDLFFBQU9DLFlBQVk7Z0JBQ2pCLElBQUk7b0JBQ0ZBLGFBQWFDLE9BQU8sQ0FBQyxDQUFDLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxPQUFPLEVBQUUsR0FDNUNYLFlBQVlZLEdBQUcsQ0FBQ0gsTUFBTUMsT0FBT0M7Z0JBRWpDLEVBQUUsT0FBTTtnQkFDTiwwREFBMEQ7Z0JBQzFELHdEQUF3RDtnQkFDeEQsaUJBQWlCO2dCQUNuQjtZQUNGO1FBQ0Y7SUFDRjtBQUVKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXHN1cGFiYXNlXFxzZXJ2ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU2VydmVyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3Nzcic7XG5pbXBvcnQgeyBjb29raWVzIH0gZnJvbSAnbmV4dC9oZWFkZXJzJztcblxuLy8gQ2xpZW50ZSBwYXJhIGVsIHNlcnZpZG9yIChjb21wb25lbnRlcyBkZWwgc2Vydmlkb3IsIEFQSSByb3V0ZXMpXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKSB7XG4gIGNvbnN0IGNvb2tpZVN0b3JlID0gYXdhaXQgY29va2llcygpO1xuXG4gIHJldHVybiBjcmVhdGVTZXJ2ZXJDbGllbnQoXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXG4gICAge1xuICAgICAgY29va2llczoge1xuICAgICAgICBnZXRBbGwoKSB7XG4gICAgICAgICAgcmV0dXJuIGNvb2tpZVN0b3JlLmdldEFsbCgpO1xuICAgICAgICB9LFxuICAgICAgICBzZXRBbGwoY29va2llc1RvU2V0KSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvb2tpZXNUb1NldC5mb3JFYWNoKCh7IG5hbWUsIHZhbHVlLCBvcHRpb25zIH0pID0+XG4gICAgICAgICAgICAgIGNvb2tpZVN0b3JlLnNldChuYW1lLCB2YWx1ZSwgb3B0aW9ucylcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgICAvLyBUaGUgYHNldEFsbGAgbWV0aG9kIHdhcyBjYWxsZWQgZnJvbSBhIFNlcnZlciBDb21wb25lbnQuXG4gICAgICAgICAgICAvLyBUaGlzIGNhbiBiZSBpZ25vcmVkIGlmIHlvdSBoYXZlIG1pZGRsZXdhcmUgcmVmcmVzaGluZ1xuICAgICAgICAgICAgLy8gdXNlciBzZXNzaW9ucy5cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH1cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVTZXJ2ZXJDbGllbnQiLCJjb29raWVzIiwiY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQiLCJjb29raWVTdG9yZSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsImdldEFsbCIsInNldEFsbCIsImNvb2tpZXNUb1NldCIsImZvckVhY2giLCJuYW1lIiwidmFsdWUiLCJvcHRpb25zIiwic2V0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/zodSchemas.ts":
/*!*******************************!*\
  !*** ./src/lib/zodSchemas.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiGeminiInputSchema: () => (/* binding */ ApiGeminiInputSchema),\n/* harmony export */   DocumentoSchema: () => (/* binding */ DocumentoSchema),\n/* harmony export */   GenerarFlashcardsSchema: () => (/* binding */ GenerarFlashcardsSchema),\n/* harmony export */   GenerarMapaMentalSchema: () => (/* binding */ GenerarMapaMentalSchema),\n/* harmony export */   GenerarPlanEstudiosSchema: () => (/* binding */ GenerarPlanEstudiosSchema),\n/* harmony export */   GenerarTestSchema: () => (/* binding */ GenerarTestSchema),\n/* harmony export */   PreguntaSchema: () => (/* binding */ PreguntaSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n// Directorio para esquemas Zod reutilizables\n\nconst DocumentoSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    titulo: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(200),\n    contenido: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1),\n    categoria: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional().nullable(),\n    numero_tema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z[\"null\"](),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.undefined()\n    ]).optional(),\n    creado_en: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    actualizado_en: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    user_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    tipo_original: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\nconst PreguntaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    pregunta: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    documentos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(DocumentoSchema).min(1)\n});\nconst GenerarTestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarTest'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarFlashcardsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarFlashcards'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarMapaMentalSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarMapaMental'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarPlanEstudiosSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarPlanEstudios'),\n    temarioId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1)\n});\nconst ApiGeminiInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    PreguntaSchema,\n    GenerarTestSchema,\n    GenerarFlashcardsSchema,\n    GenerarMapaMentalSchema,\n    GenerarPlanEstudiosSchema\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/zodSchemas.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/zod","vendor-chunks/whatwg-url","vendor-chunks/tr46","vendor-chunks/@google","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();