import { z } from 'zod';

export const preguntaFormSchema = z.object({
  pregunta: z.string().min(1, 'La pregunta es obligatoria').max(500, 'Máximo 500 caracteres'),
  documentos: z.array(
    z.object({
      id: z.string().optional(),
      titulo: z.string().min(1),
      contenido: z.string().min(1),
      categoria: z.string().optional().nullable(),
      numero_tema: z.union([z.number().int().positive(), z.string(), z.null(), z.undefined()]).optional(),
      creado_en: z.string().optional(),
      actualizado_en: z.string().optional(),
      user_id: z.string().optional(),
      tipo_original: z.string().optional(),
    })
  ).min(1, 'Debes seleccionar al menos un documento'),
});

// Esquema simplificado para los generadores que solo necesitan la petición
export const generatorFormSchema = z.object({
  peticion: z.string().min(1, 'La petición es obligatoria').max(500, 'Máximo 500 caracteres'),
});

export const testFormSchema = generatorFormSchema;
export const flashcardFormSchema = generatorFormSchema;
export const mindMapFormSchema = generatorFormSchema;
