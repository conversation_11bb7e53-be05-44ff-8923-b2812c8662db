"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/planificacion/services/planEstudiosService.ts":
/*!********************************************************************!*\
  !*** ./src/features/planificacion/services/planEstudiosService.ts ***!
  \********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarPlan: () => (/* binding */ activarPlan),\n/* harmony export */   actualizarNotasPlan: () => (/* binding */ actualizarNotasPlan),\n/* harmony export */   eliminarPlan: () => (/* binding */ eliminarPlan),\n/* harmony export */   guardarPlanEstudios: () => (/* binding */ guardarPlanEstudios),\n/* harmony export */   guardarProgresoTarea: () => (/* binding */ guardarProgresoTarea),\n/* harmony export */   obtenerEstadisticasProgreso: () => (/* binding */ obtenerEstadisticasProgreso),\n/* harmony export */   obtenerHistorialPlanes: () => (/* binding */ obtenerHistorialPlanes),\n/* harmony export */   obtenerPlanEstudiosActivo: () => (/* binding */ obtenerPlanEstudiosActivo),\n/* harmony export */   obtenerProgresoPlan: () => (/* binding */ obtenerProgresoPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(app-pages-browser)/./src/features/auth/services/authService.ts\");\n\n\n/**\n * Guarda un plan de estudios generado en la base de datos\n */ async function guardarPlanEstudios(temarioId, planData, titulo) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').insert([\n            {\n                user_id: user.id,\n                temario_id: temarioId,\n                titulo: titulo || 'Plan de Estudios',\n                plan_data: planData,\n                activo: true,\n                version: 1\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al guardar plan de estudios:', error);\n            return null;\n        }\n        console.log('✅ Plan de estudios guardado exitosamente:', data.id);\n        return data.id;\n    } catch (error) {\n        console.error('Error al guardar plan de estudios:', error);\n        return null;\n    }\n}\n/**\n * Obtiene el plan de estudios activo para un temario\n */ async function obtenerPlanEstudiosActivo(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('*').eq('user_id', user.id).eq('temario_id', temarioId).eq('activo', true).single();\n        if (error) {\n            if (error.code === 'PGRST116') {\n                return null; // No hay plan activo\n            }\n            console.error('Error al obtener plan de estudios:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener plan de estudios:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todos los planes de estudios de un temario (historial)\n */ async function obtenerHistorialPlanes(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('*').eq('user_id', user.id).eq('temario_id', temarioId).order('fecha_generacion', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener historial de planes:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener historial de planes:', error);\n        return [];\n    }\n}\n/**\n * Actualiza las notas de un plan de estudios\n */ async function actualizarNotasPlan(planId, notas) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').update({\n            notas,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) {\n            console.error('Error al actualizar notas del plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar notas del plan:', error);\n        return false;\n    }\n}\n/**\n * Marca un plan como activo y desactiva los demás\n */ async function activarPlan(planId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').update({\n            activo: true,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) {\n            console.error('Error al activar plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al activar plan:', error);\n        return false;\n    }\n}\n/**\n * Elimina un plan de estudios\n */ async function eliminarPlan(planId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').delete().eq('id', planId);\n        if (error) {\n            console.error('Error al eliminar plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar plan:', error);\n        return false;\n    }\n}\n/**\n * Guarda el progreso de una tarea del plan\n */ async function guardarProgresoTarea(planId, semanaNúmero, diaNombre, tareaTitulo, tareaTipo, completado, tiempoRealMinutos, notasProgreso, calificacion) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return false;\n        }\n        // Verificar si ya existe un registro de progreso para esta tarea\n        const { data: existente } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('id').eq('plan_id', planId).eq('user_id', user.id).eq('semana_numero', semanaNúmero).eq('dia_nombre', diaNombre).eq('tarea_titulo', tareaTitulo).single();\n        if (existente) {\n            // Actualizar registro existente\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').update({\n                completado,\n                fecha_completado: completado ? new Date().toISOString() : null,\n                tiempo_real_minutos: tiempoRealMinutos,\n                notas_progreso: notasProgreso,\n                calificacion,\n                actualizado_en: new Date().toISOString()\n            }).eq('id', existente.id);\n            if (error) {\n                console.error('Error al actualizar progreso:', error);\n                return false;\n            }\n        } else {\n            // Crear nuevo registro\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').insert([\n                {\n                    plan_id: planId,\n                    user_id: user.id,\n                    semana_numero: semanaNúmero,\n                    dia_nombre: diaNombre,\n                    tarea_titulo: tareaTitulo,\n                    tarea_tipo: tareaTipo,\n                    completado,\n                    fecha_completado: completado ? new Date().toISOString() : null,\n                    tiempo_real_minutos: tiempoRealMinutos,\n                    notas_progreso: notasProgreso,\n                    calificacion\n                }\n            ]);\n            if (error) {\n                console.error('Error al crear progreso:', error);\n                return false;\n            }\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al guardar progreso de tarea:', error);\n        return false;\n    }\n}\n/**\n * Obtiene el progreso de un plan de estudios\n */ async function obtenerProgresoPlan(planId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('*').eq('plan_id', planId).eq('user_id', user.id).order('semana_numero', {\n            ascending: true\n        }).order('creado_en', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener progreso del plan:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener progreso del plan:', error);\n        return [];\n    }\n}\n/**\n * Obtiene estadísticas del progreso del plan\n */ async function obtenerEstadisticasProgreso(planId) {\n    try {\n        const progreso = await obtenerProgresoPlan(planId);\n        const plan = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('plan_data').eq('id', planId).single();\n        if (!plan.data) {\n            return {\n                totalTareas: 0,\n                tareasCompletadas: 0,\n                porcentajeCompletado: 0,\n                tiempoTotalEstimado: 0,\n                tiempoTotalReal: 0,\n                semanasCompletadas: 0,\n                totalSemanas: 0\n            };\n        }\n        const planData = plan.data.plan_data;\n        const totalSemanas = planData.semanas.length;\n        // Calcular total de tareas\n        let totalTareas = 0;\n        planData.semanas.forEach((semana)=>{\n            semana.dias.forEach((dia)=>{\n                totalTareas += dia.tareas.length;\n            });\n        });\n        const tareasCompletadas = progreso.filter((p)=>p.completado).length;\n        const porcentajeCompletado = totalTareas > 0 ? tareasCompletadas / totalTareas * 100 : 0;\n        const tiempoTotalReal = progreso.filter((p)=>p.tiempo_real_minutos).reduce((total, p)=>total + (p.tiempo_real_minutos || 0), 0);\n        // Calcular semanas completadas (todas las tareas de la semana completadas)\n        let semanasCompletadas = 0;\n        planData.semanas.forEach((semana)=>{\n            const tareasSemanaTotales = semana.dias.reduce((total, dia)=>total + dia.tareas.length, 0);\n            const tareasSemanCompletadas = progreso.filter((p)=>p.semana_numero === semana.numero && p.completado).length;\n            if (tareasSemanaTotales > 0 && tareasSemanCompletadas === tareasSemanaTotales) {\n                semanasCompletadas++;\n            }\n        });\n        return {\n            totalTareas,\n            tareasCompletadas,\n            porcentajeCompletado: Math.round(porcentajeCompletado * 100) / 100,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal,\n            semanasCompletadas,\n            totalSemanas\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas de progreso:', error);\n        return {\n            totalTareas: 0,\n            tareasCompletadas: 0,\n            porcentajeCompletado: 0,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal: 0,\n            semanasCompletadas: 0,\n            totalSemanas: 0\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/planificacion/services/planEstudiosService.ts\n"));

/***/ })

});