import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';

// Configuración de la API de Gemini
const API_KEY = process.env.GEMINI_API_KEY || '';
const MODEL_NAME = 'gemini-2.5-flash-preview-05-20';

// Verificar que la API key esté configurada
if (!API_KEY) {
  console.error('GEMINI_API_KEY no está configurada en las variables de entorno');
}

// Inicializar el cliente de Gemini
export const genAI = new GoogleGenerativeAI(API_KEY);
export const model = genAI.getGenerativeModel({
  model: MODEL_NAME,
  safetySettings: [
    {
      category: HarmCategory.HARM_CATEGORY_HARASSMENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
  ],
});

// Ya no usamos instrucciones base globales, cada servicio usa su propio prompt personalizado

/**
 * Trunca el contenido de un documento si es demasiado largo
 */
export function truncarContenido(contenido: string | undefined | null, maxLength: number = 25000): string {
  // Verificar que el contenido sea una cadena válida
  if (contenido === undefined || contenido === null) {
    console.warn('Se intentó truncar un contenido undefined o null');
    return '';
  }

  // Asegurarse de que el contenido sea una cadena
  const contenidoStr = String(contenido);

  if (contenidoStr.length <= maxLength) {
    return contenidoStr;
  }

  return contenidoStr.substring(0, maxLength) +
    `\n\n[CONTENIDO TRUNCADO: El documento original es más largo. Esta es una versión reducida para procesamiento.]`;
}

// --- Nueva lógica de Chunking ---

const CHUNK_SIZE = 5000; // Caracteres
const CHUNK_OVERLAP = 200; // Caracteres
const MAX_TOTAL_CONTEXT_LENGTH = 50000; // Caracteres

interface DocumentChunk {
  originalDocumentTitle: string;
  chunkIndex: number;
  text: string;
}

function createTextChunks(
  documentTitle: string,
  content: string | undefined | null
): DocumentChunk[] {
  if (!content) {
    return [];
  }
  const contentStr = String(content);
  const chunks: DocumentChunk[] = [];
  let chunkIndex = 0;
  let currentIndex = 0;

  while (currentIndex < contentStr.length) {
    const endIndex = Math.min(currentIndex + CHUNK_SIZE, contentStr.length);
    const text = contentStr.substring(currentIndex, endIndex);
    chunks.push({
      originalDocumentTitle: documentTitle,
      chunkIndex: chunkIndex + 1, // Empezar índice en 1 para el usuario
      text,
    });

    chunkIndex++;
    if (endIndex === contentStr.length) {
      break; 
    }
    currentIndex += CHUNK_SIZE - CHUNK_OVERLAP;
    // Asegurar que no haya un bucle infinito si CHUNK_OVERLAP >= CHUNK_SIZE
    if (currentIndex >= endIndex && endIndex < contentStr.length) { 
        currentIndex = endIndex; // Avanzar al menos hasta el final del chunk actual
    }
  }
  return chunks;
}

/**
 * Prepara los documentos para enviarlos al modelo, dividiéndolos en chunks.
 */
export function prepararDocumentos(documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]): string {
  if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {
    console.warn('No se proporcionaron documentos válidos para prepararDocumentos');
    return '';
  }

  try {
    const allChunks: DocumentChunk[] = [];

    for (const doc of documentos) {
      if (!doc || typeof doc !== 'object' || !doc.titulo || doc.contenido === undefined || doc.contenido === null) {
        console.warn('Documento inválido, sin título o contenido en prepararDocumentos:', doc);
        continue; 
      }
      
      const fullOriginalTitle = `${doc.categoria ? `[${doc.categoria}] ` : ''}${doc.numero_tema ? `Tema ${doc.numero_tema}: ` : ''}${doc.titulo}`;
      const documentChunks = createTextChunks(fullOriginalTitle.trim(), doc.contenido);
      allChunks.push(...documentChunks);
    }

    if (allChunks.length === 0) {
      console.warn('No se generaron chunks a partir de los documentos proporcionados.');
      return '';
    }

    let fullContext = allChunks.map(chunk => {
      return `
=== INICIO CHUNK: ${chunk.originalDocumentTitle} - Parte ${chunk.chunkIndex} ===
${chunk.text}
=== FIN CHUNK: ${chunk.originalDocumentTitle} - Parte ${chunk.chunkIndex} ===
`;
    }).join('\n\n');

    if (fullContext.length > MAX_TOTAL_CONTEXT_LENGTH) {
      console.warn(`El contexto combinado (${fullContext.length} caracteres) excede el máximo de ${MAX_TOTAL_CONTEXT_LENGTH}. Se truncará.`);
      fullContext = fullContext.substring(0, MAX_TOTAL_CONTEXT_LENGTH) +
        `\n\n[CONTEXTO GENERAL TRUNCADO: El contenido combinado de todos los chunks excedió el límite máximo (${MAX_TOTAL_CONTEXT_LENGTH} caracteres).]`;
    }
    
    return fullContext;

  } catch (error) {
    console.error('Error al preparar documentos con chunks:', error);
    return '';
  }
}
