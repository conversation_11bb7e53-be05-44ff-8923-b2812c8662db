import { useEffect, useRef, useCallback } from 'react';

interface UseInactivityTimerProps {
  timeout: number; // Tiempo en milisegundos
  onTimeout: () => void; // Función a ejecutar cuando se agote el tiempo
  enabled?: boolean; // Si el timer está habilitado
}

/**
 * Hook para manejar la desconexión automática por inactividad
 */
export const useInactivityTimer = ({ 
  timeout, 
  onTimeout, 
  enabled = true 
}: UseInactivityTimerProps) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());

  // Función para resetear el timer
  const resetTimer = useCallback(() => {
    if (!enabled) return;

    // Limpiar el timer anterior si existe
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Actualizar la última actividad
    lastActivityRef.current = Date.now();

    // Crear un nuevo timer
    timeoutRef.current = setTimeout(() => {
      onTimeout();
    }, timeout);
  }, [timeout, onTimeout, enabled]);

  // Función para limpiar el timer
  const clearTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Función para obtener el tiempo restante
  const getTimeRemaining = useCallback(() => {
    if (!enabled || !timeoutRef.current) return 0;
    
    const elapsed = Date.now() - lastActivityRef.current;
    const remaining = Math.max(0, timeout - elapsed);
    return remaining;
  }, [timeout, enabled]);

  // Eventos que consideramos como actividad del usuario
  const events = [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click',
    'keydown'
  ];

  useEffect(() => {
    if (!enabled) {
      clearTimer();
      return;
    }

    // Función que maneja los eventos de actividad
    const handleActivity = () => {
      resetTimer();
    };

    // Agregar event listeners
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Iniciar el timer
    resetTimer();

    // Cleanup
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
      clearTimer();
    };
  }, [enabled, resetTimer, clearTimer]);

  return {
    resetTimer,
    clearTimer,
    getTimeRemaining
  };
};

/**
 * Hook simplificado para desconexión automática
 */
export const useAutoLogout = (
  timeoutMinutes: number = 5,
  onLogout: () => void,
  enabled: boolean = true
) => {
  const timeoutMs = timeoutMinutes * 60 * 1000; // Convertir minutos a milisegundos
  
  return useInactivityTimer({
    timeout: timeoutMs,
    onTimeout: onLogout,
    enabled
  });
};
