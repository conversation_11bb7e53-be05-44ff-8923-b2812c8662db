"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/temario/services/temariosPredefinidosService.ts":
/*!**********************************************************************!*\
  !*** ./src/features/temario/services/temariosPredefinidosService.ts ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TEMARIOS_PREDEFINIDOS: () => (/* binding */ TEMARIOS_PREDEFINIDOS),\n/* harmony export */   buscarTemariosPredefinidos: () => (/* binding */ buscarTemariosPredefinidos),\n/* harmony export */   cargarTemarioPredefinido: () => (/* binding */ cargarTemarioPredefinido),\n/* harmony export */   convertirTemarioParaCreacion: () => (/* binding */ convertirTemarioParaCreacion),\n/* harmony export */   obtenerEstadisticasTemarioPredefinido: () => (/* binding */ obtenerEstadisticasTemarioPredefinido),\n/* harmony export */   obtenerInfoTemarioPredefinido: () => (/* binding */ obtenerInfoTemarioPredefinido),\n/* harmony export */   obtenerTemariosPredefinidos: () => (/* binding */ obtenerTemariosPredefinidos),\n/* harmony export */   parsearTemario: () => (/* binding */ parsearTemario),\n/* harmony export */   validarTemarioPredefinido: () => (/* binding */ validarTemarioPredefinido)\n/* harmony export */ });\n/**\n * Lista de temarios predefinidos disponibles\n */ const TEMARIOS_PREDEFINIDOS = [\n    {\n        id: 'a1_2019_junta',\n        nombre: 'Cuerpo Superior Facultativo - Informática (A1.2019)',\n        descripcion: 'Temario completo para oposiciones del Cuerpo Superior Facultativo, opción Informática de la Junta de Andalucía',\n        cuerpo: 'CUERPO SUPERIOR FACULTATIVO, OPCIÓN INFORMÁTICA (A1.2019)',\n        archivo: 'a1_2019_junta.md'\n    },\n    {\n        id: 'c1_junta',\n        nombre: 'Cuerpo General de Administrativos (C1.1000)',\n        descripcion: 'Temario completo para oposiciones del Cuerpo General de Administrativos de la Junta de Andalucía',\n        cuerpo: 'CUERPO GENERAL DE ADMINISTRATIVOS (C1.1000)',\n        archivo: 'c1_junta.md'\n    },\n    {\n        id: 'c2_estado',\n        nombre: 'Cuerpo General Auxiliar del Estado (C2)',\n        descripcion: 'Temario para oposiciones del Cuerpo General Auxiliar del Estado',\n        cuerpo: 'CUERPO GENERAL AUXILIAR DEL ESTADO (C2)',\n        archivo: 'c2_estado.md'\n    },\n    {\n        id: 'c2_junta',\n        nombre: 'Cuerpo General Auxiliar - Junta de Andalucía (C2)',\n        descripcion: 'Temario para oposiciones del Cuerpo General Auxiliar de la Junta de Andalucía',\n        cuerpo: 'CUERPO GENERAL AUXILIAR - JUNTA DE ANDALUCÍA (C2)',\n        archivo: 'c2_junta.md'\n    }\n];\n/**\n * Obtiene la lista de temarios predefinidos disponibles\n */ function obtenerTemariosPredefinidos() {\n    return TEMARIOS_PREDEFINIDOS;\n}\n/**\n * Parsea el contenido de un archivo de temario y extrae los temas\n */ function parsearTemario(contenido) {\n    const temas = [];\n    const lineas = contenido.split('\\n');\n    console.log('Parseando temario, total de líneas:', lineas.length);\n    for(let i = 0; i < lineas.length; i++){\n        const linea = lineas[i].trim();\n        // Patrón 1: \"Tema X.\" (formato estándar)\n        let match = linea.match(/^Tema\\s+(\\d+)\\.\\s*(.+)$/);\n        // Patrón 2: \"X.\" (formato numérico simple)\n        if (!match) {\n            match = linea.match(/^(\\d+)\\.\\s*(.+)$/);\n        }\n        if (match) {\n            const numero = parseInt(match[1]);\n            const titulo = match[2].trim();\n            // Filtrar líneas que no son realmente temas (muy cortas o solo números)\n            if (titulo.length > 10 && !titulo.match(/^[IVX]+\\s*$/)) {\n                console.log(\"Encontrado tema \".concat(numero, \": \").concat(titulo.substring(0, 50), \"...\"));\n                temas.push({\n                    numero,\n                    titulo,\n                    descripcion: titulo.length > 100 ? titulo.substring(0, 100) + '...' : titulo\n                });\n            }\n        }\n    }\n    console.log('Total de temas encontrados:', temas.length);\n    return temas;\n}\n/**\n * Carga un temario predefinido desde el archivo correspondiente\n */ async function cargarTemarioPredefinido(id) {\n    try {\n        const temarioInfo = TEMARIOS_PREDEFINIDOS.find((t)=>t.id === id);\n        if (!temarioInfo) {\n            console.error('Temario predefinido no encontrado:', id);\n            return null;\n        }\n        // Cargar el contenido del archivo\n        const response = await fetch(\"/temarios/\".concat(temarioInfo.archivo));\n        if (!response.ok) {\n            console.error('Error al cargar archivo de temario:', response.status);\n            return null;\n        }\n        const contenido = await response.text();\n        const temas = parsearTemario(contenido);\n        return {\n            ...temarioInfo,\n            temas\n        };\n    } catch (error) {\n        console.error('Error al cargar temario predefinido:', error);\n        return null;\n    }\n}\n/**\n * Obtiene información básica de un temario predefinido sin cargar los temas\n */ function obtenerInfoTemarioPredefinido(id) {\n    return TEMARIOS_PREDEFINIDOS.find((t)=>t.id === id) || null;\n}\n/**\n * Valida que un temario predefinido tenga la estructura correcta\n */ function validarTemarioPredefinido(temario) {\n    if (!temario.id || !temario.nombre || !temario.cuerpo) {\n        return false;\n    }\n    if (!temario.temas || temario.temas.length === 0) {\n        return false;\n    }\n    // Verificar que los temas tengan numeración consecutiva\n    for(let i = 0; i < temario.temas.length; i++){\n        const tema = temario.temas[i];\n        if (!tema.numero || !tema.titulo) {\n            return false;\n        }\n        // Verificar numeración consecutiva (empezando desde 1)\n        if (tema.numero !== i + 1) {\n            console.warn(\"Numeraci\\xf3n no consecutiva en tema \".concat(tema.numero, \", esperado \").concat(i + 1));\n        }\n    }\n    return true;\n}\n/**\n * Convierte un temario predefinido al formato necesario para crear en la base de datos\n */ function convertirTemarioParaCreacion(temario) {\n    return {\n        titulo: temario.nombre,\n        descripcion: \"\".concat(temario.descripcion, \"\\n\\nCuerpo: \").concat(temario.cuerpo),\n        tipo: 'completo',\n        temas: temario.temas.map((tema, index)=>({\n                numero: tema.numero,\n                titulo: tema.titulo,\n                descripcion: tema.descripcion,\n                orden: index + 1\n            }))\n    };\n}\n/**\n * Busca temarios predefinidos por texto\n */ function buscarTemariosPredefinidos(busqueda) {\n    if (!busqueda.trim()) {\n        return TEMARIOS_PREDEFINIDOS;\n    }\n    const termino = busqueda.toLowerCase();\n    return TEMARIOS_PREDEFINIDOS.filter((temario)=>temario.nombre.toLowerCase().includes(termino) || temario.descripcion.toLowerCase().includes(termino) || temario.cuerpo.toLowerCase().includes(termino));\n}\n/**\n * Obtiene estadísticas de un temario predefinido\n */ async function obtenerEstadisticasTemarioPredefinido(id) {\n    try {\n        const temario = await cargarTemarioPredefinido(id);\n        if (!temario) {\n            return null;\n        }\n        return {\n            totalTemas: temario.temas.length,\n            tipoTemario: 'Temario Completo Predefinido',\n            cuerpo: temario.cuerpo\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas:', error);\n        return null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/temario/services/temariosPredefinidosService.ts\n"));

/***/ })

});