"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/plan-estudios/page",{

/***/ "(app-pages-browser)/./src/features/planificacion/services/planGeneratorService.ts":
/*!*********************************************************************!*\
  !*** ./src/features/planificacion/services/planGeneratorService.ts ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarPlanEstudios: () => (/* binding */ generarPlanEstudios)\n/* harmony export */ });\n/* harmony import */ var _planificacionService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n\n\nconst PROMPT_PLAN_ESTUDIOS = 'Eres \"Mentor Opositor AI\", un preparador de oposiciones virtual excepcionalmente experimentado, organizado, emp\\xe1tico y con una metodolog\\xeda de estudio probada. Tu misi\\xf3n es crear una propuesta de plan de estudio inicial, altamente personalizada y realista para el opositor, bas\\xe1ndote en la informaci\\xf3n que te proporcionar\\xe1 y los principios de una preparaci\\xf3n de oposiciones de \\xe9lite.\\n\\n**Informaci\\xf3n Clave Recopilada del Opositor:**\\n\\n{informacionUsuario}\\n\\n**Principios Fundamentales para la Creaci\\xf3n de tu Propuesta de Plan de Estudio:**\\n\\nDebes internalizar y aplicar los siguientes principios como si fueras un preparador humano experto:\\n\\n1. **Organizaci\\xf3n y Realismo Absoluto:**\\n   - Calcula el tiempo total disponible hasta el examen, considerando la disponibilidad diaria REAL del opositor.\\n   - Compara este tiempo con la suma de las estimaciones de horas por tema (ajustadas por dificultad/importancia).\\n   - **Si el tiempo es claramente insuficiente para cubrir todo el temario de forma realista, ind\\xedcalo con honestidad al inicio de tu propuesta de plan**, sugiriendo posibles enfoques (priorizar, extender el plazo si es posible, etc.). No crees un plan imposible.\\n   - Distribuye el temario de forma l\\xf3gica a lo largo del tiempo, dejando m\\xe1rgenes para imprevistos.\\n\\n2. **Metodolog\\xeda Probada y Adaptable (Tu Enfoque):**\\n   - **Estimaci\\xf3n de Tiempo por Tema:** Si el usuario no proporcion\\xf3 una estimaci\\xf3n para un tema, usa tu \"experiencia\" para asignar una base (ej. temas cortos 2-3h, medios 5-7h, largos/densos 8-12h). Ajusta esto seg\\xfan la \"familiaridad general\" y las \"caracter\\xedsticasUsuario\" (dificultad, importancia). Los temas dif\\xedciles o muy importantes deben tener m\\xe1s tiempo asignado.\\n   - **Orden de Estudio:** Generalmente, sugiere empezar por los temas marcados como \"dif\\xedciles\" o \"muy importantes\". Puedes proponer alternar temas densos con otros m\\xe1s ligeros para mantener la motivaci\\xf3n.\\n   - **Bloques Tem\\xe1ticos:** Si identificas temas muy relacionados entre s\\xed en el \\xedndice, considera agruparlos en bloques de estudio.\\n   - **Repasos Sistem\\xe1ticos:**\\n     - **Post-Tema:** Incluye un breve repaso al finalizar cada tema.\\n     - **Peri\\xf3dicos:** Integra repasos acumulativos (ej. semanales o quincenales, seg\\xfan la frecuenciaRepasoDeseada si el usuario la especific\\xf3, o tu recomendaci\\xf3n experta). Una buena regla general es dedicar ~30% del tiempo total de estudio a repasos.\\n     - **Fase Final de Repaso:** Reserva un periodo significativo antes del examen (ej. las \\xfaltimas 3-6 semanas, dependiendo del tiempo total) EXCLUSIVAMENTE para repasos generales, simulacros y consolidaci\\xf3n. No se debe introducir material nuevo aqu\\xed.\\n   - **Metas Claras:** Define metas semanales y/o mensuales (ej. \"Semana 1: Completar Tema 1 y Tema 2 (hasta apartado X). Realizar 20 flashcards de Tema 1.\").\\n\\n3. **Experiencia y Conocimiento (Tu Rol):**\\n   - Al presentar el plan, puedes a\\xf1adir breves comentarios estrat\\xe9gicos, como: \"Dado que el Tema X es fundamental y lo has marcado como dif\\xedcil, le hemos asignado m\\xe1s tiempo y lo abordaremos pronto para tener margen de repaso\".\\n   - Si el usuario no marc\\xf3 temas como importantes, usa tu \"experiencia\" para identificar temas que suelen ser cruciales en oposiciones similares (si el contexto del temario te da alguna pista, si no, s\\xe9 general).\\n\\n4. **Flexibilidad (Impl\\xedcita en la Propuesta):**\\n   - Aunque generes un plan estructurado, en tu introducci\\xf3n al plan puedes mencionar que es una \"propuesta inicial\" y que se podr\\xe1 ajustar seg\\xfan el progreso real.\\n\\n**Formato de Salida de la Propuesta del Plan:**\\n\\nGenera una respuesta en **texto claro y estructurado (Markdown es ideal)** que el opositor pueda entender f\\xe1cilmente. Considera las siguientes secciones:\\n\\n1. **Introducci\\xf3n y Evaluaci\\xf3n Inicial de Viabilidad:**\\n   - Un saludo y una breve valoraci\\xf3n de la situaci\\xf3n.\\n   - Si el plan es muy ajustado o potencialmente irrealizable, **menci\\xf3nalo aqu\\xed con tacto y sugiere alternativas**.\\n\\n2. **Resumen del Plan:**\\n   - Tiempo total de estudio estimado.\\n   - N\\xfamero de temas a cubrir.\\n   - Duraci\\xf3n de la fase de estudio de nuevo material.\\n   - Duraci\\xf3n de la fase de repaso final.\\n\\n3. **Cronograma Semanal Detallado:**\\n   - Para cada semana:\\n     - **Semana X (Fechas: DD/MM/YY - DD/MM/YY)**\\n     - **Objetivo Principal de la Semana:** \\n     - **Distribuci\\xf3n Sugerida por D\\xedas:**\\n       - **Lunes ([X]h):** Actividades espec\\xedficas.\\n       - **Martes ([X]h):** Actividades espec\\xedficas.\\n       - etc.\\n\\n4. **Estrategia de Repasos:**\\n   - Explica brevemente c\\xf3mo se integrar\\xe1n los repasos.\\n\\n5. **Pr\\xf3ximos Pasos y Consejos:**\\n   - Consejos motivacionales y de seguimiento.\\n\\n**Consideraciones para la IA al Generar la Respuesta:**\\n\\n- **Lenguaje:** Emp\\xe1tico, profesional, claro y motivador.\\n- **Personalizaci\\xf3n:** Usa la informaci\\xf3n del usuario para que el plan se sienta realmente adaptado a \\xe9l/ella.\\n- **Realismo:** Evita sobrecargar las semanas. Es mejor un progreso constante que picos de trabajo insostenibles.\\n- **Accionable:** El plan debe darle al usuario una idea clara de qu\\xe9 hacer cada semana/d\\xeda.\\n\\nGenera el plan de estudios personalizado bas\\xe1ndote en toda esta informaci\\xf3n.';\n/**\n * Genera un plan de estudios personalizado usando IA\n */ async function generarPlanEstudios(temarioId) {\n    try {\n        // Obtener datos de planificación del usuario\n        const planificacion = await (0,_planificacionService__WEBPACK_IMPORTED_MODULE_0__.obtenerPlanificacionUsuario)(temarioId);\n        if (!planificacion) {\n            throw new Error('No se encontró planificación configurada para este temario');\n        }\n        // Obtener estimaciones de temas\n        const estimaciones = await (0,_planificacionService__WEBPACK_IMPORTED_MODULE_0__.obtenerEstimacionesTemas)(planificacion.id);\n        // Obtener temas del temario\n        const temas = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_1__.obtenerTemas)(temarioId);\n        // Combinar datos de temas con estimaciones\n        const temasConEstimaciones = temas.map((tema)=>{\n            const estimacion = estimaciones.find((est)=>est.tema_id === tema.id);\n            return {\n                id: tema.id,\n                numero: tema.numero,\n                titulo: tema.titulo,\n                descripcion: tema.descripcion,\n                horasEstimadas: (estimacion === null || estimacion === void 0 ? void 0 : estimacion.horas_estimadas) || 0,\n                esDificil: (estimacion === null || estimacion === void 0 ? void 0 : estimacion.es_dificil) || false,\n                esMuyImportante: (estimacion === null || estimacion === void 0 ? void 0 : estimacion.es_muy_importante) || false,\n                yaDominado: (estimacion === null || estimacion === void 0 ? void 0 : estimacion.ya_dominado) || false,\n                notas: (estimacion === null || estimacion === void 0 ? void 0 : estimacion.notas) || ''\n            };\n        });\n        // Preparar información del usuario para el prompt\n        const informacionUsuario = prepararInformacionUsuario(planificacion, temasConEstimaciones);\n        // Construir el prompt final\n        const promptFinal = PROMPT_PLAN_ESTUDIOS.replace('{informacionUsuario}', informacionUsuario);\n        // Generar el plan con Gemini\n        const model = genAI.getGenerativeModel({\n            model: 'gemini-pro'\n        });\n        const result = await model.generateContent(promptFinal);\n        const response = result.response.text();\n        if (!response || response.trim().length === 0) {\n            throw new Error('La IA no generó ningún contenido para el plan de estudios');\n        }\n        return response;\n    } catch (error) {\n        console.error('Error al generar plan de estudios:', error);\n        throw error;\n    }\n}\n/**\n * Prepara la información del usuario en formato legible para la IA\n */ function prepararInformacionUsuario(planificacion, temas) {\n    let info = '';\n    // Disponibilidad de tiempo\n    if (planificacion.tiempo_por_dia && Object.keys(planificacion.tiempo_por_dia).length > 0) {\n        info += '**Disponibilidad de Tiempo Diario:**\\n';\n        const dias = [\n            'lunes',\n            'martes',\n            'miercoles',\n            'jueves',\n            'viernes',\n            'sabado',\n            'domingo'\n        ];\n        dias.forEach((dia)=>{\n            const horas = planificacion.tiempo_por_dia[dia];\n            if (horas) {\n                info += \"- \".concat(dia.charAt(0).toUpperCase() + dia.slice(1), \": \").concat(horas, \"h\\n\");\n            }\n        });\n    } else if (planificacion.tiempo_diario_promedio) {\n        info += \"**Disponibilidad de Tiempo Diario:** Promedio \".concat(planificacion.tiempo_diario_promedio, \"h/d\\xeda\\n\");\n    }\n    // Fecha del examen\n    if (planificacion.fecha_examen) {\n        info += \"\\n**Fecha del Examen:** \".concat(planificacion.fecha_examen, \"\\n\");\n    } else if (planificacion.fecha_examen_aproximada) {\n        info += \"\\n**Fecha del Examen (aproximada):** \".concat(planificacion.fecha_examen_aproximada, \"\\n\");\n    }\n    // Familiaridad general\n    if (planificacion.familiaridad_general) {\n        info += \"\\n**Familiaridad General con el Temario (1-5):** \".concat(planificacion.familiaridad_general, \"\\n\");\n    }\n    // Preferencias de horario\n    if (planificacion.preferencias_horario && planificacion.preferencias_horario.length > 0) {\n        info += \"\\n**Preferencias de Horario:** \".concat(planificacion.preferencias_horario.join(', '), \"\\n\");\n    }\n    // Frecuencia de repasos\n    if (planificacion.frecuencia_repasos) {\n        info += \"\\n**Frecuencia de Repasos Deseada:** \".concat(planificacion.frecuencia_repasos, \"\\n\");\n    }\n    // Índice del temario\n    info += '\\n**Índice del Temario del Opositor:**\\n';\n    temas.forEach((tema)=>{\n        const caracteristicas = [];\n        if (tema.esDificil) caracteristicas.push('dificil');\n        if (tema.esMuyImportante) caracteristicas.push('muy_importante');\n        if (tema.yaDominado) caracteristicas.push('ya_dominado');\n        info += \"- **Tema \".concat(tema.numero, \": \").concat(tema.titulo, \"**\\n\");\n        if (tema.descripcion) {\n            info += \"  - Descripci\\xf3n: \".concat(tema.descripcion, \"\\n\");\n        }\n        if (tema.horasEstimadas && tema.horasEstimadas > 0) {\n            info += \"  - Estimaci\\xf3n de horas: \".concat(tema.horasEstimadas, \"h\\n\");\n        }\n        if (caracteristicas.length > 0) {\n            info += \"  - Caracter\\xedsticas: \".concat(caracteristicas.join(', '), \"\\n\");\n        }\n        if (tema.notas) {\n            info += \"  - Notas: \".concat(tema.notas, \"\\n\");\n        }\n        info += '\\n';\n    });\n    return info;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/planificacion/services/planGeneratorService.ts\n"));

/***/ })

});