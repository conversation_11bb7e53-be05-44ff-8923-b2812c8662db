"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/temario/services/temariosPredefinidosService.ts":
/*!**********************************************************************!*\
  !*** ./src/features/temario/services/temariosPredefinidosService.ts ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TEMARIOS_PREDEFINIDOS: () => (/* binding */ TEMARIOS_PREDEFINIDOS),\n/* harmony export */   buscarTemariosPredefinidos: () => (/* binding */ buscarTemariosPredefinidos),\n/* harmony export */   cargarTemarioPredefinido: () => (/* binding */ cargarTemarioPredefinido),\n/* harmony export */   convertirTemarioParaCreacion: () => (/* binding */ convertirTemarioParaCreacion),\n/* harmony export */   obtenerEstadisticasTemarioPredefinido: () => (/* binding */ obtenerEstadisticasTemarioPredefinido),\n/* harmony export */   obtenerInfoTemarioPredefinido: () => (/* binding */ obtenerInfoTemarioPredefinido),\n/* harmony export */   obtenerTemariosPredefinidos: () => (/* binding */ obtenerTemariosPredefinidos),\n/* harmony export */   parsearTemario: () => (/* binding */ parsearTemario),\n/* harmony export */   validarTemarioPredefinido: () => (/* binding */ validarTemarioPredefinido)\n/* harmony export */ });\n/**\n * Lista de temarios predefinidos disponibles\n */ const TEMARIOS_PREDEFINIDOS = [\n    {\n        id: 'a1_2019_junta',\n        nombre: 'Cuerpo Superior Facultativo - Informática (A1.2019)',\n        descripcion: 'Temario completo para oposiciones del Cuerpo Superior Facultativo, opción Informática de la Junta de Andalucía',\n        cuerpo: 'CUERPO SUPERIOR FACULTATIVO, OPCIÓN INFORMÁTICA (A1.2019)',\n        archivo: 'a1_2019_junta.md'\n    },\n    {\n        id: 'c1_junta',\n        nombre: 'Cuerpo General de Administrativos (C1.1000)',\n        descripcion: 'Temario completo para oposiciones del Cuerpo General de Administrativos de la Junta de Andalucía',\n        cuerpo: 'CUERPO GENERAL DE ADMINISTRATIVOS (C1.1000)',\n        archivo: 'c1_junta.md'\n    },\n    {\n        id: 'c2_estado',\n        nombre: 'Cuerpo General Auxiliar del Estado (C2)',\n        descripcion: 'Temario para oposiciones del Cuerpo General Auxiliar del Estado',\n        cuerpo: 'CUERPO GENERAL AUXILIAR DEL ESTADO (C2)',\n        archivo: 'c2_estado.md'\n    },\n    {\n        id: 'c2_junta',\n        nombre: 'Cuerpo General Auxiliar - Junta de Andalucía (C2)',\n        descripcion: 'Temario para oposiciones del Cuerpo General Auxiliar de la Junta de Andalucía',\n        cuerpo: 'CUERPO GENERAL AUXILIAR - JUNTA DE ANDALUCÍA (C2)',\n        archivo: 'c2_junta.md'\n    }\n];\n/**\n * Obtiene la lista de temarios predefinidos disponibles\n */ function obtenerTemariosPredefinidos() {\n    return TEMARIOS_PREDEFINIDOS;\n}\n/**\n * Parsea el contenido de un archivo de temario y extrae los temas\n */ function parsearTemario(contenido) {\n    const temas = [];\n    const lineas = contenido.split('\\n');\n    for(let i = 0; i < lineas.length; i++){\n        const linea = lineas[i].trim();\n        // Patrón 1: \"Tema X.\" (formato estándar)\n        let match = linea.match(/^Tema\\s+(\\d+)\\.\\s*(.+)$/);\n        // Patrón 2: \"X.\" (formato numérico simple)\n        if (!match) {\n            match = linea.match(/^(\\d+)\\.\\s*(.+)$/);\n        }\n        if (match) {\n            const numero = parseInt(match[1]);\n            const titulo = match[2].trim();\n            // Filtrar líneas que no son realmente temas (muy cortas o solo números)\n            if (titulo.length > 10 && !titulo.match(/^[IVX]+\\s*$/)) {\n                temas.push({\n                    numero,\n                    titulo,\n                    descripcion: titulo.length > 100 ? titulo.substring(0, 100) + '...' : titulo\n                });\n            }\n        }\n    }\n    return temas;\n}\n/**\n * Carga un temario predefinido desde el archivo correspondiente\n */ async function cargarTemarioPredefinido(id) {\n    try {\n        const temarioInfo = TEMARIOS_PREDEFINIDOS.find((t)=>t.id === id);\n        if (!temarioInfo) {\n            console.error('Temario predefinido no encontrado:', id);\n            return null;\n        }\n        // Cargar el contenido del archivo\n        const response = await fetch(\"/temarios/\".concat(temarioInfo.archivo));\n        if (!response.ok) {\n            console.error('Error al cargar archivo de temario:', response.status);\n            return null;\n        }\n        const contenido = await response.text();\n        const temas = parsearTemario(contenido);\n        return {\n            ...temarioInfo,\n            temas\n        };\n    } catch (error) {\n        console.error('Error al cargar temario predefinido:', error);\n        return null;\n    }\n}\n/**\n * Obtiene información básica de un temario predefinido sin cargar los temas\n */ function obtenerInfoTemarioPredefinido(id) {\n    return TEMARIOS_PREDEFINIDOS.find((t)=>t.id === id) || null;\n}\n/**\n * Valida que un temario predefinido tenga la estructura correcta\n */ function validarTemarioPredefinido(temario) {\n    if (!temario.id || !temario.nombre || !temario.cuerpo) {\n        return false;\n    }\n    if (!temario.temas || temario.temas.length === 0) {\n        return false;\n    }\n    // Verificar que los temas tengan numeración consecutiva\n    for(let i = 0; i < temario.temas.length; i++){\n        const tema = temario.temas[i];\n        if (!tema.numero || !tema.titulo) {\n            return false;\n        }\n        // Verificar numeración consecutiva (empezando desde 1)\n        if (tema.numero !== i + 1) {\n            console.warn(\"Numeraci\\xf3n no consecutiva en tema \".concat(tema.numero, \", esperado \").concat(i + 1));\n        }\n    }\n    return true;\n}\n/**\n * Convierte un temario predefinido al formato necesario para crear en la base de datos\n */ function convertirTemarioParaCreacion(temario) {\n    return {\n        titulo: temario.nombre,\n        descripcion: \"\".concat(temario.descripcion, \"\\n\\nCuerpo: \").concat(temario.cuerpo),\n        tipo: 'completo',\n        temas: temario.temas.map((tema, index)=>({\n                numero: tema.numero,\n                titulo: tema.titulo,\n                descripcion: tema.descripcion,\n                orden: index + 1\n            }))\n    };\n}\n/**\n * Busca temarios predefinidos por texto\n */ function buscarTemariosPredefinidos(busqueda) {\n    if (!busqueda.trim()) {\n        return TEMARIOS_PREDEFINIDOS;\n    }\n    const termino = busqueda.toLowerCase();\n    return TEMARIOS_PREDEFINIDOS.filter((temario)=>temario.nombre.toLowerCase().includes(termino) || temario.descripcion.toLowerCase().includes(termino) || temario.cuerpo.toLowerCase().includes(termino));\n}\n/**\n * Obtiene estadísticas de un temario predefinido\n */ async function obtenerEstadisticasTemarioPredefinido(id) {\n    try {\n        const temario = await cargarTemarioPredefinido(id);\n        if (!temario) {\n            return null;\n        }\n        return {\n            totalTemas: temario.temas.length,\n            tipoTemario: 'Temario Completo Predefinido',\n            cuerpo: temario.cuerpo\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas:', error);\n        return null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/temario/services/temariosPredefinidosService.ts\n"));

/***/ })

});