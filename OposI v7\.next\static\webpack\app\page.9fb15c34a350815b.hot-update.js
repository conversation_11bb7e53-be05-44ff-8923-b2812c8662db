"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx":
/*!*********************************************************!*\
  !*** ./src/features/dashboard/components/Dashboard.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiBookOpen,FiCalendar,FiCheckSquare,FiClock,FiFileText,FiLayers,FiPlay,FiPlus,FiTarget,FiTrendingUp!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/dashboardService */ \"(app-pages-browser)/./src/lib/supabase/dashboardService.ts\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/temario/components/TemarioSetup */ \"(app-pages-browser)/./src/features/temario/components/TemarioSetup.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Dashboard = (param)=>{\n    let { onNavigateToTab } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [proximasFlashcards, setProximasFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tieneTemario, setTieneTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mostrarSetupTemario, setMostrarSetupTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            const [statsData, proximasData, temarioConfigurado] = await Promise.all([\n                (0,_lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__.obtenerEstadisticasDashboard)(),\n                (0,_lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__.obtenerProximasFlashcards)(5),\n                (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_4__.tieneTemarioConfigurado)()\n            ]);\n            setEstadisticas(statsData);\n            setProximasFlashcards(proximasData);\n            setTieneTemario(temarioConfigurado);\n            // Si no tiene temario configurado, mostrar setup automáticamente\n            if (!temarioConfigurado) {\n                setMostrarSetupTemario(true);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del dashboard:', error);\n            // En caso de error, usar datos por defecto\n            setEstadisticas({\n                totalDocumentos: 0,\n                totalColeccionesFlashcards: 0,\n                totalTests: 0,\n                totalFlashcards: 0,\n                flashcardsParaHoy: 0,\n                flashcardsNuevas: 0,\n                flashcardsAprendiendo: 0,\n                flashcardsRepasando: 0,\n                testsRealizados: 0,\n                porcentajeAcierto: 0,\n                coleccionesRecientes: [],\n                testsRecientes: []\n            });\n            setProximasFlashcards([]);\n            setTieneTemario(false);\n            setMostrarSetupTemario(true);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleTemarioSetupComplete = ()=>{\n        setMostrarSetupTemario(false);\n        setTieneTemario(true);\n        // Recargar datos del dashboard\n        cargarDatos();\n    };\n    const obtenerSaludo = ()=>{\n        const hora = new Date().getHours();\n        if (hora < 12) return 'Buenos días';\n        if (hora < 18) return 'Buenas tardes';\n        return 'Buenas noches';\n    };\n    const obtenerNombreUsuario = ()=>{\n        var _user_email;\n        return (user === null || user === void 0 ? void 0 : (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || 'Estudiante';\n    };\n    // Mostrar setup de temario si es necesario\n    if (mostrarSetupTemario) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            onComplete: handleTemarioSetupComplete\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n            lineNumber: 105,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: [\n                            obtenerSaludo(),\n                            \", \",\n                            obtenerNombreUsuario(),\n                            \"! \\uD83D\\uDC4B\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-100\",\n                        children: \"\\xbfListo para continuar con tu preparaci\\xf3n? Aqu\\xed tienes un resumen de tu progreso.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Documentos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalDocumentos) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiFileText, {\n                                    className: \"h-8 w-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Colecciones\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalColeccionesFlashcards) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiBook, {\n                                    className: \"h-8 w-8 text-emerald-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Tests\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalTests) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiCheckSquare, {\n                                    className: \"h-8 w-8 text-pink-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Flashcards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalFlashcards) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTarget, {\n                                    className: \"h-8 w-8 text-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"Estudio de Hoy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiCalendar, {\n                                className: \"h-6 w-6 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 rounded-lg p-4 border border-orange-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-orange-800\",\n                                                    children: \"Para Repasar Hoy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-orange-600\",\n                                                    children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.flashcardsParaHoy) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiClock, {\n                                            className: \"h-6 w-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4 border border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Nuevas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.flashcardsNuevas) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiBookOpen, {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 rounded-lg p-4 border border-green-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"% Acierto Tests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: [\n                                                        (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.porcentajeAcierto.toFixed(1)) || 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrendingUp, {\n                                            className: \"h-6 w-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, undefined),\n                    estadisticas && estadisticas.flashcardsParaHoy > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onNavigateToTab('misFlashcards'),\n                            className: \"bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPlay, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Comenzar Estudio\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, undefined),\n            proximasFlashcards.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                        children: \"Pr\\xf3ximas Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: proximasFlashcards.slice(0, 3).map((flashcard)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 truncate\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: flashcard.coleccionTitulo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(flashcard.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                            children: flashcard.estado\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, flashcard.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined),\n                    proximasFlashcards.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onNavigateToTab('misFlashcards'),\n                        className: \"mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                        children: \"Ver todas las flashcards pendientes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Colecciones Recientes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.coleccionesRecientes.map((coleccion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: coleccion.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Creada: \",\n                                                            new Date(coleccion.fechaCreacion).toLocaleDateString('es-ES')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        coleccion.paraHoy,\n                                                        \" para hoy\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, coleccion.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('misFlashcards'),\n                                className: \"mt-3 text-emerald-600 hover:text-emerald-800 text-sm font-medium\",\n                                children: \"Ver todas las colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Tests Recientes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.testsRecientes.map((test)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: test.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Creado: \",\n                                                            new Date(test.fechaCreacion).toLocaleDateString('es-ES')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800\",\n                                                    children: [\n                                                        test.numeroPreguntas,\n                                                        \" preguntas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, test.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('misTests'),\n                                className: \"mt-3 text-pink-600 hover:text-pink-800 text-sm font-medium\",\n                                children: \"Ver todos los tests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                        children: \"Acciones R\\xe1pidas\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('preguntas'),\n                                className: \"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiFileText, {\n                                        className: \"h-6 w-6 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-900\",\n                                        children: \"Hacer Preguntas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('flashcards'),\n                                className: \"flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors border border-orange-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPlus, {\n                                        className: \"h-6 w-6 text-orange-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-orange-900\",\n                                        children: \"Crear Flashcards\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('tests'),\n                                className: \"flex items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors border border-indigo-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiCheckSquare, {\n                                        className: \"h-6 w-6 text-indigo-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-indigo-900\",\n                                        children: \"Generar Tests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('mapas'),\n                                className: \"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors border border-purple-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiLayers, {\n                                        className: \"h-6 w-6 text-purple-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-purple-900\",\n                                        children: \"Mapas Mentales\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 318,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"8RATvxkhSGGworVJpPcQz6GoKNY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\n"));

/***/ })

});