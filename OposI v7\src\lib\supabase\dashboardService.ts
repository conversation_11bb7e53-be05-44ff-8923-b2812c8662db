import { supabase } from './supabaseClient';
import { obtenerUsuarioActual } from './authService';
import { obtenerColeccionesFlashcards, obtenerFlashcardsPorColeccionId } from './flashcardsService';
import { obtenerTests, obtenerEstadisticasGeneralesTests } from './testsService';
import { obtenerEstadisticasColeccion } from './estadisticasService';

export interface EstadisticasDashboard {
  // Estadísticas generales
  totalDocumentos: number;
  totalColeccionesFlashcards: number;
  totalTests: number;
  totalFlashcards: number;

  // Flashcards para hoy
  flashcardsParaHoy: number;
  flashcardsNuevas: number;
  flashcardsAprendiendo: number;
  flashcardsRepasando: number;

  // Tests
  testsRealizados: number;
  porcentajeAcierto: number;

  // Actividad reciente
  coleccionesRecientes: {
    id: string;
    titulo: string;
    fechaCreacion: string;
    paraHoy: number;
  }[];

  testsRecientes: {
    id: string;
    titulo: string;
    fechaCreacion: string;
    numeroPreguntas: number;
  }[];
}

export interface ProximasFlashcards {
  id: string;
  pregunta: string;
  coleccionTitulo: string;
  coleccionId: string;
  proximaRevision: string;
  estado: string;
}

/**
 * Obtiene estadísticas generales para el dashboard
 */
export async function obtenerEstadisticasDashboard(): Promise<EstadisticasDashboard> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      throw new Error('Usuario no autenticado');
    }

    // Obtener documentos
    const { data: documentos } = await supabase
      .from('documentos')
      .select('id')
      .eq('user_id', user.id);

    // Obtener colecciones de flashcards
    const colecciones = await obtenerColeccionesFlashcards();

    // Obtener tests
    const tests = await obtenerTests();

    // Obtener estadísticas de tests
    const estadisticasTests = await obtenerEstadisticasGeneralesTests();

    // Calcular estadísticas de flashcards
    let totalFlashcards = 0;
    let flashcardsParaHoy = 0;
    let flashcardsNuevas = 0;
    let flashcardsAprendiendo = 0;
    let flashcardsRepasando = 0;

    const coleccionesConEstadisticas = await Promise.all(
      colecciones.map(async (coleccion) => {
        const stats = await obtenerEstadisticasColeccion(coleccion.id);
        totalFlashcards += stats.total;
        flashcardsParaHoy += stats.paraHoy;
        flashcardsNuevas += stats.nuevas;
        flashcardsAprendiendo += stats.aprendiendo;
        flashcardsRepasando += stats.repasando;

        return {
          id: coleccion.id,
          titulo: coleccion.titulo,
          fechaCreacion: coleccion.creado_en,
          paraHoy: stats.paraHoy
        };
      })
    );

    // Ordenar colecciones por fecha de creación (más recientes primero)
    const coleccionesRecientes = coleccionesConEstadisticas
      .sort((a, b) => new Date(b.fechaCreacion).getTime() - new Date(a.fechaCreacion).getTime())
      .slice(0, 5);

    // Preparar tests recientes
    const testsRecientes = tests
      .map(test => ({
        id: test.id,
        titulo: test.titulo,
        fechaCreacion: test.creado_en,
        numeroPreguntas: test.numero_preguntas || 0
      }))
      .slice(0, 5);

    return {
      totalDocumentos: documentos?.length || 0,
      totalColeccionesFlashcards: colecciones.length,
      totalTests: tests.length,
      totalFlashcards,
      flashcardsParaHoy,
      flashcardsNuevas,
      flashcardsAprendiendo,
      flashcardsRepasando,
      testsRealizados: estadisticasTests.totalTests,
      porcentajeAcierto: estadisticasTests.porcentajeAcierto,
      coleccionesRecientes,
      testsRecientes
    };

  } catch (error) {
    console.error('Error al obtener estadísticas del dashboard:', error);
    return {
      totalDocumentos: 0,
      totalColeccionesFlashcards: 0,
      totalTests: 0,
      totalFlashcards: 0,
      flashcardsParaHoy: 0,
      flashcardsNuevas: 0,
      flashcardsAprendiendo: 0,
      flashcardsRepasando: 0,
      testsRealizados: 0,
      porcentajeAcierto: 0,
      coleccionesRecientes: [],
      testsRecientes: []
    };
  }
}

/**
 * Obtiene las próximas flashcards a repasar
 */
export async function obtenerProximasFlashcards(limite: number = 10): Promise<ProximasFlashcards[]> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      return [];
    }

    // Obtener colecciones del usuario
    const colecciones = await obtenerColeccionesFlashcards();

    if (colecciones.length === 0) {
      return [];
    }

    const hoy = new Date();
    hoy.setHours(23, 59, 59, 999); // Final del día

    // Obtener progreso de flashcards que deben estudiarse hoy
    const { data: progresosHoy, error: errorProgreso } = await supabase
      .from('progreso_flashcards')
      .select('flashcard_id, proxima_revision, estado')
      .lte('proxima_revision', hoy.toISOString())
      .order('proxima_revision', { ascending: true })
      .limit(limite);

    if (errorProgreso) {
      console.error('Error al obtener progreso de flashcards:', errorProgreso);
      return [];
    }

    if (!progresosHoy || progresosHoy.length === 0) {
      return [];
    }

    // Obtener las flashcards correspondientes
    const flashcardIds = progresosHoy.map(p => p.flashcard_id);
    const { data: flashcards, error: errorFlashcards } = await supabase
      .from('flashcards')
      .select('id, pregunta, coleccion_id')
      .in('id', flashcardIds);

    if (errorFlashcards) {
      console.error('Error al obtener flashcards:', errorFlashcards);
      return [];
    }

    // Mapear los resultados
    const resultado: ProximasFlashcards[] = [];

    for (const progreso of progresosHoy) {
      const flashcard = flashcards?.find(f => f.id === progreso.flashcard_id);

      if (flashcard) {
        const coleccion = colecciones.find(c => c.id === flashcard.coleccion_id);

        if (coleccion) {
          resultado.push({
            id: flashcard.id,
            pregunta: flashcard.pregunta,
            coleccionTitulo: coleccion.titulo,
            coleccionId: coleccion.id,
            proximaRevision: progreso.proxima_revision,
            estado: progreso.estado || 'nuevo'
          });
        }
      }
    }

    return resultado;

  } catch (error) {
    console.error('Error al obtener próximas flashcards:', error);
    return [];
  }
}
