"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/temario/services/temariosPredefinidosService.ts":
/*!**********************************************************************!*\
  !*** ./src/features/temario/services/temariosPredefinidosService.ts ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TEMARIOS_PREDEFINIDOS: () => (/* binding */ TEMARIOS_PREDEFINIDOS),\n/* harmony export */   buscarTemariosPredefinidos: () => (/* binding */ buscarTemariosPredefinidos),\n/* harmony export */   cargarTemarioPredefinido: () => (/* binding */ cargarTemarioPredefinido),\n/* harmony export */   convertirTemarioParaCreacion: () => (/* binding */ convertirTemarioParaCreacion),\n/* harmony export */   obtenerEstadisticasTemarioPredefinido: () => (/* binding */ obtenerEstadisticasTemarioPredefinido),\n/* harmony export */   obtenerInfoTemarioPredefinido: () => (/* binding */ obtenerInfoTemarioPredefinido),\n/* harmony export */   obtenerTemariosPredefinidos: () => (/* binding */ obtenerTemariosPredefinidos),\n/* harmony export */   parsearTemario: () => (/* binding */ parsearTemario),\n/* harmony export */   validarTemarioPredefinido: () => (/* binding */ validarTemarioPredefinido)\n/* harmony export */ });\n/**\n * Lista de temarios predefinidos disponibles\n */ const TEMARIOS_PREDEFINIDOS = [\n    {\n        id: 'a1_2019_junta',\n        nombre: 'Cuerpo Superior Facultativo - Informática (A1.2019)',\n        descripcion: 'Temario completo para oposiciones del Cuerpo Superior Facultativo, opción Informática de la Junta de Andalucía',\n        cuerpo: 'CUERPO SUPERIOR FACULTATIVO, OPCIÓN INFORMÁTICA (A1.2019)',\n        archivo: 'a1_2019_junta.md'\n    },\n    {\n        id: 'c1_junta',\n        nombre: 'Cuerpo General de Administrativos (C1.1000)',\n        descripcion: 'Temario completo para oposiciones del Cuerpo General de Administrativos de la Junta de Andalucía',\n        cuerpo: 'CUERPO GENERAL DE ADMINISTRATIVOS (C1.1000)',\n        archivo: 'c1_junta.md'\n    },\n    {\n        id: 'c2_estado',\n        nombre: 'Cuerpo General Auxiliar del Estado (C2)',\n        descripcion: 'Temario para oposiciones del Cuerpo General Auxiliar del Estado',\n        cuerpo: 'CUERPO GENERAL AUXILIAR DEL ESTADO (C2)',\n        archivo: 'c2_estado.md'\n    },\n    {\n        id: 'c2_junta',\n        nombre: 'Cuerpo General Auxiliar - Junta de Andalucía (C2)',\n        descripcion: 'Temario para oposiciones del Cuerpo General Auxiliar de la Junta de Andalucía',\n        cuerpo: 'CUERPO GENERAL AUXILIAR - JUNTA DE ANDALUCÍA (C2)',\n        archivo: 'c2_junta.md'\n    }\n];\n/**\n * Obtiene la lista de temarios predefinidos disponibles\n */ function obtenerTemariosPredefinidos() {\n    return TEMARIOS_PREDEFINIDOS;\n}\n/**\n * Parsea el contenido de un archivo de temario y extrae los temas\n */ function parsearTemario(contenido) {\n    const temas = [];\n    const lineas = contenido.split('\\n');\n    console.log('Parseando temario, total de líneas:', lineas.length);\n    for(let i = 0; i < lineas.length; i++){\n        const linea = lineas[i].trim();\n        // Buscar líneas que empiecen con \"Tema X.\" donde X es un número\n        const match = linea.match(/^Tema\\s+(\\d+)\\.\\s*(.+)$/);\n        if (match) {\n            const numero = parseInt(match[1]);\n            const titulo = match[2].trim();\n            console.log(\"Encontrado tema \".concat(numero, \": \").concat(titulo.substring(0, 50), \"...\"));\n            temas.push({\n                numero,\n                titulo,\n                descripcion: titulo.length > 100 ? titulo.substring(0, 100) + '...' : titulo\n            });\n        }\n    }\n    console.log('Total de temas encontrados:', temas.length);\n    return temas;\n}\n/**\n * Carga un temario predefinido desde el archivo correspondiente\n */ async function cargarTemarioPredefinido(id) {\n    try {\n        const temarioInfo = TEMARIOS_PREDEFINIDOS.find((t)=>t.id === id);\n        if (!temarioInfo) {\n            console.error('Temario predefinido no encontrado:', id);\n            return null;\n        }\n        // Cargar el contenido del archivo\n        const response = await fetch(\"/temarios/\".concat(temarioInfo.archivo));\n        if (!response.ok) {\n            console.error('Error al cargar archivo de temario:', response.status);\n            return null;\n        }\n        const contenido = await response.text();\n        const temas = parsearTemario(contenido);\n        return {\n            ...temarioInfo,\n            temas\n        };\n    } catch (error) {\n        console.error('Error al cargar temario predefinido:', error);\n        return null;\n    }\n}\n/**\n * Obtiene información básica de un temario predefinido sin cargar los temas\n */ function obtenerInfoTemarioPredefinido(id) {\n    return TEMARIOS_PREDEFINIDOS.find((t)=>t.id === id) || null;\n}\n/**\n * Valida que un temario predefinido tenga la estructura correcta\n */ function validarTemarioPredefinido(temario) {\n    if (!temario.id || !temario.nombre || !temario.cuerpo) {\n        return false;\n    }\n    if (!temario.temas || temario.temas.length === 0) {\n        return false;\n    }\n    // Verificar que los temas tengan numeración consecutiva\n    for(let i = 0; i < temario.temas.length; i++){\n        const tema = temario.temas[i];\n        if (!tema.numero || !tema.titulo) {\n            return false;\n        }\n        // Verificar numeración consecutiva (empezando desde 1)\n        if (tema.numero !== i + 1) {\n            console.warn(\"Numeraci\\xf3n no consecutiva en tema \".concat(tema.numero, \", esperado \").concat(i + 1));\n        }\n    }\n    return true;\n}\n/**\n * Convierte un temario predefinido al formato necesario para crear en la base de datos\n */ function convertirTemarioParaCreacion(temario) {\n    return {\n        titulo: temario.nombre,\n        descripcion: \"\".concat(temario.descripcion, \"\\n\\nCuerpo: \").concat(temario.cuerpo),\n        tipo: 'completo',\n        temas: temario.temas.map((tema, index)=>({\n                numero: tema.numero,\n                titulo: tema.titulo,\n                descripcion: tema.descripcion,\n                orden: index + 1\n            }))\n    };\n}\n/**\n * Busca temarios predefinidos por texto\n */ function buscarTemariosPredefinidos(busqueda) {\n    if (!busqueda.trim()) {\n        return TEMARIOS_PREDEFINIDOS;\n    }\n    const termino = busqueda.toLowerCase();\n    return TEMARIOS_PREDEFINIDOS.filter((temario)=>temario.nombre.toLowerCase().includes(termino) || temario.descripcion.toLowerCase().includes(termino) || temario.cuerpo.toLowerCase().includes(termino));\n}\n/**\n * Obtiene estadísticas de un temario predefinido\n */ async function obtenerEstadisticasTemarioPredefinido(id) {\n    try {\n        const temario = await cargarTemarioPredefinido(id);\n        if (!temario) {\n            return null;\n        }\n        return {\n            totalTemas: temario.temas.length,\n            tipoTemario: 'Temario Completo Predefinido',\n            cuerpo: temario.cuerpo\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas:', error);\n        return null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/temario/services/temariosPredefinidosService.ts\n"));

/***/ })

});