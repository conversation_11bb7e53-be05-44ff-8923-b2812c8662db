# 📚 OposicionesIA - Guía de Instalación

## 🎯 Descripción
OposicionesIA es una aplicación web completa para la preparación de oposiciones que utiliza IA (Google Gemini) para generar materiales de estudio. Incluye gestión de documentos, generación de Q&A, flashcards con repetición espaciada, mapas mentales, tests y estadísticas completas.

## 🔧 Requisitos Previos

### Software Necesario:
- **Node.js** (versión 18 o superior) - [Descargar aquí](https://nodejs.org/)
- **npm** (incluido con Node.js)
- **Git** (opcional, para control de versiones)

### Servicios Externos:
- **Cuenta de Supabase** - [Crear cuenta gratuita](https://supabase.com/)
- **API Key de Google Gemini** - [Obtener aquí](https://makersuite.google.com/app/apikey)

## 📦 Instalación

### 1. Extraer el Proyecto
```bash
# Extraer el archivo ZIP en la ubicación deseada
# Navegar al directorio del proyecto
cd OposI-v7
```

### 2. Instalar Dependencias
```bash
# Instalar todas las dependencias del proyecto
npm install
```

### 3. Configurar Variables de Entorno

Crear un archivo `.env.local` en la raíz del proyecto con el siguiente contenido:

```env
# Configuración de Supabase
NEXT_PUBLIC_SUPABASE_URL=tu_supabase_url_aqui
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_supabase_anon_key_aqui

# API Key de Google Gemini
GEMINI_API_KEY=tu_gemini_api_key_aqui
```

### 4. Configurar Supabase

#### 4.1 Crear Proyecto en Supabase
1. Ve a [Supabase](https://supabase.com/)
2. Crea una nueva cuenta o inicia sesión
3. Crea un nuevo proyecto
4. Anota la URL y la clave anónima del proyecto

#### 4.2 Configurar Base de Datos
Ejecuta los siguientes scripts SQL en el editor SQL de Supabase:

```sql
-- Crear tabla de documentos
CREATE TABLE documentos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  titulo TEXT NOT NULL,
  contenido TEXT NOT NULL,
  categoria TEXT,
  numero_tema INTEGER,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  actualizado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de conversaciones
CREATE TABLE conversaciones (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  titulo TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  actualizado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de mensajes
CREATE TABLE mensajes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  conversacion_id UUID REFERENCES conversaciones(id) ON DELETE CASCADE,
  contenido TEXT NOT NULL,
  es_usuario BOOLEAN NOT NULL,
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de colecciones de flashcards
CREATE TABLE colecciones_flashcards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  titulo TEXT NOT NULL,
  descripcion TEXT,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de flashcards
CREATE TABLE flashcards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  coleccion_id UUID REFERENCES colecciones_flashcards(id) ON DELETE CASCADE,
  pregunta TEXT NOT NULL,
  respuesta TEXT NOT NULL,
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  actualizado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de progreso de flashcards
CREATE TABLE progreso_flashcards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  flashcard_id UUID REFERENCES flashcards(id) ON DELETE CASCADE,
  factor_facilidad DECIMAL DEFAULT 2.5,
  intervalo INTEGER DEFAULT 0,
  repeticiones INTEGER DEFAULT 0,
  estado TEXT DEFAULT 'nuevo',
  ultima_revision TIMESTAMP WITH TIME ZONE,
  proxima_revision TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de historial de revisiones
CREATE TABLE historial_revisiones (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  flashcard_id UUID REFERENCES flashcards(id) ON DELETE CASCADE,
  dificultad TEXT NOT NULL,
  factor_facilidad DECIMAL,
  intervalo INTEGER,
  repeticiones INTEGER,
  fecha TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de tests
CREATE TABLE tests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  titulo TEXT NOT NULL,
  descripcion TEXT,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  documentos_ids TEXT[],
  numero_preguntas INTEGER,
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de preguntas de test
CREATE TABLE preguntas_test (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  test_id UUID REFERENCES tests(id) ON DELETE CASCADE,
  pregunta TEXT NOT NULL,
  opcion_a TEXT NOT NULL,
  opcion_b TEXT NOT NULL,
  opcion_c TEXT NOT NULL,
  opcion_d TEXT NOT NULL,
  respuesta_correcta CHAR(1) NOT NULL,
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de estadísticas de test
CREATE TABLE estadisticas_test (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  test_id UUID REFERENCES tests(id) ON DELETE CASCADE,
  pregunta_id UUID REFERENCES preguntas_test(id) ON DELETE CASCADE,
  respuesta_seleccionada CHAR(1) NOT NULL,
  es_correcta BOOLEAN NOT NULL,
  tiempo_respuesta INTEGER,
  fecha_respuesta TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Habilitar RLS (Row Level Security)
ALTER TABLE documentos ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversaciones ENABLE ROW LEVEL SECURITY;
ALTER TABLE mensajes ENABLE ROW LEVEL SECURITY;
ALTER TABLE colecciones_flashcards ENABLE ROW LEVEL SECURITY;
ALTER TABLE flashcards ENABLE ROW LEVEL SECURITY;
ALTER TABLE progreso_flashcards ENABLE ROW LEVEL SECURITY;
ALTER TABLE historial_revisiones ENABLE ROW LEVEL SECURITY;
ALTER TABLE tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE preguntas_test ENABLE ROW LEVEL SECURITY;
ALTER TABLE estadisticas_test ENABLE ROW LEVEL SECURITY;
ALTER TABLE temarios ENABLE ROW LEVEL SECURITY;
ALTER TABLE temas ENABLE ROW LEVEL SECURITY;
ALTER TABLE planificacion_estudio ENABLE ROW LEVEL SECURITY;

-- Crear tabla de temarios
CREATE TABLE temarios (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  titulo TEXT NOT NULL,
  descripcion TEXT,
  tipo TEXT NOT NULL CHECK (tipo IN ('completo', 'temas_sueltos')),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  actualizado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de temas
CREATE TABLE temas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  temario_id UUID REFERENCES temarios(id) ON DELETE CASCADE,
  numero INTEGER NOT NULL,
  titulo TEXT NOT NULL,
  descripcion TEXT,
  orden INTEGER NOT NULL,
  completado BOOLEAN DEFAULT FALSE,
  fecha_completado TIMESTAMP WITH TIME ZONE,
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  actualizado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de planificación de estudio
CREATE TABLE planificacion_estudio (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  temario_id UUID REFERENCES temarios(id) ON DELETE CASCADE,
  tema_id UUID REFERENCES temas(id) ON DELETE CASCADE,
  fecha_planificada DATE NOT NULL,
  tiempo_estimado INTEGER, -- en minutos
  completado BOOLEAN DEFAULT FALSE,
  fecha_completado TIMESTAMP WITH TIME ZONE,
  notas TEXT,
  creado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  actualizado_en TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear políticas RLS
CREATE POLICY "Users can only see their own documents" ON documentos FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can only see their own conversations" ON conversaciones FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can only see their own flashcard collections" ON colecciones_flashcards FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can only see their own tests" ON tests FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can only see their own temarios" ON temarios FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can only see their own temas" ON temas FOR ALL USING (
  EXISTS (SELECT 1 FROM temarios WHERE temarios.id = temas.temario_id AND temarios.user_id = auth.uid())
);
CREATE POLICY "Users can only see their own planificacion" ON planificacion_estudio FOR ALL USING (
  EXISTS (SELECT 1 FROM temarios WHERE temarios.id = planificacion_estudio.temario_id AND temarios.user_id = auth.uid())
);

-- Políticas para tablas relacionadas
CREATE POLICY "Users can access messages from their conversations" ON mensajes FOR ALL USING (
  EXISTS (SELECT 1 FROM conversaciones WHERE conversaciones.id = mensajes.conversacion_id AND conversaciones.user_id = auth.uid())
);

CREATE POLICY "Users can access flashcards from their collections" ON flashcards FOR ALL USING (
  EXISTS (SELECT 1 FROM colecciones_flashcards WHERE colecciones_flashcards.id = flashcards.coleccion_id AND colecciones_flashcards.user_id = auth.uid())
);

CREATE POLICY "Users can access progress from their flashcards" ON progreso_flashcards FOR ALL USING (
  EXISTS (
    SELECT 1 FROM flashcards 
    JOIN colecciones_flashcards ON flashcards.coleccion_id = colecciones_flashcards.id 
    WHERE flashcards.id = progreso_flashcards.flashcard_id AND colecciones_flashcards.user_id = auth.uid()
  )
);

CREATE POLICY "Users can access history from their flashcards" ON historial_revisiones FOR ALL USING (
  EXISTS (
    SELECT 1 FROM flashcards 
    JOIN colecciones_flashcards ON flashcards.coleccion_id = colecciones_flashcards.id 
    WHERE flashcards.id = historial_revisiones.flashcard_id AND colecciones_flashcards.user_id = auth.uid()
  )
);

CREATE POLICY "Users can access questions from their tests" ON preguntas_test FOR ALL USING (
  EXISTS (SELECT 1 FROM tests WHERE tests.id = preguntas_test.test_id AND tests.user_id = auth.uid())
);

CREATE POLICY "Users can access statistics from their tests" ON estadisticas_test FOR ALL USING (
  EXISTS (SELECT 1 FROM tests WHERE tests.id = estadisticas_test.test_id AND tests.user_id = auth.uid())
);
```

### 5. Obtener API Key de Google Gemini
1. Ve a [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Inicia sesión con tu cuenta de Google
3. Crea una nueva API Key
4. Copia la clave y agrégala al archivo `.env.local`

## 🚀 Ejecutar la Aplicación

### Modo Desarrollo
```bash
npm run dev
```

La aplicación estará disponible en: `http://localhost:3000`

### Modo Producción
```bash
# Construir la aplicación
npm run build

# Ejecutar en producción
npm start
```

## 🔐 Primer Uso

1. **Registro de Usuario:**
   - Ve a la página de login
   - Crea una nueva cuenta con email y contraseña
   - Confirma tu email (si está habilitado en Supabase)

2. **Subir Documentos:**
   - Ve a "Gestionar Documentos"
   - Sube archivos PDF o TXT con material de estudio

3. **Generar Contenido:**
   - Usa la IA para generar Q&A, flashcards, mapas mentales y tests
   - Todo el contenido se guarda automáticamente

## 🔧 Características Principales

- **📄 Gestión de Documentos**: Subida y organización de PDFs y archivos de texto
- **🤖 Q&A con IA**: Conversaciones inteligentes sobre el material de estudio
- **🃏 Flashcards**: Sistema de repetición espaciada para memorización efectiva
- **🧠 Mapas Mentales**: Visualización interactiva de conceptos
- **📝 Tests**: Generación y realización de exámenes personalizados
- **📊 Estadísticas**: Seguimiento detallado del progreso de estudio
- **🔐 Seguridad**: Desconexión automática tras 5 minutos de inactividad
- **📱 Responsive**: Funciona en dispositivos móviles y desktop

## 🛠️ Solución de Problemas

### Error de Conexión a Supabase
- Verifica que las URLs y claves en `.env.local` sean correctas
- Asegúrate de que el proyecto de Supabase esté activo

### Error de API de Gemini
- Verifica que la API Key sea válida
- Comprueba que tengas créditos disponibles en Google AI Studio

### Problemas de Instalación
- Asegúrate de tener Node.js 18 o superior
- Elimina `node_modules` y ejecuta `npm install` nuevamente

## 📞 Soporte

Para problemas técnicos o preguntas:
- Revisa la documentación de [Next.js](https://nextjs.org/docs)
- Consulta la documentación de [Supabase](https://supabase.com/docs)
- Revisa la documentación de [Google Gemini](https://ai.google.dev/docs)

---

**¡Disfruta estudiando con OposicionesIA! 🎓**
