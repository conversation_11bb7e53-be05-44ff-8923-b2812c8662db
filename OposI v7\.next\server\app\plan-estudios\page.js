/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/plan-estudios/page";
exports.ids = ["app/plan-estudios/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fplan-estudios%2Fpage&page=%2Fplan-estudios%2Fpage&appPaths=%2Fplan-estudios%2Fpage&pagePath=private-next-app-dir%2Fplan-estudios%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fplan-estudios%2Fpage&page=%2Fplan-estudios%2Fpage&appPaths=%2Fplan-estudios%2Fpage&pagePath=private-next-app-dir%2Fplan-estudios%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/plan-estudios/page.tsx */ \"(rsc)/./src/app/plan-estudios/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'plan-estudios',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/plan-estudios/page\",\n        pathname: \"/plan-estudios\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fplan-estudios%2Fpage&page=%2Fplan-estudios%2Fpage&appPaths=%2Fplan-estudios%2Fpage&pagePath=private-next-app-dir%2Fplan-estudios%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2ZlYXR1cmVzJTVDJTVDc2hhcmVkJTVDJTVDY29tcG9uZW50cyU1QyU1Q0NsaWVudExheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4TUFBcUwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFxPcG9zSSB2N1xcXFxzcmNcXFxcZmVhdHVyZXNcXFxcc2hhcmVkXFxcXGNvbXBvbmVudHNcXFxcQ2xpZW50TGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cplan-estudios%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cplan-estudios%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/plan-estudios/page.tsx */ \"(rsc)/./src/app/plan-estudios/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BsYW4tZXN0dWRpb3MlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEtBQXNJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFxPcG9zSSB2N1xcXFxzcmNcXFxcYXBwXFxcXHBsYW4tZXN0dWRpb3NcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cplan-estudios%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b63ad14717ba\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYjYzYWQxNDcxN2JhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/shared/components/ClientLayout */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\");\n\n\n\nconst metadata = {\n    title: 'OposiAI - Asistente IA para Oposiciones',\n    description: 'Aplicación de preguntas y respuestas con IA para temarios de oposiciones'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN1QjtBQUM4QztBQUU5RCxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNkLDRFQUFDVCxnRkFBWUE7MEJBQ1ZLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXE9wb3NJIHY3XFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnO1xuaW1wb3J0IENsaWVudExheW91dCBmcm9tICdAL2ZlYXR1cmVzL3NoYXJlZC9jb21wb25lbnRzL0NsaWVudExheW91dCc7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnT3Bvc2lBSSAtIEFzaXN0ZW50ZSBJQSBwYXJhIE9wb3NpY2lvbmVzJyxcbiAgZGVzY3JpcHRpb246ICdBcGxpY2FjacOzbiBkZSBwcmVndW50YXMgeSByZXNwdWVzdGFzIGNvbiBJQSBwYXJhIHRlbWFyaW9zIGRlIG9wb3NpY2lvbmVzJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVzXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJmb250LXNhbnMgYmctZ3JheS0xMDBcIj5cbiAgICAgICAgPENsaWVudExheW91dD5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvQ2xpZW50TGF5b3V0PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJDbGllbnRMYXlvdXQiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/plan-estudios/page.tsx":
/*!****************************************!*\
  !*** ./src/app/plan-estudios/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\app\\plan-estudios\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\features\\shared\\components\\ClientLayout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(ssr)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2ZlYXR1cmVzJTVDJTVDc2hhcmVkJTVDJTVDY29tcG9uZW50cyU1QyU1Q0NsaWVudExheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4TUFBcUwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFxPcG9zSSB2N1xcXFxzcmNcXFxcZmVhdHVyZXNcXFxcc2hhcmVkXFxcXGNvbXBvbmVudHNcXFxcQ2xpZW50TGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cplan-estudios%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cplan-estudios%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/plan-estudios/page.tsx */ \"(ssr)/./src/app/plan-estudios/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BsYW4tZXN0dWRpb3MlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEtBQXNJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFxPcG9zSSB2N1xcXFxzcmNcXFxcYXBwXFxcXHBsYW4tZXN0dWRpb3NcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cplan-estudios%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/plan-estudios/page.tsx":
/*!****************************************!*\
  !*** ./src/app/plan-estudios/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiDownload_FiPrint_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiDownload,FiPrint,FiRefreshCw,FiTarget!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiDownload_FiPrint_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiDownload,FiPrint,FiRefreshCw,FiTarget!=!react-icons/fi */ \"(ssr)/__barrel_optimize__?names=FiCalendar,FiDownload,FiPrint,FiRefreshCw,FiTarget!=!./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(ssr)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(ssr)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst PlanEstudiosPage = ()=>{\n    const [temario, setTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [planGenerado, setPlanGenerado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlanEstudiosPage.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"PlanEstudiosPage.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            // Obtener temario del usuario\n            const temarioData = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemarioUsuario)();\n            if (!temarioData) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('No se encontró un temario configurado');\n                return;\n            }\n            setTemario(temarioData);\n            // Verificar si tiene planificación configurada\n            const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__.tienePlanificacionConfigurada)(temarioData.id);\n            setTienePlanificacion(tienePlan);\n            if (!tienePlan) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Necesitas configurar tu planificación antes de generar el plan de estudios');\n                return;\n            }\n        } catch (error) {\n            console.error('Error al cargar datos:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al cargar los datos');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGenerarPlan = async ()=>{\n        if (!temario) return;\n        setIsGenerating(true);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.loading('Generando tu plan de estudios personalizado con IA...');\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'generarPlanEstudios',\n                    temarioId: temario.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`Error en la API: ${response.status}`);\n            }\n            const { result } = await response.json();\n            setPlanGenerado(result);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('¡Plan de estudios generado exitosamente!', {\n                id: loadingToastId\n            });\n        } catch (error) {\n            console.error('Error al generar plan:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.', {\n                id: loadingToastId\n            });\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const handleRegenerarPlan = ()=>{\n        setPlanGenerado('');\n        handleGenerarPlan();\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planGenerado) return;\n        const blob = new Blob([\n            planGenerado\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `plan-estudios-${temario?.titulo || 'temario'}.md`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('Plan descargado exitosamente');\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planGenerado) return;\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write(`\n        <html>\n          <head>\n            <title>Plan de Estudios - ${temario?.titulo}</title>\n            <style>\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\n              h1, h2, h3 { color: #333; }\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\n              ul, ol { margin-left: 20px; }\n              strong { color: #2563eb; }\n              @media print { body { margin: 0; } }\n            </style>\n          </head>\n          <body>\n            <div id=\"content\"></div>\n            <script>\n              // Convertir markdown a HTML básico para impresión\n              const markdown = ${JSON.stringify(planGenerado)};\n              const content = markdown\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\n                .replace(/\\\\n/g, '<br>');\n              document.getElementById('content').innerHTML = content;\n              window.print();\n            </script>\n          </body>\n        </html>\n      `);\n            printWindow.document.close();\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando datos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!temario || !tienePlanificacion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrint_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {\n                            className: \"w-8 h-8 text-yellow-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Configuraci\\xf3n Requerida\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Para generar tu plan de estudios personalizado, necesitas:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-left text-sm text-gray-600 mb-6 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-blue-400 rounded-full mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Tener un temario configurado\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-blue-400 rounded-full mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Completar la planificaci\\xf3n inteligente\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/temario\",\n                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrint_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Ir a Mi Temario\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                        children: \"Mi Plan de Estudios\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"Plan personalizado generado con IA para: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: temario.titulo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 58\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: planGenerado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleRegenerarPlan,\n                                            disabled: isGenerating,\n                                            className: \"flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrint_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiRefreshCw, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Regenerar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDescargarPlan,\n                                            className: \"flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrint_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiDownload, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Descargar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleImprimirPlan,\n                                            className: \"flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrint_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPrint, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Imprimir\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, undefined),\n                !planGenerado ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrint_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCalendar, {\n                                className: \"w-10 h-10 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                            children: \"Genera tu Plan de Estudios Personalizado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                            children: \"Nuestro asistente de IA analizar\\xe1 tu planificaci\\xf3n, disponibilidad de tiempo, y las caracter\\xedsticas de cada tema para crear un plan de estudios completamente personalizado y realista.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGenerarPlan,\n                            disabled: isGenerating,\n                            className: \"flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 mx-auto\",\n                            children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Generando plan con IA...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrint_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {\n                                        className: \"w-5 h-5 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Generar Plan de Estudios\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-blue max-w-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_7__.Markdown, {\n                            children: planGenerado\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlanEstudiosPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BsYW4tZXN0dWRpb3MvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQzhDO0FBQ2Q7QUFDb0I7QUFFL0Q7QUFDRztBQUUzQyxNQUFNWSxtQkFBNkI7SUFDakMsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdiLCtDQUFRQSxDQUFpQjtJQUN2RCxNQUFNLENBQUNjLGNBQWNDLGdCQUFnQixHQUFHZiwrQ0FBUUEsQ0FBUztJQUN6RCxNQUFNLENBQUNnQixXQUFXQyxhQUFhLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNrQixjQUFjQyxnQkFBZ0IsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ29CLG9CQUFvQkMsc0JBQXNCLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUU3REMsZ0RBQVNBO3NDQUFDO1lBQ1JxQjtRQUNGO3FDQUFHLEVBQUU7SUFFTCxNQUFNQSxjQUFjO1FBQ2xCTCxhQUFhO1FBQ2IsSUFBSTtZQUNGLDhCQUE4QjtZQUM5QixNQUFNTSxjQUFjLE1BQU1oQixnR0FBcUJBO1lBQy9DLElBQUksQ0FBQ2dCLGFBQWE7Z0JBQ2hCZCxrREFBS0EsQ0FBQ2UsS0FBSyxDQUFDO2dCQUNaO1lBQ0Y7WUFDQVgsV0FBV1U7WUFFWCwrQ0FBK0M7WUFDL0MsTUFBTUUsWUFBWSxNQUFNakIsb0hBQTZCQSxDQUFDZSxZQUFZRyxFQUFFO1lBQ3BFTCxzQkFBc0JJO1lBRXRCLElBQUksQ0FBQ0EsV0FBVztnQkFDZGhCLGtEQUFLQSxDQUFDZSxLQUFLLENBQUM7Z0JBQ1o7WUFDRjtRQUVGLEVBQUUsT0FBT0EsT0FBTztZQUNkRyxRQUFRSCxLQUFLLENBQUMsMEJBQTBCQTtZQUN4Q2Ysa0RBQUtBLENBQUNlLEtBQUssQ0FBQztRQUNkLFNBQVU7WUFDUlAsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNVyxvQkFBb0I7UUFDeEIsSUFBSSxDQUFDaEIsU0FBUztRQUVkTyxnQkFBZ0I7UUFDaEIsSUFBSVU7UUFFSixJQUFJO1lBQ0ZBLGlCQUFpQnBCLGtEQUFLQSxDQUFDcUIsT0FBTyxDQUFDO1lBRS9CLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSxlQUFlO2dCQUMxQ0MsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxRQUFRO29CQUNSQyxXQUFXM0IsUUFBUWMsRUFBRTtnQkFDdkI7WUFDRjtZQUVBLElBQUksQ0FBQ0ssU0FBU1MsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0sQ0FBQyxpQkFBaUIsRUFBRVYsU0FBU1csTUFBTSxFQUFFO1lBQ3ZEO1lBRUEsTUFBTSxFQUFFQyxNQUFNLEVBQUUsR0FBRyxNQUFNWixTQUFTYSxJQUFJO1lBQ3RDN0IsZ0JBQWdCNEI7WUFFaEJsQyxrREFBS0EsQ0FBQ29DLE9BQU8sQ0FBQyw0Q0FBNEM7Z0JBQUVuQixJQUFJRztZQUFlO1FBQ2pGLEVBQUUsT0FBT0wsT0FBTztZQUNkRyxRQUFRSCxLQUFLLENBQUMsMEJBQTBCQTtZQUN4Q2Ysa0RBQUtBLENBQUNlLEtBQUssQ0FBQyw2REFBNkQ7Z0JBQUVFLElBQUlHO1lBQWU7UUFDaEcsU0FBVTtZQUNSVixnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLE1BQU0yQixzQkFBc0I7UUFDMUIvQixnQkFBZ0I7UUFDaEJhO0lBQ0Y7SUFFQSxNQUFNbUIsc0JBQXNCO1FBQzFCLElBQUksQ0FBQ2pDLGNBQWM7UUFFbkIsTUFBTWtDLE9BQU8sSUFBSUMsS0FBSztZQUFDbkM7U0FBYSxFQUFFO1lBQUVvQyxNQUFNO1FBQWdCO1FBQzlELE1BQU1DLE1BQU1DLElBQUlDLGVBQWUsQ0FBQ0w7UUFDaEMsTUFBTU0sSUFBSUMsU0FBU0MsYUFBYSxDQUFDO1FBQ2pDRixFQUFFRyxJQUFJLEdBQUdOO1FBQ1RHLEVBQUVJLFFBQVEsR0FBRyxDQUFDLGNBQWMsRUFBRTlDLFNBQVMrQyxVQUFVLFVBQVUsR0FBRyxDQUFDO1FBQy9ESixTQUFTcEIsSUFBSSxDQUFDeUIsV0FBVyxDQUFDTjtRQUMxQkEsRUFBRU8sS0FBSztRQUNQTixTQUFTcEIsSUFBSSxDQUFDMkIsV0FBVyxDQUFDUjtRQUMxQkYsSUFBSVcsZUFBZSxDQUFDWjtRQUVwQjFDLGtEQUFLQSxDQUFDb0MsT0FBTyxDQUFDO0lBQ2hCO0lBRUEsTUFBTW1CLHFCQUFxQjtRQUN6QixJQUFJLENBQUNsRCxjQUFjO1FBRW5CLE1BQU1tRCxjQUFjQyxPQUFPQyxJQUFJLENBQUMsSUFBSTtRQUNwQyxJQUFJRixhQUFhO1lBQ2ZBLFlBQVlWLFFBQVEsQ0FBQ2EsS0FBSyxDQUFDLENBQUM7OztzQ0FHSSxFQUFFeEQsU0FBUytDLE9BQU87Ozs7Ozs7Ozs7Ozs7OzsrQkFlekIsRUFBRXZCLEtBQUtDLFNBQVMsQ0FBQ3ZCLGNBQWM7Ozs7Ozs7Ozs7Ozs7OztNQWV4RCxDQUFDO1lBQ0RtRCxZQUFZVixRQUFRLENBQUNjLEtBQUs7UUFDNUI7SUFDRjtJQUVBLElBQUlyRCxXQUFXO1FBQ2IscUJBQ0UsOERBQUNzRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJckM7SUFFQSxJQUFJLENBQUMzRCxXQUFXLENBQUNRLG9CQUFvQjtRQUNuQyxxQkFDRSw4REFBQ2tEO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNwRSw4SEFBUUE7NEJBQUNvRSxXQUFVOzs7Ozs7Ozs7OztrQ0FFdEIsOERBQUNFO3dCQUFHRixXQUFVO2tDQUEyQzs7Ozs7O2tDQUd6RCw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQXFCOzs7Ozs7a0NBR2xDLDhEQUFDRzt3QkFBR0gsV0FBVTs7MENBQ1osOERBQUNJO2dDQUFHSixXQUFVOztrREFDWiw4REFBQ0Q7d0NBQUlDLFdBQVU7Ozs7OztvQ0FBOEM7Ozs7Ozs7MENBRy9ELDhEQUFDSTtnQ0FBR0osV0FBVTs7a0RBQ1osOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7b0NBQThDOzs7Ozs7Ozs7Ozs7O2tDQUlqRSw4REFBQ2pCO3dCQUNDRyxNQUFLO3dCQUNMYyxXQUFVOzswQ0FFViw4REFBQ3BFLDhIQUFRQTtnQ0FBQ29FLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU0vQztJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDTTt3Q0FBR0wsV0FBVTtrREFBd0M7Ozs7OztrREFHdEQsOERBQUNDO3dDQUFFRCxXQUFVOzs0Q0FBZ0I7MERBQ2MsOERBQUNNOzBEQUFRakUsUUFBUStDLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHcEUsOERBQUNXO2dDQUFJQyxXQUFVOzBDQUNaekQsOEJBQ0M7O3NEQUNFLDhEQUFDZ0U7NENBQ0NDLFNBQVNqQzs0Q0FDVGtDLFVBQVU5RDs0Q0FDVnFELFdBQVU7OzhEQUVWLDhEQUFDbkUsaUlBQVdBO29EQUFDbUUsV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7OztzREFHMUMsOERBQUNPOzRDQUNDQyxTQUFTaEM7NENBQ1R3QixXQUFVOzs4REFFViw4REFBQ2xFLGdJQUFVQTtvREFBQ2tFLFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7c0RBR3pDLDhEQUFDTzs0Q0FDQ0MsU0FBU2Y7NENBQ1RPLFdBQVU7OzhEQUVWLDhEQUFDakUsNkhBQU9BO29EQUFDaUUsV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFVL0MsQ0FBQ3pELDZCQUNBLDhEQUFDd0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ3JFLGdJQUFVQTtnQ0FBQ3FFLFdBQVU7Ozs7Ozs7Ozs7O3NDQUV4Qiw4REFBQ0U7NEJBQUdGLFdBQVU7c0NBQTRDOzs7Ozs7c0NBRzFELDhEQUFDQzs0QkFBRUQsV0FBVTtzQ0FBdUM7Ozs7OztzQ0FLcEQsOERBQUNPOzRCQUNDQyxTQUFTbkQ7NEJBQ1RvRCxVQUFVOUQ7NEJBQ1ZxRCxXQUFVO3NDQUVUckQsNkJBQ0M7O2tEQUNFLDhEQUFDb0Q7d0NBQUlDLFdBQVU7Ozs7OztvQ0FBdUU7OzZEQUl4Rjs7a0RBQ0UsOERBQUNwRSw4SEFBUUE7d0NBQUNvRSxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7OzhDQU83Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDN0Qsb0RBQWFBO3NDQUFFSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzlCO0FBRUEsaUVBQWVILGdCQUFnQkEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXHNyY1xcYXBwXFxwbGFuLWVzdHVkaW9zXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgRmlDYWxlbmRhciwgRmlDbG9jaywgRmlUYXJnZXQsIEZpUmVmcmVzaEN3LCBGaURvd25sb2FkLCBGaVByaW50IH0gZnJvbSAncmVhY3QtaWNvbnMvZmknO1xuaW1wb3J0IHsgb2J0ZW5lclRlbWFyaW9Vc3VhcmlvIH0gZnJvbSAnQC9mZWF0dXJlcy90ZW1hcmlvL3NlcnZpY2VzL3RlbWFyaW9TZXJ2aWNlJztcbmltcG9ydCB7IHRpZW5lUGxhbmlmaWNhY2lvbkNvbmZpZ3VyYWRhIH0gZnJvbSAnQC9mZWF0dXJlcy9wbGFuaWZpY2FjaW9uL3NlcnZpY2VzL3BsYW5pZmljYWNpb25TZXJ2aWNlJztcbmltcG9ydCB7IFRlbWFyaW8gfSBmcm9tICdAL2xpYi9zdXBhYmFzZS9zdXBhYmFzZUNsaWVudCc7XG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gJ3JlYWN0LWhvdC10b2FzdCc7XG5pbXBvcnQgUmVhY3RNYXJrZG93biBmcm9tICdyZWFjdC1tYXJrZG93bic7XG5cbmNvbnN0IFBsYW5Fc3R1ZGlvc1BhZ2U6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCBbdGVtYXJpbywgc2V0VGVtYXJpb10gPSB1c2VTdGF0ZTxUZW1hcmlvIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtwbGFuR2VuZXJhZG8sIHNldFBsYW5HZW5lcmFkb10gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbaXNHZW5lcmF0aW5nLCBzZXRJc0dlbmVyYXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbdGllbmVQbGFuaWZpY2FjaW9uLCBzZXRUaWVuZVBsYW5pZmljYWNpb25dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FyZ2FyRGF0b3MoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGNhcmdhckRhdG9zID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgLy8gT2J0ZW5lciB0ZW1hcmlvIGRlbCB1c3VhcmlvXG4gICAgICBjb25zdCB0ZW1hcmlvRGF0YSA9IGF3YWl0IG9idGVuZXJUZW1hcmlvVXN1YXJpbygpO1xuICAgICAgaWYgKCF0ZW1hcmlvRGF0YSkge1xuICAgICAgICB0b2FzdC5lcnJvcignTm8gc2UgZW5jb250csOzIHVuIHRlbWFyaW8gY29uZmlndXJhZG8nKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgc2V0VGVtYXJpbyh0ZW1hcmlvRGF0YSk7XG5cbiAgICAgIC8vIFZlcmlmaWNhciBzaSB0aWVuZSBwbGFuaWZpY2FjacOzbiBjb25maWd1cmFkYVxuICAgICAgY29uc3QgdGllbmVQbGFuID0gYXdhaXQgdGllbmVQbGFuaWZpY2FjaW9uQ29uZmlndXJhZGEodGVtYXJpb0RhdGEuaWQpO1xuICAgICAgc2V0VGllbmVQbGFuaWZpY2FjaW9uKHRpZW5lUGxhbik7XG5cbiAgICAgIGlmICghdGllbmVQbGFuKSB7XG4gICAgICAgIHRvYXN0LmVycm9yKCdOZWNlc2l0YXMgY29uZmlndXJhciB0dSBwbGFuaWZpY2FjacOzbiBhbnRlcyBkZSBnZW5lcmFyIGVsIHBsYW4gZGUgZXN0dWRpb3MnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGNhcmdhciBkYXRvczonLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcignRXJyb3IgYWwgY2FyZ2FyIGxvcyBkYXRvcycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVHZW5lcmFyUGxhbiA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXRlbWFyaW8pIHJldHVybjtcblxuICAgIHNldElzR2VuZXJhdGluZyh0cnVlKTtcbiAgICBsZXQgbG9hZGluZ1RvYXN0SWQ6IHN0cmluZyB8IHVuZGVmaW5lZDtcblxuICAgIHRyeSB7XG4gICAgICBsb2FkaW5nVG9hc3RJZCA9IHRvYXN0LmxvYWRpbmcoJ0dlbmVyYW5kbyB0dSBwbGFuIGRlIGVzdHVkaW9zIHBlcnNvbmFsaXphZG8gY29uIElBLi4uJyk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZ2VtaW5pJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIGFjdGlvbjogJ2dlbmVyYXJQbGFuRXN0dWRpb3MnLFxuICAgICAgICAgIHRlbWFyaW9JZDogdGVtYXJpby5pZFxuICAgICAgICB9KVxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvciBlbiBsYSBBUEk6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB7IHJlc3VsdCB9ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgc2V0UGxhbkdlbmVyYWRvKHJlc3VsdCk7XG5cbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ8KhUGxhbiBkZSBlc3R1ZGlvcyBnZW5lcmFkbyBleGl0b3NhbWVudGUhJywgeyBpZDogbG9hZGluZ1RvYXN0SWQgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGdlbmVyYXIgcGxhbjonLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcignRXJyb3IgYWwgZ2VuZXJhciBlbCBwbGFuIGRlIGVzdHVkaW9zLiBJbnTDqW50YWxvIGRlIG51ZXZvLicsIHsgaWQ6IGxvYWRpbmdUb2FzdElkIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVSZWdlbmVyYXJQbGFuID0gKCkgPT4ge1xuICAgIHNldFBsYW5HZW5lcmFkbygnJyk7XG4gICAgaGFuZGxlR2VuZXJhclBsYW4oKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEZXNjYXJnYXJQbGFuID0gKCkgPT4ge1xuICAgIGlmICghcGxhbkdlbmVyYWRvKSByZXR1cm47XG5cbiAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW3BsYW5HZW5lcmFkb10sIHsgdHlwZTogJ3RleHQvbWFya2Rvd24nIH0pO1xuICAgIGNvbnN0IHVybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7XG4gICAgY29uc3QgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgICBhLmhyZWYgPSB1cmw7XG4gICAgYS5kb3dubG9hZCA9IGBwbGFuLWVzdHVkaW9zLSR7dGVtYXJpbz8udGl0dWxvIHx8ICd0ZW1hcmlvJ30ubWRgO1xuICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQoYSk7XG4gICAgYS5jbGljaygpO1xuICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoYSk7XG4gICAgVVJMLnJldm9rZU9iamVjdFVSTCh1cmwpO1xuICAgIFxuICAgIHRvYXN0LnN1Y2Nlc3MoJ1BsYW4gZGVzY2FyZ2FkbyBleGl0b3NhbWVudGUnKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVJbXByaW1pclBsYW4gPSAoKSA9PiB7XG4gICAgaWYgKCFwbGFuR2VuZXJhZG8pIHJldHVybjtcbiAgICBcbiAgICBjb25zdCBwcmludFdpbmRvdyA9IHdpbmRvdy5vcGVuKCcnLCAnX2JsYW5rJyk7XG4gICAgaWYgKHByaW50V2luZG93KSB7XG4gICAgICBwcmludFdpbmRvdy5kb2N1bWVudC53cml0ZShgXG4gICAgICAgIDxodG1sPlxuICAgICAgICAgIDxoZWFkPlxuICAgICAgICAgICAgPHRpdGxlPlBsYW4gZGUgRXN0dWRpb3MgLSAke3RlbWFyaW8/LnRpdHVsb308L3RpdGxlPlxuICAgICAgICAgICAgPHN0eWxlPlxuICAgICAgICAgICAgICBib2R5IHsgZm9udC1mYW1pbHk6IEFyaWFsLCBzYW5zLXNlcmlmOyBtYXJnaW46IDIwcHg7IGxpbmUtaGVpZ2h0OiAxLjY7IH1cbiAgICAgICAgICAgICAgaDEsIGgyLCBoMyB7IGNvbG9yOiAjMzMzOyB9XG4gICAgICAgICAgICAgIGgxIHsgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMzMzM7IHBhZGRpbmctYm90dG9tOiAxMHB4OyB9XG4gICAgICAgICAgICAgIGgyIHsgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICM2NjY7IHBhZGRpbmctYm90dG9tOiA1cHg7IH1cbiAgICAgICAgICAgICAgdWwsIG9sIHsgbWFyZ2luLWxlZnQ6IDIwcHg7IH1cbiAgICAgICAgICAgICAgc3Ryb25nIHsgY29sb3I6ICMyNTYzZWI7IH1cbiAgICAgICAgICAgICAgQG1lZGlhIHByaW50IHsgYm9keSB7IG1hcmdpbjogMDsgfSB9XG4gICAgICAgICAgICA8L3N0eWxlPlxuICAgICAgICAgIDwvaGVhZD5cbiAgICAgICAgICA8Ym9keT5cbiAgICAgICAgICAgIDxkaXYgaWQ9XCJjb250ZW50XCI+PC9kaXY+XG4gICAgICAgICAgICA8c2NyaXB0PlxuICAgICAgICAgICAgICAvLyBDb252ZXJ0aXIgbWFya2Rvd24gYSBIVE1MIGLDoXNpY28gcGFyYSBpbXByZXNpw7NuXG4gICAgICAgICAgICAgIGNvbnN0IG1hcmtkb3duID0gJHtKU09OLnN0cmluZ2lmeShwbGFuR2VuZXJhZG8pfTtcbiAgICAgICAgICAgICAgY29uc3QgY29udGVudCA9IG1hcmtkb3duXG4gICAgICAgICAgICAgICAgLnJlcGxhY2UoL14jICguKiQpL2dpbSwgJzxoMT4kMTwvaDE+JylcbiAgICAgICAgICAgICAgICAucmVwbGFjZSgvXiMjICguKiQpL2dpbSwgJzxoMj4kMTwvaDI+JylcbiAgICAgICAgICAgICAgICAucmVwbGFjZSgvXiMjIyAoLiokKS9naW0sICc8aDM+JDE8L2gzPicpXG4gICAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcXFwqXFxcXCooLio/KVxcXFwqXFxcXCovZywgJzxzdHJvbmc+JDE8L3N0cm9uZz4nKVxuICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcKiguKj8pXFxcXCovZywgJzxlbT4kMTwvZW0+JylcbiAgICAgICAgICAgICAgICAucmVwbGFjZSgvXi0gKC4qJCkvZ2ltLCAnPGxpPiQxPC9saT4nKVxuICAgICAgICAgICAgICAgIC5yZXBsYWNlKC8oPGxpPi4qPFxcXFwvbGk+KS9zLCAnPHVsPiQxPC91bD4nKVxuICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9cXFxcbi9nLCAnPGJyPicpO1xuICAgICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY29udGVudCcpLmlubmVySFRNTCA9IGNvbnRlbnQ7XG4gICAgICAgICAgICAgIHdpbmRvdy5wcmludCgpO1xuICAgICAgICAgICAgPC9zY3JpcHQ+XG4gICAgICAgICAgPC9ib2R5PlxuICAgICAgICA8L2h0bWw+XG4gICAgICBgKTtcbiAgICAgIHByaW50V2luZG93LmRvY3VtZW50LmNsb3NlKCk7XG4gICAgfVxuICB9O1xuXG4gIGlmIChpc0xvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+Q2FyZ2FuZG8gZGF0b3MuLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmICghdGVtYXJpbyB8fCAhdGllbmVQbGFuaWZpY2FjaW9uKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC00XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctbWQgdy1mdWxsIGJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLXllbGxvdy0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgPEZpVGFyZ2V0IGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC15ZWxsb3ctNjAwXCIgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgICAgQ29uZmlndXJhY2nDs24gUmVxdWVyaWRhXG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTRcIj5cbiAgICAgICAgICAgIFBhcmEgZ2VuZXJhciB0dSBwbGFuIGRlIGVzdHVkaW9zIHBlcnNvbmFsaXphZG8sIG5lY2VzaXRhczpcbiAgICAgICAgICA8L3A+XG4gICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtbGVmdCB0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItNiBzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmx1ZS00MDAgcm91bmRlZC1mdWxsIG1yLTNcIj48L2Rpdj5cbiAgICAgICAgICAgICAgVGVuZXIgdW4gdGVtYXJpbyBjb25maWd1cmFkb1xuICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmx1ZS00MDAgcm91bmRlZC1mdWxsIG1yLTNcIj48L2Rpdj5cbiAgICAgICAgICAgICAgQ29tcGxldGFyIGxhIHBsYW5pZmljYWNpw7NuIGludGVsaWdlbnRlXG4gICAgICAgICAgICA8L2xpPlxuICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgPGFcbiAgICAgICAgICAgIGhyZWY9XCIvdGVtYXJpb1wiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEZpVGFyZ2V0IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICBJciBhIE1pIFRlbWFyaW9cbiAgICAgICAgICA8L2E+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy02eGwgbXgtYXV0byBweC00IHB5LThcIj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNiBtYi02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgTWkgUGxhbiBkZSBFc3R1ZGlvc1xuICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgUGxhbiBwZXJzb25hbGl6YWRvIGdlbmVyYWRvIGNvbiBJQSBwYXJhOiA8c3Ryb25nPnt0ZW1hcmlvLnRpdHVsb308L3N0cm9uZz5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICB7cGxhbkdlbmVyYWRvICYmIChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZWdlbmVyYXJQbGFufVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNHZW5lcmF0aW5nfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgdGV4dC1ncmF5LTcwMCBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9ycyBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPEZpUmVmcmVzaEN3IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIFJlZ2VuZXJhclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZURlc2NhcmdhclBsYW59XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPEZpRG93bmxvYWQgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgRGVzY2FyZ2FyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlSW1wcmltaXJQbGFufVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgdGV4dC1ncmF5LTcwMCBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxGaVByaW50IGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIEltcHJpbWlyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ29udGVuaWRvIHByaW5jaXBhbCAqL31cbiAgICAgICAgeyFwbGFuR2VuZXJhZG8gPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtOCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIwIGgtMjAgYmctYmx1ZS0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNlwiPlxuICAgICAgICAgICAgICA8RmlDYWxlbmRhciBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICBHZW5lcmEgdHUgUGxhbiBkZSBFc3R1ZGlvcyBQZXJzb25hbGl6YWRvXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi04IG1heC13LTJ4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgIE51ZXN0cm8gYXNpc3RlbnRlIGRlIElBIGFuYWxpemFyw6EgdHUgcGxhbmlmaWNhY2nDs24sIGRpc3BvbmliaWxpZGFkIGRlIHRpZW1wbywgXG4gICAgICAgICAgICAgIHkgbGFzIGNhcmFjdGVyw61zdGljYXMgZGUgY2FkYSB0ZW1hIHBhcmEgY3JlYXIgdW4gcGxhbiBkZSBlc3R1ZGlvcyBjb21wbGV0YW1lbnRlIFxuICAgICAgICAgICAgICBwZXJzb25hbGl6YWRvIHkgcmVhbGlzdGEuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUdlbmVyYXJQbGFufVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNHZW5lcmF0aW5nfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBweC02IHB5LTMgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzIGRpc2FibGVkOm9wYWNpdHktNTAgbXgtYXV0b1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpc0dlbmVyYXRpbmcgPyAoXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTUgdy01IGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlIG1yLTNcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIEdlbmVyYW5kbyBwbGFuIGNvbiBJQS4uLlxuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICA8RmlUYXJnZXQgY2xhc3NOYW1lPVwidy01IGgtNSBtci0zXCIgLz5cbiAgICAgICAgICAgICAgICAgIEdlbmVyYXIgUGxhbiBkZSBFc3R1ZGlvc1xuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwcm9zZSBwcm9zZS1ibHVlIG1heC13LW5vbmVcIj5cbiAgICAgICAgICAgICAgPFJlYWN0TWFya2Rvd24+e3BsYW5HZW5lcmFkb308L1JlYWN0TWFya2Rvd24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgUGxhbkVzdHVkaW9zUGFnZTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiRmlDYWxlbmRhciIsIkZpVGFyZ2V0IiwiRmlSZWZyZXNoQ3ciLCJGaURvd25sb2FkIiwiRmlQcmludCIsIm9idGVuZXJUZW1hcmlvVXN1YXJpbyIsInRpZW5lUGxhbmlmaWNhY2lvbkNvbmZpZ3VyYWRhIiwidG9hc3QiLCJSZWFjdE1hcmtkb3duIiwiUGxhbkVzdHVkaW9zUGFnZSIsInRlbWFyaW8iLCJzZXRUZW1hcmlvIiwicGxhbkdlbmVyYWRvIiwic2V0UGxhbkdlbmVyYWRvIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNHZW5lcmF0aW5nIiwic2V0SXNHZW5lcmF0aW5nIiwidGllbmVQbGFuaWZpY2FjaW9uIiwic2V0VGllbmVQbGFuaWZpY2FjaW9uIiwiY2FyZ2FyRGF0b3MiLCJ0ZW1hcmlvRGF0YSIsImVycm9yIiwidGllbmVQbGFuIiwiaWQiLCJjb25zb2xlIiwiaGFuZGxlR2VuZXJhclBsYW4iLCJsb2FkaW5nVG9hc3RJZCIsImxvYWRpbmciLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiYWN0aW9uIiwidGVtYXJpb0lkIiwib2siLCJFcnJvciIsInN0YXR1cyIsInJlc3VsdCIsImpzb24iLCJzdWNjZXNzIiwiaGFuZGxlUmVnZW5lcmFyUGxhbiIsImhhbmRsZURlc2NhcmdhclBsYW4iLCJibG9iIiwiQmxvYiIsInR5cGUiLCJ1cmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJhIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImRvd25sb2FkIiwidGl0dWxvIiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInJlbW92ZUNoaWxkIiwicmV2b2tlT2JqZWN0VVJMIiwiaGFuZGxlSW1wcmltaXJQbGFuIiwicHJpbnRXaW5kb3ciLCJ3aW5kb3ciLCJvcGVuIiwid3JpdGUiLCJjbG9zZSIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJoMiIsInVsIiwibGkiLCJoMSIsInN0cm9uZyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/plan-estudios/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/authService */ \"(ssr)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _features_auth_hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/auth/hooks/useInactivityTimer */ \"(ssr)/./src/features/auth/hooks/useInactivityTimer.ts\");\n/* harmony import */ var _features_auth_components_InactivityWarning__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/auth/components/InactivityWarning */ \"(ssr)/./src/features/auth/components/InactivityWarning.tsx\");\n\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Start true: loading initial auth state\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInactivityWarning, setShowInactivityWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [warningTimeRemaining, setWarningTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60); // 60 segundos de advertencia\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Effect for auth state listener and initial session check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setIsLoading(true); // Explicitly set loading true at the start of auth setup\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (event, currentSession)=>{\n                    setSession(currentSession);\n                    setUser(currentSession?.user ?? null);\n                    setError(null); // Clear previous errors on any auth state change\n                    // Centralize setIsLoading(false) after processing the event.\n                    if (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED' || event === 'PASSWORD_RECOVERY') {\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Initial session fetch. onAuthStateChange with INITIAL_SESSION will also fire.\n            _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session: initialSessionCheck }, error: getSessionError })=>{\n                    if (getSessionError) {\n                        setError(getSessionError.message);\n                        setIsLoading(false); // Ensure loading is false if initial getSession fails\n                    }\n                    // Para dispositivos móviles, verificar también localStorage si no hay sesión\n                    if (!initialSessionCheck && \"undefined\" !== 'undefined') {}\n                // If INITIAL_SESSION hasn't fired and set loading to false, and this fails, we ensure it's false.\n                // Note: if getSession is successful, `setIsLoading(false)` is primarily handled by INITIAL_SESSION event.\n                }\n            }[\"AuthProvider.useEffect\"]).catch({\n                \"AuthProvider.useEffect\": (error)=>{\n                    setError(error.message);\n                    setIsLoading(false); // Ensure loading is false if initial getSession throws\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener?.subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Runs once on mount\n    // Effect for handling redirections based on auth state and pathname\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // No realizar redirecciones mientras se está cargando\n            if (isLoading) {\n                return;\n            }\n            // No aplicar redirecciones a rutas de API o recursos estáticos\n            if (pathname.startsWith('/api') || pathname.startsWith('/_next')) {\n                return;\n            }\n            // Definir rutas públicas (mantener sincronizado con middleware)\n            const publicPaths = [\n                '/login'\n            ];\n            // Si hay sesión y estamos en /login, el middleware ya debería haber redirigido.\n            // Esta es una salvaguarda del lado del cliente.\n            if (session && pathname === '/login') {\n                router.replace('/'); // router.replace es mejor aquí para evitar entradas en el historial\n                return; // Importante retornar para no evaluar la siguiente condición\n            }\n            // Si NO hay sesión y NO estamos en una ruta pública (y no es una ruta API/interna)\n            // Esta lógica es para cuando el estado cambia en el cliente (ej. logout)\n            if (!session && !publicPaths.includes(pathname) && !pathname.startsWith('/api') && !pathname.startsWith('/_next')) {\n                router.replace('/login');\n                return;\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        session,\n        isLoading,\n        pathname,\n        router\n    ]);\n    const iniciarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[iniciarSesion]\": async (email, password_provided)=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const { user: loggedInUser, session: currentAuthSession, error: loginError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.iniciarSesion)(email, password_provided);\n                if (loginError) {\n                    setError(loginError);\n                    setIsLoading(false); // Ensure loading is false on error\n                    return {\n                        user: null,\n                        session: null,\n                        error: loginError\n                    };\n                }\n                // Verificar que la sesión se haya establecido correctamente antes de redirigir\n                if (currentAuthSession) {\n                    // Esperar un momento adicional para asegurar que las cookies se propaguen\n                    // antes de la redirección\n                    await new Promise({\n                        \"AuthProvider.useCallback[iniciarSesion]\": (resolve)=>setTimeout(resolve, 300)\n                    }[\"AuthProvider.useCallback[iniciarSesion]\"]);\n                    // Redirigir a la página principal usando replace para evitar entradas en el historial\n                    router.replace('/');\n                }\n                // If successful, onAuthStateChange (SIGNED_IN) will set user, session, and isLoading to false.\n                return {\n                    user: loggedInUser,\n                    session: currentAuthSession,\n                    error: null\n                };\n            } catch (e) {\n                const errorMessage = e instanceof Error && e.message ? e.message : 'Error desconocido durante el inicio de sesión.';\n                setError(errorMessage);\n                setIsLoading(false); // Ensure loading is false on exception\n                return {\n                    user: null,\n                    session: null,\n                    error: errorMessage\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[iniciarSesion]\"], [\n        router\n    ]); // Added router dependency\n    const cerrarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[cerrarSesion]\": async ()=>{\n            setIsLoading(true);\n            setError(null);\n            const { error: logoutError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.cerrarSesion)();\n            if (logoutError) {\n                setError(logoutError);\n                setIsLoading(false); // Ensure loading is false on error\n            }\n        // If successful, onAuthStateChange (SIGNED_OUT) handles state updates and isLoading.\n        // The redirection useEffect will then handle redirecting to /login.\n        }\n    }[\"AuthProvider.useCallback[cerrarSesion]\"], []); // Assuming cerrarSesionService is a stable import\n    const estaAutenticado = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[estaAutenticado]\": ()=>!!user && !!session && !isLoading\n    }[\"AuthProvider.useCallback[estaAutenticado]\"], [\n        user,\n        session,\n        isLoading\n    ]);\n    // Función para manejar el logout por inactividad\n    const handleInactivityLogout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleInactivityLogout]\": async ()=>{\n            // Cerrar sesión directamente sin mostrar advertencia\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleInactivityLogout]\"], [\n        cerrarSesion\n    ]);\n    // Función para extender la sesión\n    const handleExtendSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleExtendSession]\": ()=>{\n            setShowInactivityWarning(false);\n        // El hook useAutoLogout se reiniciará automáticamente con la actividad\n        }\n    }[\"AuthProvider.useCallback[handleExtendSession]\"], []);\n    // Función para cerrar sesión desde la advertencia\n    const handleLogoutFromWarning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleLogoutFromWarning]\": async ()=>{\n            setShowInactivityWarning(false);\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleLogoutFromWarning]\"], [\n        cerrarSesion\n    ]);\n    // Hook para manejar la inactividad (solo si el usuario está autenticado)\n    const { resetTimer } = (0,_features_auth_hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_5__.useAutoLogout)(5, handleInactivityLogout, estaAutenticado() // Solo activo si está autenticado\n    );\n    const value = {\n        user,\n        session,\n        isLoading,\n        error,\n        iniciarSesion,\n        cerrarSesion,\n        estaAutenticado\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: [\n            children,\n             false && /*#__PURE__*/ 0\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth debe ser utilizado dentro de un AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/BackgroundTasksContext.tsx":
/*!*************************************************!*\
  !*** ./src/contexts/BackgroundTasksContext.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundTasksProvider: () => (/* binding */ BackgroundTasksProvider),\n/* harmony export */   useBackgroundTasks: () => (/* binding */ useBackgroundTasks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ BackgroundTasksProvider,useBackgroundTasks auto */ \n\n\nconst BackgroundTasksContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst BackgroundTasksProvider = ({ children })=>{\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[addTask]\": (taskData)=>{\n            const id = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            const newTask = {\n                ...taskData,\n                id,\n                status: 'pending',\n                createdAt: new Date()\n            };\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[addTask]\": (prev)=>[\n                        ...prev,\n                        newTask\n                    ]\n            }[\"BackgroundTasksProvider.useCallback[addTask]\"]);\n            // Mostrar notificación de inicio\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading(`Iniciando: ${newTask.title}`, {\n                id: `task_start_${id}`,\n                duration: 2000\n            });\n            return id;\n        }\n    }[\"BackgroundTasksProvider.useCallback[addTask]\"], []);\n    const updateTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[updateTask]\": (id, updates)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[updateTask]\": (prev)=>prev.map({\n                        \"BackgroundTasksProvider.useCallback[updateTask]\": (task)=>{\n                            if (task.id === id) {\n                                const updatedTask = {\n                                    ...task,\n                                    ...updates\n                                };\n                                // Manejar notificaciones según el estado\n                                if (updates.status === 'processing' && task.status === 'pending') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_start_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading(`Procesando: ${task.title}`, {\n                                        id: `task_processing_${id}`\n                                    });\n                                } else if (updates.status === 'completed' && task.status !== 'completed') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(`Completado: ${task.title}`, {\n                                        id: `task_completed_${id}`,\n                                        duration: 4000\n                                    });\n                                    updatedTask.completedAt = new Date();\n                                } else if (updates.status === 'error' && task.status !== 'error') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(`Error: ${task.title}`, {\n                                        id: `task_error_${id}`,\n                                        duration: 5000\n                                    });\n                                }\n                                return updatedTask;\n                            }\n                            return task;\n                        }\n                    }[\"BackgroundTasksProvider.useCallback[updateTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[updateTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[updateTask]\"], []);\n    const removeTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[removeTask]\": (id)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[removeTask]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[removeTask]\": (task)=>task.id !== id\n                    }[\"BackgroundTasksProvider.useCallback[removeTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[removeTask]\"]);\n            // Limpiar notificaciones relacionadas\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_start_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_completed_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_error_${id}`);\n        }\n    }[\"BackgroundTasksProvider.useCallback[removeTask]\"], []);\n    const getTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTask]\": (id)=>{\n            return tasks.find({\n                \"BackgroundTasksProvider.useCallback[getTask]\": (task)=>task.id === id\n            }[\"BackgroundTasksProvider.useCallback[getTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTask]\"], [\n        tasks\n    ]);\n    const getTasksByType = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTasksByType]\": (type)=>{\n            return tasks.filter({\n                \"BackgroundTasksProvider.useCallback[getTasksByType]\": (task)=>task.type === type\n            }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"], [\n        tasks\n    ]);\n    const clearCompletedTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": ()=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (task)=>task.status !== 'completed' && task.status !== 'error'\n                    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"])\n            }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"], []);\n    const activeTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[activeTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[activeTasks]\": (task)=>task.status === 'pending' || task.status === 'processing'\n            }[\"BackgroundTasksProvider.useMemo[activeTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[activeTasks]\"], [\n        tasks\n    ]);\n    const completedTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[completedTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[completedTasks]\": (task)=>task.status === 'completed' || task.status === 'error'\n            }[\"BackgroundTasksProvider.useMemo[completedTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[completedTasks]\"], [\n        tasks\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[value]\": ()=>({\n                tasks,\n                addTask,\n                updateTask,\n                removeTask,\n                getTask,\n                getTasksByType,\n                clearCompletedTasks,\n                activeTasks,\n                completedTasks\n            })\n    }[\"BackgroundTasksProvider.useMemo[value]\"], [\n        tasks,\n        addTask,\n        updateTask,\n        removeTask,\n        getTask,\n        getTasksByType,\n        clearCompletedTasks,\n        activeTasks,\n        completedTasks\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackgroundTasksContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\contexts\\\\BackgroundTasksContext.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\nconst useBackgroundTasks = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BackgroundTasksContext);\n    if (context === undefined) {\n        throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/BackgroundTasksContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/AuthManager.tsx":
/*!******************************************************!*\
  !*** ./src/features/auth/components/AuthManager.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * Componente para manejar errores comunes de autenticación\n * y sincronización de tiempo en Supabase\n */ function AuthManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AuthManager.useEffect\": ()=>{\n            // Verificar si hay problemas de sincronización de tiempo\n            const checkTimeSync = {\n                \"AuthManager.useEffect.checkTimeSync\": async ()=>{\n                    try {\n                        // Usar variables de entorno para la configuración de Supabase\n                        const supabaseUrl = \"https://fxnhpxjijinfuxxxplzj.supabase.co\";\n                        const supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\";\n                        if (!supabaseUrl || !supabaseKey) {\n                            console.warn('Variables de entorno de Supabase no configuradas');\n                            return;\n                        }\n                        // Obtener la hora del servidor de Supabase\n                        const response = await fetch(`${supabaseUrl}/rest/v1/`, {\n                            method: 'GET',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'apikey': supabaseKey\n                            }\n                        });\n                        // Obtener la fecha del servidor desde las cabeceras\n                        const serverDate = new Date(response.headers.get('date') || '');\n                        const clientDate = new Date();\n                        // Calcular la diferencia en segundos\n                        const timeDiff = Math.abs((serverDate.getTime() - clientDate.getTime()) / 1000);\n                        // Si la diferencia es mayor a 60 segundos, mostrar una advertencia\n                        if (timeDiff > 60) {\n                            console.warn(`Posible problema de sincronización de tiempo detectado. ` + `La diferencia entre tu hora local y el servidor es de ${Math.round(timeDiff)} segundos. ` + `Esto puede causar problemas de autenticación.`);\n                        }\n                    } catch (error) {\n                        console.error('Error al verificar sincronización de tiempo:', error);\n                    }\n                }\n            }[\"AuthManager.useEffect.checkTimeSync\"];\n            // Ejecutar la verificación\n            checkTimeSync();\n            // Configurar un listener para eventos de autenticación\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"AuthManager.useEffect\": (event, session)=>{\n                    console.log('[AuthManager] Auth event:', event, 'Session:', !!session);\n                    if (event === 'SIGNED_OUT') {\n                        // Supabase ya maneja la limpieza de tokens internamente\n                        console.log('[AuthManager] Sesión cerrada');\n                    } else if (event === 'SIGNED_IN') {\n                        console.log('[AuthManager] Sesión iniciada correctamente');\n                    }\n                }\n            }[\"AuthManager.useEffect\"]);\n            return ({\n                \"AuthManager.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                }\n            })[\"AuthManager.useEffect\"];\n        }\n    }[\"AuthManager.useEffect\"], []);\n    // Este componente no renderiza nada visible\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/AuthManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/InactivityWarning.tsx":
/*!************************************************************!*\
  !*** ./src/features/auth/components/InactivityWarning.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiClock,FiRefreshCw!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst InactivityWarning = ({ isVisible, timeRemaining, onExtendSession, onLogout })=>{\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(timeRemaining);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            setCountdown(timeRemaining);\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        timeRemaining\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            if (!isVisible || countdown <= 0) return;\n            const interval = setInterval({\n                \"InactivityWarning.useEffect.interval\": ()=>{\n                    setCountdown({\n                        \"InactivityWarning.useEffect.interval\": (prev)=>{\n                            if (prev <= 1) {\n                                onLogout();\n                                return 0;\n                            }\n                            return prev - 1;\n                        }\n                    }[\"InactivityWarning.useEffect.interval\"]);\n                }\n            }[\"InactivityWarning.useEffect.interval\"], 1000);\n            return ({\n                \"InactivityWarning.useEffect\": ()=>clearInterval(interval)\n            })[\"InactivityWarning.useEffect\"];\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        isVisible,\n        countdown,\n        onLogout\n    ]);\n    if (!isVisible) return null;\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-100 rounded-full p-3 mr-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiAlertTriangle, {\n                                className: \"text-yellow-600 text-xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Sesi\\xf3n por expirar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Tu sesi\\xf3n expirar\\xe1 por inactividad\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                                    className: \"text-gray-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-mono font-bold text-gray-900\",\n                                    children: formatTime(countdown)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-gray-600 mt-2\",\n                            children: \"Tiempo restante antes del cierre autom\\xe1tico\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onExtendSession,\n                            className: \"flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiRefreshCw, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Continuar sesi\\xf3n\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onLogout,\n                            className: \"flex-1 bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n                            children: \"Cerrar sesi\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Por seguridad, tu sesi\\xf3n se cerrar\\xe1 autom\\xe1ticamente tras 5 minutos de inactividad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InactivityWarning);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/InactivityWarning.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/hooks/useInactivityTimer.ts":
/*!*******************************************************!*\
  !*** ./src/features/auth/hooks/useInactivityTimer.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAutoLogout: () => (/* binding */ useAutoLogout),\n/* harmony export */   useInactivityTimer: () => (/* binding */ useInactivityTimer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Hook para manejar la desconexión automática por inactividad\n */ const useInactivityTimer = ({ timeout, onTimeout, enabled = true })=>{\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastActivityRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Date.now());\n    // Función para resetear el timer\n    const resetTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n            if (!enabled) return;\n            // Limpiar el timer anterior si existe\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            // Actualizar la última actividad\n            lastActivityRef.current = Date.now();\n            // Crear un nuevo timer\n            timeoutRef.current = setTimeout({\n                \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n                    onTimeout();\n                }\n            }[\"useInactivityTimer.useCallback[resetTimer]\"], timeout);\n        }\n    }[\"useInactivityTimer.useCallback[resetTimer]\"], [\n        timeout,\n        onTimeout,\n        enabled\n    ]);\n    // Función para limpiar el timer\n    const clearTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[clearTimer]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n        }\n    }[\"useInactivityTimer.useCallback[clearTimer]\"], []);\n    // Función para obtener el tiempo restante\n    const getTimeRemaining = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[getTimeRemaining]\": ()=>{\n            if (!enabled || !timeoutRef.current) return 0;\n            const elapsed = Date.now() - lastActivityRef.current;\n            const remaining = Math.max(0, timeout - elapsed);\n            return remaining;\n        }\n    }[\"useInactivityTimer.useCallback[getTimeRemaining]\"], [\n        timeout,\n        enabled\n    ]);\n    // Eventos que consideramos como actividad del usuario\n    const events = [\n        'mousedown',\n        'mousemove',\n        'keypress',\n        'scroll',\n        'touchstart',\n        'click',\n        'keydown'\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useInactivityTimer.useEffect\": ()=>{\n            if (!enabled) {\n                clearTimer();\n                return;\n            }\n            // Función que maneja los eventos de actividad\n            const handleActivity = {\n                \"useInactivityTimer.useEffect.handleActivity\": ()=>{\n                    resetTimer();\n                }\n            }[\"useInactivityTimer.useEffect.handleActivity\"];\n            // Agregar event listeners\n            events.forEach({\n                \"useInactivityTimer.useEffect\": (event)=>{\n                    document.addEventListener(event, handleActivity, true);\n                }\n            }[\"useInactivityTimer.useEffect\"]);\n            // Iniciar el timer\n            resetTimer();\n            // Cleanup\n            return ({\n                \"useInactivityTimer.useEffect\": ()=>{\n                    events.forEach({\n                        \"useInactivityTimer.useEffect\": (event)=>{\n                            document.removeEventListener(event, handleActivity, true);\n                        }\n                    }[\"useInactivityTimer.useEffect\"]);\n                    clearTimer();\n                }\n            })[\"useInactivityTimer.useEffect\"];\n        }\n    }[\"useInactivityTimer.useEffect\"], [\n        enabled,\n        resetTimer,\n        clearTimer\n    ]);\n    return {\n        resetTimer,\n        clearTimer,\n        getTimeRemaining\n    };\n};\n/**\n * Hook simplificado para desconexión automática\n */ const useAutoLogout = (timeoutMinutes = 5, onLogout, enabled = true)=>{\n    const timeoutMs = timeoutMinutes * 60 * 1000; // Convertir minutos a milisegundos\n    return useInactivityTimer({\n        timeout: timeoutMs,\n        onTimeout: onLogout,\n        enabled\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/hooks/useInactivityTimer.ts\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/services/authService.ts":
/*!***************************************************!*\
  !*** ./src/features/auth/services/authService.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n\n/**\n * Inicia sesión con email y contraseña\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            error: errorMessage\n        };\n    }\n}\n/**\n * Cierra la sesión del usuario actual\n */ async function cerrarSesion() {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            return {\n                error: error.message\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\n * Obtiene la sesión actual del usuario\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\n * Obtiene el usuario actual\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\n * Verifica si el usuario está autenticado\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/services/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/features/planificacion/services/planificacionService.ts":
/*!*********************************************************************!*\
  !*** ./src/features/planificacion/services/planificacionService.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eliminarPlanificacion: () => (/* binding */ eliminarPlanificacion),\n/* harmony export */   guardarEstimacionesTemas: () => (/* binding */ guardarEstimacionesTemas),\n/* harmony export */   guardarPlanificacionUsuario: () => (/* binding */ guardarPlanificacionUsuario),\n/* harmony export */   obtenerEstimacionesTemas: () => (/* binding */ obtenerEstimacionesTemas),\n/* harmony export */   obtenerPlanificacionUsuario: () => (/* binding */ obtenerPlanificacionUsuario),\n/* harmony export */   tienePlanificacionConfigurada: () => (/* binding */ tienePlanificacionConfigurada)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(ssr)/./src/features/auth/services/authService.ts\");\n\n\n/**\n * Verifica si el usuario tiene una planificación configurada\n */ async function tienePlanificacionConfigurada(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.log('No hay usuario autenticado o error:', authError);\n            return false;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planificacion_usuario').select('id').eq('user_id', user.id).eq('temario_id', temarioId).eq('completado', true).limit(1);\n        if (error) {\n            console.error('Error al verificar planificación:', error);\n            return false;\n        }\n        return data && data.length > 0;\n    } catch (error) {\n        console.error('Error al verificar planificación:', error);\n        return false;\n    }\n}\n/**\n * Obtiene la planificación del usuario para un temario\n */ async function obtenerPlanificacionUsuario(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planificacion_usuario').select('*').eq('user_id', user.id).eq('temario_id', temarioId).single();\n        if (error) {\n            if (error.code === 'PGRST116') {\n                return null; // No hay planificación\n            }\n            console.error('Error al obtener planificación:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener planificación:', error);\n        return null;\n    }\n}\n/**\n * Crea o actualiza la planificación del usuario\n */ async function guardarPlanificacionUsuario(temarioId, planificacion) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        // Verificar si ya existe una planificación\n        const planificacionExistente = await obtenerPlanificacionUsuario(temarioId);\n        if (planificacionExistente) {\n            // Actualizar planificación existente\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planificacion_usuario').update({\n                ...planificacion,\n                completado: true,\n                actualizado_en: new Date().toISOString()\n            }).eq('id', planificacionExistente.id).select().single();\n            if (error) {\n                console.error('Error al actualizar planificación:', error);\n                return null;\n            }\n            return data.id;\n        } else {\n            // Crear nueva planificación\n            const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planificacion_usuario').insert([\n                {\n                    user_id: user.id,\n                    temario_id: temarioId,\n                    ...planificacion,\n                    completado: true\n                }\n            ]).select().single();\n            if (error) {\n                console.error('Error al crear planificación:', error);\n                return null;\n            }\n            return data.id;\n        }\n    } catch (error) {\n        console.error('Error al guardar planificación:', error);\n        return null;\n    }\n}\n/**\n * Obtiene las estimaciones de temas para una planificación\n */ async function obtenerEstimacionesTemas(planificacionId) {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estimaciones_temas').select('*').eq('planificacion_id', planificacionId);\n        if (error) {\n            console.error('Error al obtener estimaciones de temas:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener estimaciones de temas:', error);\n        return [];\n    }\n}\n/**\n * Guarda las estimaciones de temas\n */ async function guardarEstimacionesTemas(planificacionId, estimaciones) {\n    try {\n        // Eliminar estimaciones existentes\n        await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estimaciones_temas').delete().eq('planificacion_id', planificacionId);\n        // Insertar nuevas estimaciones\n        const estimacionesConPlanificacion = estimaciones.map((est)=>({\n                ...est,\n                planificacion_id: planificacionId\n            }));\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estimaciones_temas').insert(estimacionesConPlanificacion);\n        if (error) {\n            console.error('Error al guardar estimaciones de temas:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al guardar estimaciones de temas:', error);\n        return false;\n    }\n}\n/**\n * Elimina una planificación y todas sus estimaciones\n */ async function eliminarPlanificacion(planificacionId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planificacion_usuario').delete().eq('id', planificacionId);\n        if (error) {\n            console.error('Error al eliminar planificación:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar planificación:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvZmVhdHVyZXMvcGxhbmlmaWNhY2lvbi9zZXJ2aWNlcy9wbGFuaWZpY2FjaW9uU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFpRDtBQUMyQjtBQUc1RTs7Q0FFQyxHQUNNLGVBQWVFLDhCQUE4QkMsU0FBaUI7SUFDbkUsSUFBSTtRQUNGLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxPQUFPQyxTQUFTLEVBQUUsR0FBRyxNQUFNTCx5RkFBb0JBO1FBQzdELElBQUksQ0FBQ0csUUFBUUUsV0FBVztZQUN0QkMsUUFBUUMsR0FBRyxDQUFDLHVDQUF1Q0Y7WUFDbkQsT0FBTztRQUNUO1FBRUEsTUFBTSxFQUFFRyxJQUFJLEVBQUVKLEtBQUssRUFBRSxHQUFHLE1BQU1MLDBEQUFRQSxDQUNuQ1UsSUFBSSxDQUFDLHlCQUNMQyxNQUFNLENBQUMsTUFDUEMsRUFBRSxDQUFDLFdBQVdSLEtBQUtTLEVBQUUsRUFDckJELEVBQUUsQ0FBQyxjQUFjVCxXQUNqQlMsRUFBRSxDQUFDLGNBQWMsTUFDakJFLEtBQUssQ0FBQztRQUVULElBQUlULE9BQU87WUFDVEUsUUFBUUYsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkQsT0FBTztRQUNUO1FBRUEsT0FBT0ksUUFBUUEsS0FBS00sTUFBTSxHQUFHO0lBQy9CLEVBQUUsT0FBT1YsT0FBTztRQUNkRSxRQUFRRixLQUFLLENBQUMscUNBQXFDQTtRQUNuRCxPQUFPO0lBQ1Q7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZVcsNEJBQTRCYixTQUFpQjtJQUNqRSxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE9BQU9DLFNBQVMsRUFBRSxHQUFHLE1BQU1MLHlGQUFvQkE7UUFDN0QsSUFBSSxDQUFDRyxRQUFRRSxXQUFXO1lBQ3RCLE9BQU87UUFDVDtRQUVBLE1BQU0sRUFBRUcsSUFBSSxFQUFFSixLQUFLLEVBQUUsR0FBRyxNQUFNTCwwREFBUUEsQ0FDbkNVLElBQUksQ0FBQyx5QkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxXQUFXUixLQUFLUyxFQUFFLEVBQ3JCRCxFQUFFLENBQUMsY0FBY1QsV0FDakJjLE1BQU07UUFFVCxJQUFJWixPQUFPO1lBQ1QsSUFBSUEsTUFBTWEsSUFBSSxLQUFLLFlBQVk7Z0JBQzdCLE9BQU8sTUFBTSx1QkFBdUI7WUFDdEM7WUFDQVgsUUFBUUYsS0FBSyxDQUFDLG1DQUFtQ0E7WUFDakQsT0FBTztRQUNUO1FBRUEsT0FBT0k7SUFDVCxFQUFFLE9BQU9KLE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLG1DQUFtQ0E7UUFDakQsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVjLDRCQUNwQmhCLFNBQWlCLEVBQ2pCaUIsYUFRQztJQUVELElBQUk7UUFDRixNQUFNLEVBQUVoQixJQUFJLEVBQUVDLE9BQU9DLFNBQVMsRUFBRSxHQUFHLE1BQU1MLHlGQUFvQkE7UUFDN0QsSUFBSSxDQUFDRyxRQUFRRSxXQUFXO1lBQ3RCQyxRQUFRRixLQUFLLENBQUM7WUFDZCxPQUFPO1FBQ1Q7UUFFQSwyQ0FBMkM7UUFDM0MsTUFBTWdCLHlCQUF5QixNQUFNTCw0QkFBNEJiO1FBRWpFLElBQUlrQix3QkFBd0I7WUFDMUIscUNBQXFDO1lBQ3JDLE1BQU0sRUFBRVosSUFBSSxFQUFFSixLQUFLLEVBQUUsR0FBRyxNQUFNTCwwREFBUUEsQ0FDbkNVLElBQUksQ0FBQyx5QkFDTFksTUFBTSxDQUFDO2dCQUNOLEdBQUdGLGFBQWE7Z0JBQ2hCRyxZQUFZO2dCQUNaQyxnQkFBZ0IsSUFBSUMsT0FBT0MsV0FBVztZQUN4QyxHQUNDZCxFQUFFLENBQUMsTUFBTVMsdUJBQXVCUixFQUFFLEVBQ2xDRixNQUFNLEdBQ05NLE1BQU07WUFFVCxJQUFJWixPQUFPO2dCQUNURSxRQUFRRixLQUFLLENBQUMsc0NBQXNDQTtnQkFDcEQsT0FBTztZQUNUO1lBRUEsT0FBT0ksS0FBS0ksRUFBRTtRQUNoQixPQUFPO1lBQ0wsNEJBQTRCO1lBQzVCLE1BQU0sRUFBRUosSUFBSSxFQUFFSixLQUFLLEVBQUUsR0FBRyxNQUFNTCwwREFBUUEsQ0FDbkNVLElBQUksQ0FBQyx5QkFDTGlCLE1BQU0sQ0FBQztnQkFBQztvQkFDUEMsU0FBU3hCLEtBQUtTLEVBQUU7b0JBQ2hCZ0IsWUFBWTFCO29CQUNaLEdBQUdpQixhQUFhO29CQUNoQkcsWUFBWTtnQkFDZDthQUFFLEVBQ0RaLE1BQU0sR0FDTk0sTUFBTTtZQUVULElBQUlaLE9BQU87Z0JBQ1RFLFFBQVFGLEtBQUssQ0FBQyxpQ0FBaUNBO2dCQUMvQyxPQUFPO1lBQ1Q7WUFFQSxPQUFPSSxLQUFLSSxFQUFFO1FBQ2hCO0lBQ0YsRUFBRSxPQUFPUixPQUFPO1FBQ2RFLFFBQVFGLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ2pELE9BQU87SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFleUIseUJBQXlCQyxlQUF1QjtJQUNwRSxJQUFJO1FBQ0YsTUFBTSxFQUFFdEIsSUFBSSxFQUFFSixLQUFLLEVBQUUsR0FBRyxNQUFNTCwwREFBUUEsQ0FDbkNVLElBQUksQ0FBQyxzQkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxvQkFBb0JtQjtRQUUxQixJQUFJMUIsT0FBTztZQUNURSxRQUFRRixLQUFLLENBQUMsMkNBQTJDQTtZQUN6RCxPQUFPLEVBQUU7UUFDWDtRQUVBLE9BQU9JLFFBQVEsRUFBRTtJQUNuQixFQUFFLE9BQU9KLE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLDJDQUEyQ0E7UUFDekQsT0FBTyxFQUFFO0lBQ1g7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZTJCLHlCQUNwQkQsZUFBdUIsRUFDdkJFLFlBT0U7SUFFRixJQUFJO1FBQ0YsbUNBQW1DO1FBQ25DLE1BQU1qQywwREFBUUEsQ0FDWFUsSUFBSSxDQUFDLHNCQUNMd0IsTUFBTSxHQUNOdEIsRUFBRSxDQUFDLG9CQUFvQm1CO1FBRTFCLCtCQUErQjtRQUMvQixNQUFNSSwrQkFBK0JGLGFBQWFHLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBUTtnQkFDNUQsR0FBR0EsR0FBRztnQkFDTkMsa0JBQWtCUDtZQUNwQjtRQUVBLE1BQU0sRUFBRTFCLEtBQUssRUFBRSxHQUFHLE1BQU1MLDBEQUFRQSxDQUM3QlUsSUFBSSxDQUFDLHNCQUNMaUIsTUFBTSxDQUFDUTtRQUVWLElBQUk5QixPQUFPO1lBQ1RFLFFBQVFGLEtBQUssQ0FBQywyQ0FBMkNBO1lBQ3pELE9BQU87UUFDVDtRQUVBLE9BQU87SUFDVCxFQUFFLE9BQU9BLE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLDJDQUEyQ0E7UUFDekQsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVrQyxzQkFBc0JSLGVBQXVCO0lBQ2pFLElBQUk7UUFDRixNQUFNLEVBQUUxQixLQUFLLEVBQUUsR0FBRyxNQUFNTCwwREFBUUEsQ0FDN0JVLElBQUksQ0FBQyx5QkFDTHdCLE1BQU0sR0FDTnRCLEVBQUUsQ0FBQyxNQUFNbUI7UUFFWixJQUFJMUIsT0FBTztZQUNURSxRQUFRRixLQUFLLENBQUMsb0NBQW9DQTtZQUNsRCxPQUFPO1FBQ1Q7UUFFQSxPQUFPO0lBQ1QsRUFBRSxPQUFPQSxPQUFPO1FBQ2RFLFFBQVFGLEtBQUssQ0FBQyxvQ0FBb0NBO1FBQ2xELE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxmZWF0dXJlc1xccGxhbmlmaWNhY2lvblxcc2VydmljZXNcXHBsYW5pZmljYWNpb25TZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN1cGFiYXNlIH0gZnJvbSAnQC9saWIvc3VwYWJhc2UvY2xpZW50JztcbmltcG9ydCB7IG9idGVuZXJVc3VhcmlvQWN0dWFsIH0gZnJvbSAnQC9mZWF0dXJlcy9hdXRoL3NlcnZpY2VzL2F1dGhTZXJ2aWNlJztcbmltcG9ydCB7IFBsYW5pZmljYWNpb25Vc3VhcmlvLCBFc3RpbWFjaW9uVGVtYSB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL3N1cGFiYXNlQ2xpZW50JztcblxuLyoqXG4gKiBWZXJpZmljYSBzaSBlbCB1c3VhcmlvIHRpZW5lIHVuYSBwbGFuaWZpY2FjacOzbiBjb25maWd1cmFkYVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdGllbmVQbGFuaWZpY2FjaW9uQ29uZmlndXJhZGEodGVtYXJpb0lkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHVzZXIsIGVycm9yOiBhdXRoRXJyb3IgfSA9IGF3YWl0IG9idGVuZXJVc3VhcmlvQWN0dWFsKCk7XG4gICAgaWYgKCF1c2VyIHx8IGF1dGhFcnJvcikge1xuICAgICAgY29uc29sZS5sb2coJ05vIGhheSB1c3VhcmlvIGF1dGVudGljYWRvIG8gZXJyb3I6JywgYXV0aEVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3BsYW5pZmljYWNpb25fdXN1YXJpbycpXG4gICAgICAuc2VsZWN0KCdpZCcpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLmVxKCd0ZW1hcmlvX2lkJywgdGVtYXJpb0lkKVxuICAgICAgLmVxKCdjb21wbGV0YWRvJywgdHJ1ZSlcbiAgICAgIC5saW1pdCgxKTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgdmVyaWZpY2FyIHBsYW5pZmljYWNpw7NuOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSAmJiBkYXRhLmxlbmd0aCA+IDA7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgdmVyaWZpY2FyIHBsYW5pZmljYWNpw7NuOicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn1cblxuLyoqXG4gKiBPYnRpZW5lIGxhIHBsYW5pZmljYWNpw7NuIGRlbCB1c3VhcmlvIHBhcmEgdW4gdGVtYXJpb1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gb2J0ZW5lclBsYW5pZmljYWNpb25Vc3VhcmlvKHRlbWFyaW9JZDogc3RyaW5nKTogUHJvbWlzZTxQbGFuaWZpY2FjaW9uVXN1YXJpbyB8IG51bGw+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHVzZXIsIGVycm9yOiBhdXRoRXJyb3IgfSA9IGF3YWl0IG9idGVuZXJVc3VhcmlvQWN0dWFsKCk7XG4gICAgaWYgKCF1c2VyIHx8IGF1dGhFcnJvcikge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdwbGFuaWZpY2FjaW9uX3VzdWFyaW8nKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLmVxKCd0ZW1hcmlvX2lkJywgdGVtYXJpb0lkKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBpZiAoZXJyb3IuY29kZSA9PT0gJ1BHUlNUMTE2Jykge1xuICAgICAgICByZXR1cm4gbnVsbDsgLy8gTm8gaGF5IHBsYW5pZmljYWNpw7NuXG4gICAgICB9XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBvYnRlbmVyIHBsYW5pZmljYWNpw7NuOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIHJldHVybiBkYXRhO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIG9idGVuZXIgcGxhbmlmaWNhY2nDs246JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbi8qKlxuICogQ3JlYSBvIGFjdHVhbGl6YSBsYSBwbGFuaWZpY2FjacOzbiBkZWwgdXN1YXJpb1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ3VhcmRhclBsYW5pZmljYWNpb25Vc3VhcmlvKFxuICB0ZW1hcmlvSWQ6IHN0cmluZyxcbiAgcGxhbmlmaWNhY2lvbjoge1xuICAgIHRpZW1wb19kaWFyaW9fcHJvbWVkaW8/OiBudW1iZXI7XG4gICAgdGllbXBvX3Bvcl9kaWE/OiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+O1xuICAgIGZlY2hhX2V4YW1lbj86IHN0cmluZztcbiAgICBmZWNoYV9leGFtZW5fYXByb3hpbWFkYT86IHN0cmluZztcbiAgICBmYW1pbGlhcmlkYWRfZ2VuZXJhbD86IG51bWJlcjtcbiAgICBwcmVmZXJlbmNpYXNfaG9yYXJpbz86IHN0cmluZ1tdO1xuICAgIGZyZWN1ZW5jaWFfcmVwYXNvcz86IHN0cmluZztcbiAgfVxuKTogUHJvbWlzZTxzdHJpbmcgfCBudWxsPiB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyB1c2VyLCBlcnJvcjogYXV0aEVycm9yIH0gPSBhd2FpdCBvYnRlbmVyVXN1YXJpb0FjdHVhbCgpO1xuICAgIGlmICghdXNlciB8fCBhdXRoRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ05vIGhheSB1c3VhcmlvIGF1dGVudGljYWRvJyk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICAvLyBWZXJpZmljYXIgc2kgeWEgZXhpc3RlIHVuYSBwbGFuaWZpY2FjacOzblxuICAgIGNvbnN0IHBsYW5pZmljYWNpb25FeGlzdGVudGUgPSBhd2FpdCBvYnRlbmVyUGxhbmlmaWNhY2lvblVzdWFyaW8odGVtYXJpb0lkKTtcblxuICAgIGlmIChwbGFuaWZpY2FjaW9uRXhpc3RlbnRlKSB7XG4gICAgICAvLyBBY3R1YWxpemFyIHBsYW5pZmljYWNpw7NuIGV4aXN0ZW50ZVxuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ3BsYW5pZmljYWNpb25fdXN1YXJpbycpXG4gICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgIC4uLnBsYW5pZmljYWNpb24sXG4gICAgICAgICAgY29tcGxldGFkbzogdHJ1ZSxcbiAgICAgICAgICBhY3R1YWxpemFkb19lbjogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgIH0pXG4gICAgICAgIC5lcSgnaWQnLCBwbGFuaWZpY2FjaW9uRXhpc3RlbnRlLmlkKVxuICAgICAgICAuc2VsZWN0KClcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgYWN0dWFsaXphciBwbGFuaWZpY2FjacOzbjonLCBlcnJvcik7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gZGF0YS5pZDtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gQ3JlYXIgbnVldmEgcGxhbmlmaWNhY2nDs25cbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCdwbGFuaWZpY2FjaW9uX3VzdWFyaW8nKVxuICAgICAgICAuaW5zZXJ0KFt7XG4gICAgICAgICAgdXNlcl9pZDogdXNlci5pZCxcbiAgICAgICAgICB0ZW1hcmlvX2lkOiB0ZW1hcmlvSWQsXG4gICAgICAgICAgLi4ucGxhbmlmaWNhY2lvbixcbiAgICAgICAgICBjb21wbGV0YWRvOiB0cnVlXG4gICAgICAgIH1dKVxuICAgICAgICAuc2VsZWN0KClcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgY3JlYXIgcGxhbmlmaWNhY2nDs246JywgZXJyb3IpO1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGRhdGEuaWQ7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGd1YXJkYXIgcGxhbmlmaWNhY2nDs246JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbi8qKlxuICogT2J0aWVuZSBsYXMgZXN0aW1hY2lvbmVzIGRlIHRlbWFzIHBhcmEgdW5hIHBsYW5pZmljYWNpw7NuXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBvYnRlbmVyRXN0aW1hY2lvbmVzVGVtYXMocGxhbmlmaWNhY2lvbklkOiBzdHJpbmcpOiBQcm9taXNlPEVzdGltYWNpb25UZW1hW10+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2VzdGltYWNpb25lc190ZW1hcycpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5lcSgncGxhbmlmaWNhY2lvbl9pZCcsIHBsYW5pZmljYWNpb25JZCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIG9idGVuZXIgZXN0aW1hY2lvbmVzIGRlIHRlbWFzOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSB8fCBbXTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBvYnRlbmVyIGVzdGltYWNpb25lcyBkZSB0ZW1hczonLCBlcnJvcik7XG4gICAgcmV0dXJuIFtdO1xuICB9XG59XG5cbi8qKlxuICogR3VhcmRhIGxhcyBlc3RpbWFjaW9uZXMgZGUgdGVtYXNcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGd1YXJkYXJFc3RpbWFjaW9uZXNUZW1hcyhcbiAgcGxhbmlmaWNhY2lvbklkOiBzdHJpbmcsXG4gIGVzdGltYWNpb25lczogQXJyYXk8e1xuICAgIHRlbWFfaWQ6IHN0cmluZztcbiAgICBob3Jhc19lc3RpbWFkYXM/OiBudW1iZXI7XG4gICAgZXNfZGlmaWNpbDogYm9vbGVhbjtcbiAgICBlc19tdXlfaW1wb3J0YW50ZTogYm9vbGVhbjtcbiAgICB5YV9kb21pbmFkbzogYm9vbGVhbjtcbiAgICBub3Rhcz86IHN0cmluZztcbiAgfT5cbik6IFByb21pc2U8Ym9vbGVhbj4ge1xuICB0cnkge1xuICAgIC8vIEVsaW1pbmFyIGVzdGltYWNpb25lcyBleGlzdGVudGVzXG4gICAgYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdlc3RpbWFjaW9uZXNfdGVtYXMnKVxuICAgICAgLmRlbGV0ZSgpXG4gICAgICAuZXEoJ3BsYW5pZmljYWNpb25faWQnLCBwbGFuaWZpY2FjaW9uSWQpO1xuXG4gICAgLy8gSW5zZXJ0YXIgbnVldmFzIGVzdGltYWNpb25lc1xuICAgIGNvbnN0IGVzdGltYWNpb25lc0NvblBsYW5pZmljYWNpb24gPSBlc3RpbWFjaW9uZXMubWFwKGVzdCA9PiAoe1xuICAgICAgLi4uZXN0LFxuICAgICAgcGxhbmlmaWNhY2lvbl9pZDogcGxhbmlmaWNhY2lvbklkXG4gICAgfSkpO1xuXG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdlc3RpbWFjaW9uZXNfdGVtYXMnKVxuICAgICAgLmluc2VydChlc3RpbWFjaW9uZXNDb25QbGFuaWZpY2FjaW9uKTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgZ3VhcmRhciBlc3RpbWFjaW9uZXMgZGUgdGVtYXM6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cblxuICAgIHJldHVybiB0cnVlO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGd1YXJkYXIgZXN0aW1hY2lvbmVzIGRlIHRlbWFzOicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn1cblxuLyoqXG4gKiBFbGltaW5hIHVuYSBwbGFuaWZpY2FjacOzbiB5IHRvZGFzIHN1cyBlc3RpbWFjaW9uZXNcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGVsaW1pbmFyUGxhbmlmaWNhY2lvbihwbGFuaWZpY2FjaW9uSWQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICB0cnkge1xuICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgncGxhbmlmaWNhY2lvbl91c3VhcmlvJylcbiAgICAgIC5kZWxldGUoKVxuICAgICAgLmVxKCdpZCcsIHBsYW5pZmljYWNpb25JZCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGVsaW1pbmFyIHBsYW5pZmljYWNpw7NuOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBlbGltaW5hciBwbGFuaWZpY2FjacOzbjonLCBlcnJvcik7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59XG4iXSwibmFtZXMiOlsic3VwYWJhc2UiLCJvYnRlbmVyVXN1YXJpb0FjdHVhbCIsInRpZW5lUGxhbmlmaWNhY2lvbkNvbmZpZ3VyYWRhIiwidGVtYXJpb0lkIiwidXNlciIsImVycm9yIiwiYXV0aEVycm9yIiwiY29uc29sZSIsImxvZyIsImRhdGEiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJpZCIsImxpbWl0IiwibGVuZ3RoIiwib2J0ZW5lclBsYW5pZmljYWNpb25Vc3VhcmlvIiwic2luZ2xlIiwiY29kZSIsImd1YXJkYXJQbGFuaWZpY2FjaW9uVXN1YXJpbyIsInBsYW5pZmljYWNpb24iLCJwbGFuaWZpY2FjaW9uRXhpc3RlbnRlIiwidXBkYXRlIiwiY29tcGxldGFkbyIsImFjdHVhbGl6YWRvX2VuIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiaW5zZXJ0IiwidXNlcl9pZCIsInRlbWFyaW9faWQiLCJvYnRlbmVyRXN0aW1hY2lvbmVzVGVtYXMiLCJwbGFuaWZpY2FjaW9uSWQiLCJndWFyZGFyRXN0aW1hY2lvbmVzVGVtYXMiLCJlc3RpbWFjaW9uZXMiLCJkZWxldGUiLCJlc3RpbWFjaW9uZXNDb25QbGFuaWZpY2FjaW9uIiwibWFwIiwiZXN0IiwicGxhbmlmaWNhY2lvbl9pZCIsImVsaW1pbmFyUGxhbmlmaWNhY2lvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/features/planificacion/services/planificacionService.ts\n");

/***/ }),

/***/ "(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/shared/components/BackgroundTasksPanel.tsx ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst BackgroundTasksPanel = ()=>{\n    const { activeTasks, completedTasks, removeTask, clearCompletedTasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__.useBackgroundTasks)();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCompleted, setShowCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const totalTasks = activeTasks.length + completedTasks.length;\n    if (totalTasks === 0) {\n        return null;\n    }\n    const getTaskIcon = (task)=>{\n        switch(task.status){\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 16\n                }, undefined);\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 16\n                }, undefined);\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 16\n                }, undefined);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTaskTypeLabel = (type)=>{\n        switch(type){\n            case 'mapa-mental':\n                return 'Mapa Mental';\n            case 'test':\n                return 'Test';\n            case 'flashcards':\n                return 'Flashcards';\n            default:\n                return 'Tarea';\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('es-ES', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 max-w-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between\",\n                    onClick: ()=>setIsExpanded(!isExpanded),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"Tareas (\",\n                                        activeTasks.length,\n                                        \" activas)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-96 overflow-y-auto\",\n                    children: [\n                        activeTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border-b border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold text-gray-700 mb-2\",\n                                    children: \"Tareas Activas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: activeTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        task.progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-200 rounded-full h-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-blue-500 h-1.5 rounded-full transition-all duration-300\",\n                                                                    style: {\n                                                                        width: `${task.progress}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: formatTime(task.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, undefined),\n                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompleted(!showCompleted),\n                                            className: \"text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Completadas (\",\n                                                        completedTasks.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                showCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearCompletedTasks,\n                                            className: \"text-xs text-gray-500 hover:text-red-600 transition-colors\",\n                                            children: \"Limpiar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, undefined),\n                                showCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: completedTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        task.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-500 truncate\",\n                                                            children: task.error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: task.completedAt ? formatTime(task.completedAt) : formatTime(task.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeTask(task.id),\n                                                            className: \"text-gray-400 hover:text-red-500 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 23\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackgroundTasksPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../auth/components/AuthManager */ \"(ssr)/./src/features/auth/components/AuthManager.tsx\");\n/* harmony import */ var _BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./BackgroundTasksPanel */ \"(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n // Importar Toaster\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__.BackgroundTasksProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.Toaster, {\n                    position: \"top-right\" // Posición de los toasts\n                    ,\n                    toastOptions: {\n                        // Opciones por defecto para los toasts\n                        duration: 5000,\n                        style: {\n                            background: '#363636',\n                            color: '#fff'\n                        },\n                        success: {\n                            duration: 3000,\n                            style: {\n                                background: '#10b981',\n                                color: '#fff'\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            style: {\n                                background: '#ef4444',\n                                color: '#fff'\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/shared/components/ClientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/temario/services/temarioService.ts":
/*!*********************************************************!*\
  !*** ./src/features/temario/services/temarioService.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarEstadoTema: () => (/* binding */ actualizarEstadoTema),\n/* harmony export */   actualizarTema: () => (/* binding */ actualizarTema),\n/* harmony export */   actualizarTemario: () => (/* binding */ actualizarTemario),\n/* harmony export */   crearTemario: () => (/* binding */ crearTemario),\n/* harmony export */   crearTemas: () => (/* binding */ crearTemas),\n/* harmony export */   eliminarTema: () => (/* binding */ eliminarTema),\n/* harmony export */   eliminarTemario: () => (/* binding */ eliminarTemario),\n/* harmony export */   obtenerEstadisticasTemario: () => (/* binding */ obtenerEstadisticasTemario),\n/* harmony export */   obtenerTemarioUsuario: () => (/* binding */ obtenerTemarioUsuario),\n/* harmony export */   obtenerTemas: () => (/* binding */ obtenerTemas),\n/* harmony export */   tieneTemarioConfigurado: () => (/* binding */ tieneTemarioConfigurado)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(ssr)/./src/features/auth/services/authService.ts\");\n\n\n/**\n * Verifica si el usuario tiene un temario configurado\n */ async function tieneTemarioConfigurado() {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.log('No hay usuario autenticado o error:', authError);\n            return false;\n        }\n        console.log('Verificando temario para usuario:', user.id);\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').select('id').eq('user_id', user.id).limit(1);\n        if (error) {\n            console.error('Error al verificar temario en Supabase:', error);\n            // Si es un error de tabla no encontrada, devolver false sin error\n            if (error.code === 'PGRST116' || error.message?.includes('relation') || error.message?.includes('does not exist')) {\n                console.log('Tabla temarios no encontrada, devolviendo false');\n                return false;\n            }\n            return false;\n        }\n        const tieneTemario = data && data.length > 0;\n        console.log('Resultado verificación temario:', tieneTemario);\n        return tieneTemario;\n    } catch (error) {\n        console.error('Error general al verificar temario:', error);\n        return false;\n    }\n}\n/**\n * Obtiene el temario del usuario actual\n */ async function obtenerTemarioUsuario() {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.log('No hay usuario autenticado para obtener temario o error:', authError);\n            return null;\n        }\n        console.log('Obteniendo temario para usuario:', user.id);\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').select('*').eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener temario en Supabase:', error);\n            // Si no hay datos, es normal (usuario sin temario)\n            if (error.code === 'PGRST116') {\n                console.log('Usuario no tiene temario configurado');\n                return null;\n            }\n            return null;\n        }\n        console.log('Temario obtenido:', data);\n        return data;\n    } catch (error) {\n        console.error('Error general al obtener temario:', error);\n        return null;\n    }\n}\n/**\n * Crea un nuevo temario\n */ async function crearTemario(titulo, descripcion, tipo) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.error('No hay usuario autenticado o error:', authError);\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').insert([\n            {\n                titulo,\n                descripcion,\n                tipo,\n                user_id: user.id\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al crear temario:', error);\n            return null;\n        }\n        return data.id;\n    } catch (error) {\n        console.error('Error al crear temario:', error);\n        return null;\n    }\n}\n/**\n * Obtiene los temas de un temario\n */ async function obtenerTemas(temarioId) {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').select('*').eq('temario_id', temarioId).order('orden', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener temas:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener temas:', error);\n        return [];\n    }\n}\n/**\n * Crea múltiples temas para un temario\n */ async function crearTemas(temarioId, temas) {\n    try {\n        const temasConTemarioId = temas.map((tema)=>({\n                ...tema,\n                temario_id: temarioId\n            }));\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').insert(temasConTemarioId);\n        if (error) {\n            console.error('Error al crear temas:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al crear temas:', error);\n        return false;\n    }\n}\n/**\n * Actualiza los datos básicos de un temario\n */ async function actualizarTemario(temarioId, titulo, descripcion) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').update({\n            titulo,\n            descripcion,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', temarioId);\n        if (error) {\n            console.error('Error al actualizar temario:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar temario:', error);\n        return false;\n    }\n}\n/**\n * Actualiza los datos de un tema\n */ async function actualizarTema(temaId, titulo, descripcion) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').update({\n            titulo,\n            descripcion,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', temaId);\n        if (error) {\n            console.error('Error al actualizar tema:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar tema:', error);\n        return false;\n    }\n}\n/**\n * Elimina un tema\n */ async function eliminarTema(temaId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').delete().eq('id', temaId);\n        if (error) {\n            console.error('Error al eliminar tema:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar tema:', error);\n        return false;\n    }\n}\n/**\n * Actualiza el estado de completado de un tema\n */ async function actualizarEstadoTema(temaId, completado) {\n    try {\n        const updateData = {\n            completado,\n            actualizado_en: new Date().toISOString()\n        };\n        if (completado) {\n            updateData.fecha_completado = new Date().toISOString();\n        } else {\n            updateData.fecha_completado = null;\n        }\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').update(updateData).eq('id', temaId);\n        if (error) {\n            console.error('Error al actualizar estado del tema:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar estado del tema:', error);\n        return false;\n    }\n}\n/**\n * Obtiene estadísticas del temario\n */ async function obtenerEstadisticasTemario(temarioId) {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').select('completado').eq('temario_id', temarioId);\n        if (error) {\n            console.error('Error al obtener estadísticas del temario:', error);\n            return null;\n        }\n        const totalTemas = data.length;\n        const temasCompletados = data.filter((tema)=>tema.completado).length;\n        const porcentajeCompletado = totalTemas > 0 ? temasCompletados / totalTemas * 100 : 0;\n        return {\n            totalTemas,\n            temasCompletados,\n            porcentajeCompletado\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas del temario:', error);\n        return null;\n    }\n}\n/**\n * Elimina un temario y todos sus temas asociados\n */ async function eliminarTemario(temarioId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').delete().eq('id', temarioId);\n        if (error) {\n            console.error('Error al eliminar temario:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar temario:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/temario/services/temarioService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/authService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabase/authService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n\n/**\n * Inicia sesión con email y contraseña\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            error: errorMessage\n        };\n    }\n}\n/**\n * Cierra la sesión del usuario actual\n */ async function cerrarSesion() {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            return {\n                error: error.message\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\n * Obtiene la sesión actual del usuario\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\n * Obtiene el usuario actual\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\n * Verifica si el usuario está autenticado\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\");\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0Q7QUFFcEQsc0RBQXNEO0FBQy9DLFNBQVNDO0lBQ2QsT0FBT0Qsa0VBQW1CQSxDQUN4QkUsMENBQW9DLEVBQ3BDQSxrTkFBeUM7QUFFN0M7QUFFQSwrQ0FBK0M7QUFDeEMsTUFBTUksV0FBV0wsZUFBZSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXHNyY1xcbGliXFxzdXBhYmFzZVxcY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcblxuLy8gQ2xpZW50ZSBwYXJhIGVsIG5hdmVnYWRvciAoY29tcG9uZW50ZXMgZGVsIGNsaWVudGUpXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuICApO1xufVxuXG4vLyBNYW50ZW5lciBjb21wYXRpYmlsaWRhZCBjb24gY8OzZGlnbyBleGlzdGVudGVcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJjcmVhdGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJzdXBhYmFzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/supabaseClient.ts":
/*!********************************************!*\
  !*** ./src/lib/supabase/supabaseClient.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   supabase: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/supabase/client.ts\");\n// Solo re-exportar el cliente del navegador para mantener compatibilidad\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/supabaseClient.ts\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=FiCalendar,FiDownload,FiPrint,FiRefreshCw,FiTarget!=!./node_modules/react-icons/fi/index.mjs":
/*!******************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiCalendar,FiDownload,FiPrint,FiRefreshCw,FiTarget!=!./node_modules/react-icons/fi/index.mjs ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FiActivity: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiActivity),\n/* harmony export */   FiAirplay: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAirplay),\n/* harmony export */   FiAlertCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAlertCircle),\n/* harmony export */   FiAlertOctagon: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAlertOctagon),\n/* harmony export */   FiAlertTriangle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAlertTriangle),\n/* harmony export */   FiAlignCenter: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAlignCenter),\n/* harmony export */   FiAlignJustify: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAlignJustify),\n/* harmony export */   FiAlignLeft: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAlignLeft),\n/* harmony export */   FiAlignRight: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAlignRight),\n/* harmony export */   FiAnchor: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAnchor),\n/* harmony export */   FiAperture: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAperture),\n/* harmony export */   FiArchive: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArchive),\n/* harmony export */   FiArrowDown: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowDown),\n/* harmony export */   FiArrowDownCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowDownCircle),\n/* harmony export */   FiArrowDownLeft: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowDownLeft),\n/* harmony export */   FiArrowDownRight: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowDownRight),\n/* harmony export */   FiArrowLeft: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowLeft),\n/* harmony export */   FiArrowLeftCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowLeftCircle),\n/* harmony export */   FiArrowRight: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowRight),\n/* harmony export */   FiArrowRightCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowRightCircle),\n/* harmony export */   FiArrowUp: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowUp),\n/* harmony export */   FiArrowUpCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowUpCircle),\n/* harmony export */   FiArrowUpLeft: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowUpLeft),\n/* harmony export */   FiArrowUpRight: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiArrowUpRight),\n/* harmony export */   FiAtSign: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAtSign),\n/* harmony export */   FiAward: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiAward),\n/* harmony export */   FiBarChart: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBarChart),\n/* harmony export */   FiBarChart2: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBarChart2),\n/* harmony export */   FiBattery: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBattery),\n/* harmony export */   FiBatteryCharging: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBatteryCharging),\n/* harmony export */   FiBell: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBell),\n/* harmony export */   FiBellOff: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBellOff),\n/* harmony export */   FiBluetooth: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBluetooth),\n/* harmony export */   FiBold: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBold),\n/* harmony export */   FiBook: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBook),\n/* harmony export */   FiBookOpen: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBookOpen),\n/* harmony export */   FiBookmark: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBookmark),\n/* harmony export */   FiBox: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBox),\n/* harmony export */   FiBriefcase: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiBriefcase),\n/* harmony export */   FiCalendar: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCalendar),\n/* harmony export */   FiCamera: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCamera),\n/* harmony export */   FiCameraOff: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCameraOff),\n/* harmony export */   FiCast: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCast),\n/* harmony export */   FiCheck: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCheck),\n/* harmony export */   FiCheckCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCheckCircle),\n/* harmony export */   FiCheckSquare: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCheckSquare),\n/* harmony export */   FiChevronDown: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiChevronDown),\n/* harmony export */   FiChevronLeft: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiChevronLeft),\n/* harmony export */   FiChevronRight: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiChevronRight),\n/* harmony export */   FiChevronUp: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiChevronUp),\n/* harmony export */   FiChevronsDown: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiChevronsDown),\n/* harmony export */   FiChevronsLeft: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiChevronsLeft),\n/* harmony export */   FiChevronsRight: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiChevronsRight),\n/* harmony export */   FiChevronsUp: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiChevronsUp),\n/* harmony export */   FiChrome: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiChrome),\n/* harmony export */   FiCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCircle),\n/* harmony export */   FiClipboard: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiClipboard),\n/* harmony export */   FiClock: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiClock),\n/* harmony export */   FiCloud: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCloud),\n/* harmony export */   FiCloudDrizzle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCloudDrizzle),\n/* harmony export */   FiCloudLightning: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCloudLightning),\n/* harmony export */   FiCloudOff: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCloudOff),\n/* harmony export */   FiCloudRain: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCloudRain),\n/* harmony export */   FiCloudSnow: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCloudSnow),\n/* harmony export */   FiCode: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCode),\n/* harmony export */   FiCodepen: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCodepen),\n/* harmony export */   FiCodesandbox: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCodesandbox),\n/* harmony export */   FiCoffee: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCoffee),\n/* harmony export */   FiColumns: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiColumns),\n/* harmony export */   FiCommand: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCommand),\n/* harmony export */   FiCompass: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCompass),\n/* harmony export */   FiCopy: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCopy),\n/* harmony export */   FiCornerDownLeft: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCornerDownLeft),\n/* harmony export */   FiCornerDownRight: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCornerDownRight),\n/* harmony export */   FiCornerLeftDown: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCornerLeftDown),\n/* harmony export */   FiCornerLeftUp: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCornerLeftUp),\n/* harmony export */   FiCornerRightDown: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCornerRightDown),\n/* harmony export */   FiCornerRightUp: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCornerRightUp),\n/* harmony export */   FiCornerUpLeft: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCornerUpLeft),\n/* harmony export */   FiCornerUpRight: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCornerUpRight),\n/* harmony export */   FiCpu: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCpu),\n/* harmony export */   FiCreditCard: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCreditCard),\n/* harmony export */   FiCrop: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCrop),\n/* harmony export */   FiCrosshair: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiCrosshair),\n/* harmony export */   FiDatabase: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDatabase),\n/* harmony export */   FiDelete: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDelete),\n/* harmony export */   FiDisc: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDisc),\n/* harmony export */   FiDivide: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDivide),\n/* harmony export */   FiDivideCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDivideCircle),\n/* harmony export */   FiDivideSquare: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDivideSquare),\n/* harmony export */   FiDollarSign: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDollarSign),\n/* harmony export */   FiDownload: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDownload),\n/* harmony export */   FiDownloadCloud: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDownloadCloud),\n/* harmony export */   FiDribbble: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDribbble),\n/* harmony export */   FiDroplet: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiDroplet),\n/* harmony export */   FiEdit: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiEdit),\n/* harmony export */   FiEdit2: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiEdit2),\n/* harmony export */   FiEdit3: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiEdit3),\n/* harmony export */   FiExternalLink: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiExternalLink),\n/* harmony export */   FiEye: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiEye),\n/* harmony export */   FiEyeOff: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiEyeOff),\n/* harmony export */   FiFacebook: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFacebook),\n/* harmony export */   FiFastForward: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFastForward),\n/* harmony export */   FiFeather: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFeather),\n/* harmony export */   FiFigma: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFigma),\n/* harmony export */   FiFile: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFile),\n/* harmony export */   FiFileMinus: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFileMinus),\n/* harmony export */   FiFilePlus: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFilePlus),\n/* harmony export */   FiFileText: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFileText),\n/* harmony export */   FiFilm: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFilm),\n/* harmony export */   FiFilter: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFilter),\n/* harmony export */   FiFlag: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFlag),\n/* harmony export */   FiFolder: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFolder),\n/* harmony export */   FiFolderMinus: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFolderMinus),\n/* harmony export */   FiFolderPlus: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFolderPlus),\n/* harmony export */   FiFramer: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFramer),\n/* harmony export */   FiFrown: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiFrown),\n/* harmony export */   FiGift: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiGift),\n/* harmony export */   FiGitBranch: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiGitBranch),\n/* harmony export */   FiGitCommit: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiGitCommit),\n/* harmony export */   FiGitMerge: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiGitMerge),\n/* harmony export */   FiGitPullRequest: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiGitPullRequest),\n/* harmony export */   FiGithub: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiGithub),\n/* harmony export */   FiGitlab: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiGitlab),\n/* harmony export */   FiGlobe: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiGlobe),\n/* harmony export */   FiGrid: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiGrid),\n/* harmony export */   FiHardDrive: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiHardDrive),\n/* harmony export */   FiHash: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiHash),\n/* harmony export */   FiHeadphones: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiHeadphones),\n/* harmony export */   FiHeart: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiHeart),\n/* harmony export */   FiHelpCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiHelpCircle),\n/* harmony export */   FiHexagon: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiHexagon),\n/* harmony export */   FiHome: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiHome),\n/* harmony export */   FiImage: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiImage),\n/* harmony export */   FiInbox: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiInbox),\n/* harmony export */   FiInfo: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiInfo),\n/* harmony export */   FiInstagram: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiInstagram),\n/* harmony export */   FiItalic: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiItalic),\n/* harmony export */   FiKey: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiKey),\n/* harmony export */   FiLayers: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiLayers),\n/* harmony export */   FiLayout: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiLayout),\n/* harmony export */   FiLifeBuoy: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiLifeBuoy),\n/* harmony export */   FiLink: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiLink),\n/* harmony export */   FiLink2: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiLink2),\n/* harmony export */   FiLinkedin: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiLinkedin),\n/* harmony export */   FiList: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiList),\n/* harmony export */   FiLoader: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiLoader),\n/* harmony export */   FiLock: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiLock),\n/* harmony export */   FiLogIn: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiLogIn),\n/* harmony export */   FiLogOut: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiLogOut),\n/* harmony export */   FiMail: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMail),\n/* harmony export */   FiMap: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMap),\n/* harmony export */   FiMapPin: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMapPin),\n/* harmony export */   FiMaximize: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMaximize),\n/* harmony export */   FiMaximize2: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMaximize2),\n/* harmony export */   FiMeh: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMeh),\n/* harmony export */   FiMenu: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMenu),\n/* harmony export */   FiMessageCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMessageCircle),\n/* harmony export */   FiMessageSquare: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMessageSquare),\n/* harmony export */   FiMic: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMic),\n/* harmony export */   FiMicOff: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMicOff),\n/* harmony export */   FiMinimize: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMinimize),\n/* harmony export */   FiMinimize2: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMinimize2),\n/* harmony export */   FiMinus: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMinus),\n/* harmony export */   FiMinusCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMinusCircle),\n/* harmony export */   FiMinusSquare: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMinusSquare),\n/* harmony export */   FiMonitor: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMonitor),\n/* harmony export */   FiMoon: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMoon),\n/* harmony export */   FiMoreHorizontal: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMoreHorizontal),\n/* harmony export */   FiMoreVertical: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMoreVertical),\n/* harmony export */   FiMousePointer: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMousePointer),\n/* harmony export */   FiMove: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMove),\n/* harmony export */   FiMusic: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiMusic),\n/* harmony export */   FiNavigation: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiNavigation),\n/* harmony export */   FiNavigation2: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiNavigation2),\n/* harmony export */   FiOctagon: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiOctagon),\n/* harmony export */   FiPackage: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPackage),\n/* harmony export */   FiPaperclip: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPaperclip),\n/* harmony export */   FiPause: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPause),\n/* harmony export */   FiPauseCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPauseCircle),\n/* harmony export */   FiPenTool: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPenTool),\n/* harmony export */   FiPercent: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPercent),\n/* harmony export */   FiPhone: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPhone),\n/* harmony export */   FiPhoneCall: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPhoneCall),\n/* harmony export */   FiPhoneForwarded: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPhoneForwarded),\n/* harmony export */   FiPhoneIncoming: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPhoneIncoming),\n/* harmony export */   FiPhoneMissed: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPhoneMissed),\n/* harmony export */   FiPhoneOff: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPhoneOff),\n/* harmony export */   FiPhoneOutgoing: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPhoneOutgoing),\n/* harmony export */   FiPieChart: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPieChart),\n/* harmony export */   FiPlay: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPlay),\n/* harmony export */   FiPlayCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPlayCircle),\n/* harmony export */   FiPlus: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPlus),\n/* harmony export */   FiPlusCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPlusCircle),\n/* harmony export */   FiPlusSquare: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPlusSquare),\n/* harmony export */   FiPocket: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPocket),\n/* harmony export */   FiPower: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPower),\n/* harmony export */   FiPrinter: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiPrinter),\n/* harmony export */   FiRadio: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiRadio),\n/* harmony export */   FiRefreshCcw: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiRefreshCcw),\n/* harmony export */   FiRefreshCw: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiRefreshCw),\n/* harmony export */   FiRepeat: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiRepeat),\n/* harmony export */   FiRewind: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiRewind),\n/* harmony export */   FiRotateCcw: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiRotateCcw),\n/* harmony export */   FiRotateCw: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiRotateCw),\n/* harmony export */   FiRss: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiRss),\n/* harmony export */   FiSave: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSave),\n/* harmony export */   FiScissors: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiScissors),\n/* harmony export */   FiSearch: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSearch),\n/* harmony export */   FiSend: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSend),\n/* harmony export */   FiServer: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiServer),\n/* harmony export */   FiSettings: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSettings),\n/* harmony export */   FiShare: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiShare),\n/* harmony export */   FiShare2: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiShare2),\n/* harmony export */   FiShield: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiShield),\n/* harmony export */   FiShieldOff: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiShieldOff),\n/* harmony export */   FiShoppingBag: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiShoppingBag),\n/* harmony export */   FiShoppingCart: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiShoppingCart),\n/* harmony export */   FiShuffle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiShuffle),\n/* harmony export */   FiSidebar: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSidebar),\n/* harmony export */   FiSkipBack: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSkipBack),\n/* harmony export */   FiSkipForward: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSkipForward),\n/* harmony export */   FiSlack: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSlack),\n/* harmony export */   FiSlash: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSlash),\n/* harmony export */   FiSliders: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSliders),\n/* harmony export */   FiSmartphone: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSmartphone),\n/* harmony export */   FiSmile: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSmile),\n/* harmony export */   FiSpeaker: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSpeaker),\n/* harmony export */   FiSquare: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSquare),\n/* harmony export */   FiStar: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiStar),\n/* harmony export */   FiStopCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiStopCircle),\n/* harmony export */   FiSun: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSun),\n/* harmony export */   FiSunrise: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSunrise),\n/* harmony export */   FiSunset: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiSunset),\n/* harmony export */   FiTable: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTable),\n/* harmony export */   FiTablet: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTablet),\n/* harmony export */   FiTag: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTag),\n/* harmony export */   FiTarget: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTarget),\n/* harmony export */   FiTerminal: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTerminal),\n/* harmony export */   FiThermometer: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiThermometer),\n/* harmony export */   FiThumbsDown: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiThumbsDown),\n/* harmony export */   FiThumbsUp: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiThumbsUp),\n/* harmony export */   FiToggleLeft: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiToggleLeft),\n/* harmony export */   FiToggleRight: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiToggleRight),\n/* harmony export */   FiTool: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTool),\n/* harmony export */   FiTrash: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTrash),\n/* harmony export */   FiTrash2: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTrash2),\n/* harmony export */   FiTrello: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTrello),\n/* harmony export */   FiTrendingDown: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTrendingDown),\n/* harmony export */   FiTrendingUp: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTrendingUp),\n/* harmony export */   FiTriangle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTriangle),\n/* harmony export */   FiTruck: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTruck),\n/* harmony export */   FiTv: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTv),\n/* harmony export */   FiTwitch: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTwitch),\n/* harmony export */   FiTwitter: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiTwitter),\n/* harmony export */   FiType: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiType),\n/* harmony export */   FiUmbrella: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUmbrella),\n/* harmony export */   FiUnderline: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUnderline),\n/* harmony export */   FiUnlock: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUnlock),\n/* harmony export */   FiUpload: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUpload),\n/* harmony export */   FiUploadCloud: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUploadCloud),\n/* harmony export */   FiUser: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUser),\n/* harmony export */   FiUserCheck: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUserCheck),\n/* harmony export */   FiUserMinus: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUserMinus),\n/* harmony export */   FiUserPlus: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUserPlus),\n/* harmony export */   FiUserX: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUserX),\n/* harmony export */   FiUsers: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiUsers),\n/* harmony export */   FiVideo: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiVideo),\n/* harmony export */   FiVideoOff: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiVideoOff),\n/* harmony export */   FiVoicemail: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiVoicemail),\n/* harmony export */   FiVolume: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiVolume),\n/* harmony export */   FiVolume1: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiVolume1),\n/* harmony export */   FiVolume2: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiVolume2),\n/* harmony export */   FiVolumeX: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiVolumeX),\n/* harmony export */   FiWatch: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiWatch),\n/* harmony export */   FiWifi: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiWifi),\n/* harmony export */   FiWifiOff: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiWifiOff),\n/* harmony export */   FiWind: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiWind),\n/* harmony export */   FiX: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiX),\n/* harmony export */   FiXCircle: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiXCircle),\n/* harmony export */   FiXOctagon: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiXOctagon),\n/* harmony export */   FiXSquare: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiXSquare),\n/* harmony export */   FiYoutube: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiYoutube),\n/* harmony export */   FiZap: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiZap),\n/* harmony export */   FiZapOff: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiZapOff),\n/* harmony export */   FiZoomIn: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiZoomIn),\n/* harmony export */   FiZoomOut: () => (/* reexport safe */ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FiZoomOut)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.mjs */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1GaUNhbGVuZGFyLEZpRG93bmxvYWQsRmlQcmludCxGaVJlZnJlc2hDdyxGaVRhcmdldCE9IS4vbm9kZV9tb2R1bGVzL3JlYWN0LWljb25zL2ZpL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBd0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXE9wb3NJIHY3XFxub2RlX21vZHVsZXNcXHJlYWN0LWljb25zXFxmaVxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFxPcG9zSSB2N1xcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaWNvbnNcXFxcZmlcXFxcaW5kZXgubWpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=FiCalendar,FiDownload,FiPrint,FiRefreshCw,FiTarget!=!./node_modules/react-icons/fi/index.mjs\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/react-icons","vendor-chunks/react-hot-toast","vendor-chunks/@heroicons","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/micromark-util-symbol","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-from-markdown","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fplan-estudios%2Fpage&page=%2Fplan-estudios%2Fpage&appPaths=%2Fplan-estudios%2Fpage&pagePath=private-next-app-dir%2Fplan-estudios%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();