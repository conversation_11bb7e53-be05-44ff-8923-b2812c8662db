"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCalendar,FiCheck,FiCheckSquare,FiChevronRight,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiRefreshCw,FiSettings,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticoSupabase__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../features/shared/components/DiagnosticoSupabase */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticoSupabase.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TabButton = (param)=>{\n    let { active, onClick, icon, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 5\n            }, undefined),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 34,\n        columnNumber: 3\n    }, undefined);\n};\n_c = TabButton;\nfunction Home() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        // Recargar la lista de documentos automáticamente\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        // Ocultar el mensaje después de 5 segundos\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        // Recargar la lista de documentos cuando se elimina uno\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const tabs = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiRefreshCw, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 50\n            }, this),\n            color: 'bg-gradient-to-r from-blue-600 to-purple-600'\n        },\n        {\n            id: 'temario',\n            label: 'Mi Temario',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 49\n            }, this),\n            color: 'bg-green-600'\n        },\n        {\n            id: 'planEstudios',\n            label: 'Mi Plan de Estudios',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiCalendar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 63\n            }, this),\n            color: 'bg-teal-600'\n        },\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiMessageSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiLayers, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiFileText, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiList, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiCheckSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        },\n        {\n            id: 'gestionar',\n            label: 'Gestionar Documentos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiSettings, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 61\n            }, this),\n            color: 'bg-gray-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xfa de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: ()=>setActiveTab(tab.id),\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    ref: documentSelectorRef,\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onNavigateToTab: setActiveTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiCalendar, {\n                                                            className: \"w-10 h-10 text-teal-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"Mi Plan de Estudios\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                        children: \"Accede a tu plan de estudios personalizado generado con IA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>router.push('/plan-estudios'),\n                                                        className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiCalendar, {\n                                                                className: \"w-5 h-5 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Ver Mi Plan de Estudios\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'diagnostico' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticoSupabase__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"yK1sn0d53DZX6s50SAW2Cl3m8ao=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});