/**
 * @import {Options, State} from 'mdast-util-to-markdown'
 */
/**
 * @param {State} state
 * @returns {Exclude<Options['listItemIndent'], null | undefined>}
 */
export function checkListItemIndent(state: State): Exclude<Options["listItemIndent"], null | undefined>;
import type { State } from 'mdast-util-to-markdown';
import type { Options } from 'mdast-util-to-markdown';
//# sourceMappingURL=check-list-item-indent.d.ts.map