import { model } from '@/lib/gemini/geminiClient';
import { obtenerPlanificacionUsuario, obtenerEstimacionesTemas } from './planificacionService';
import { obtenerTemas } from '@/features/temario/services/temarioService';
import { PROMPT_PLAN_ESTUDIOS } from '@/config/prompts';

interface DatosPlanificacion {
  tiempo_diario_promedio?: number;
  tiempo_por_dia?: Record<string, number>;
  fecha_examen?: string;
  fecha_examen_aproximada?: string;
  familiaridad_general?: number;
  preferencias_horario?: string[];
  frecuencia_repasos?: string;
}

interface DatosTema {
  id: string;
  numero: number;
  titulo: string;
  descripcion?: string;
  horasEstimadas?: number;
  esDificil: boolean;
  esMuyImportante: boolean;
  yaDominado: boolean;
  notas?: string;
}

/**
 * Genera un plan de estudios personalizado usando IA
 */
export async function generarPlanEstudios(temarioId: string): Promise<string> {
  try {
    // Obtener datos de planificación del usuario
    const planificacion = await obtenerPlanificacionUsuario(temarioId);
    if (!planificacion) {
      throw new Error('No se encontró planificación configurada para este temario');
    }

    // Obtener estimaciones de temas
    const estimaciones = await obtenerEstimacionesTemas(planificacion.id);

    // Obtener temas del temario
    const temas = await obtenerTemas(temarioId);

    // Combinar datos de temas con estimaciones
    const temasConEstimaciones: DatosTema[] = temas.map(tema => {
      const estimacion = estimaciones.find(est => est.tema_id === tema.id);
      return {
        id: tema.id,
        numero: tema.numero,
        titulo: tema.titulo,
        descripcion: tema.descripcion,
        horasEstimadas: estimacion?.horas_estimadas || 0,
        esDificil: estimacion?.es_dificil || false,
        esMuyImportante: estimacion?.es_muy_importante || false,
        yaDominado: estimacion?.ya_dominado || false,
        notas: estimacion?.notas || ''
      };
    });

    // Preparar información del usuario para el prompt
    const informacionUsuario = prepararInformacionUsuario(planificacion, temasConEstimaciones);

    // Construir el prompt final
    const promptFinal = PROMPT_PLAN_ESTUDIOS.replace('{informacionUsuario}', informacionUsuario);

    // Generar el plan con Gemini
    const result = await model.generateContent(promptFinal);
    const response = result.response.text();

    if (!response || response.trim().length === 0) {
      throw new Error('La IA no generó ningún contenido para el plan de estudios');
    }

    return response;
  } catch (error) {
    console.error('Error al generar plan de estudios:', error);
    throw error;
  }
}

/**
 * Prepara la información del usuario en formato legible para la IA
 */
function prepararInformacionUsuario(
  planificacion: DatosPlanificacion,
  temas: DatosTema[]
): string {
  let info = '';

  // Disponibilidad de tiempo
  if (planificacion.tiempo_por_dia && Object.keys(planificacion.tiempo_por_dia).length > 0) {
    info += '**Disponibilidad de Tiempo Diario:**\n';
    const dias = ['lunes', 'martes', 'miercoles', 'jueves', 'viernes', 'sabado', 'domingo'];
    dias.forEach(dia => {
      const horas = planificacion.tiempo_por_dia![dia];
      if (horas) {
        info += `- ${dia.charAt(0).toUpperCase() + dia.slice(1)}: ${horas}h\n`;
      }
    });
  } else if (planificacion.tiempo_diario_promedio) {
    info += `**Disponibilidad de Tiempo Diario:** Promedio ${planificacion.tiempo_diario_promedio}h/día\n`;
  }

  // Fecha del examen
  if (planificacion.fecha_examen) {
    info += `\n**Fecha del Examen:** ${planificacion.fecha_examen}\n`;
  } else if (planificacion.fecha_examen_aproximada) {
    info += `\n**Fecha del Examen (aproximada):** ${planificacion.fecha_examen_aproximada}\n`;
  }

  // Familiaridad general
  if (planificacion.familiaridad_general) {
    info += `\n**Familiaridad General con el Temario (1-5):** ${planificacion.familiaridad_general}\n`;
  }

  // Preferencias de horario
  if (planificacion.preferencias_horario && planificacion.preferencias_horario.length > 0) {
    info += `\n**Preferencias de Horario:** ${planificacion.preferencias_horario.join(', ')}\n`;
  }

  // Frecuencia de repasos
  if (planificacion.frecuencia_repasos) {
    info += `\n**Frecuencia de Repasos Deseada:** ${planificacion.frecuencia_repasos}\n`;
  }

  // Índice del temario
  info += '\n**Índice del Temario del Opositor:**\n';
  temas.forEach(tema => {
    const caracteristicas = [];
    if (tema.esDificil) caracteristicas.push('dificil');
    if (tema.esMuyImportante) caracteristicas.push('muy_importante');
    if (tema.yaDominado) caracteristicas.push('ya_dominado');

    info += `- **Tema ${tema.numero}: ${tema.titulo}**\n`;
    if (tema.descripcion) {
      info += `  - Descripción: ${tema.descripcion}\n`;
    }
    if (tema.horasEstimadas && tema.horasEstimadas > 0) {
      info += `  - Estimación de horas: ${tema.horasEstimadas}h\n`;
    }
    if (caracteristicas.length > 0) {
      info += `  - Características: ${caracteristicas.join(', ')}\n`;
    }
    if (tema.notas) {
      info += `  - Notas: ${tema.notas}\n`;
    }
    info += '\n';
  });

  return info;
}
