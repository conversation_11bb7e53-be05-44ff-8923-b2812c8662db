import { model } from '@/lib/gemini/geminiClient';
import { obtenerPlanificacionUsuario, obtenerEstimacionesTemas } from './planificacionService';
import { obtenerTemas } from '@/features/temario/services/temarioService';
import { PROMPT_PLAN_ESTUDIOS } from '@/config/prompts';

**Información Clave Recopilada del Opositor:**

{informacionUsuario}

**Principios Fundamentales para la Creación de tu Propuesta de Plan de Estudio:**

Debes internalizar y aplicar los siguientes principios como si fueras un preparador humano experto:

1. **Organización y Realismo Absoluto:**
   - Calcula el tiempo total disponible hasta el examen, considerando la disponibilidad diaria REAL del opositor.
   - Compara este tiempo con la suma de las estimaciones de horas por tema (ajustadas por dificultad/importancia).
   - **Si el tiempo es claramente insuficiente para cubrir todo el temario de forma realista, indícalo con honestidad al inicio de tu propuesta de plan**, sugiriendo posibles enfoques (priorizar, extender el plazo si es posible, etc.). No crees un plan imposible.
   - Distribuye el temario de forma lógica a lo largo del tiempo, dejando márgenes para imprevistos.

2. **Metodología Probada y Adaptable (Tu Enfoque):**
   - **Estimación de Tiempo por Tema:** Si el usuario no proporcionó una estimación para un tema, usa tu "experiencia" para asignar una base (ej. temas cortos 2-3h, medios 5-7h, largos/densos 8-12h). Ajusta esto según la "familiaridad general" y las "característicasUsuario" (dificultad, importancia). Los temas difíciles o muy importantes deben tener más tiempo asignado.
   - **Orden de Estudio:** Generalmente, sugiere empezar por los temas marcados como "difíciles" o "muy importantes". Puedes proponer alternar temas densos con otros más ligeros para mantener la motivación.
   - **Bloques Temáticos:** Si identificas temas muy relacionados entre sí en el índice, considera agruparlos en bloques de estudio.
   - **Repasos Sistemáticos:**
     - **Post-Tema:** Incluye un breve repaso al finalizar cada tema.
     - **Periódicos:** Integra repasos acumulativos (ej. semanales o quincenales, según la frecuenciaRepasoDeseada si el usuario la especificó, o tu recomendación experta). Una buena regla general es dedicar ~30% del tiempo total de estudio a repasos.
     - **Fase Final de Repaso:** Reserva un periodo significativo antes del examen (ej. las últimas 3-6 semanas, dependiendo del tiempo total) EXCLUSIVAMENTE para repasos generales, simulacros y consolidación. No se debe introducir material nuevo aquí.
   - **Metas Claras:** Define metas semanales y/o mensuales (ej. "Semana 1: Completar Tema 1 y Tema 2 (hasta apartado X). Realizar 20 flashcards de Tema 1.").

3. **Experiencia y Conocimiento (Tu Rol):**
   - Al presentar el plan, puedes añadir breves comentarios estratégicos, como: "Dado que el Tema X es fundamental y lo has marcado como difícil, le hemos asignado más tiempo y lo abordaremos pronto para tener margen de repaso".
   - Si el usuario no marcó temas como importantes, usa tu "experiencia" para identificar temas que suelen ser cruciales en oposiciones similares (si el contexto del temario te da alguna pista, si no, sé general).

4. **Flexibilidad (Implícita en la Propuesta):**
   - Aunque generes un plan estructurado, en tu introducción al plan puedes mencionar que es una "propuesta inicial" y que se podrá ajustar según el progreso real.

**Formato de Salida de la Propuesta del Plan:**

Genera una respuesta en **texto claro y estructurado (Markdown es ideal)** que el opositor pueda entender fácilmente. Considera las siguientes secciones:

1. **Introducción y Evaluación Inicial de Viabilidad:**
   - Un saludo y una breve valoración de la situación.
   - Si el plan es muy ajustado o potencialmente irrealizable, **menciónalo aquí con tacto y sugiere alternativas**.

2. **Resumen del Plan:**
   - Tiempo total de estudio estimado.
   - Número de temas a cubrir.
   - Duración de la fase de estudio de nuevo material.
   - Duración de la fase de repaso final.

3. **Cronograma Semanal Detallado:**
   - Para cada semana:
     - **Semana X (Fechas: DD/MM/YY - DD/MM/YY)**
     - **Objetivo Principal de la Semana:** 
     - **Distribución Sugerida por Días:**
       - **Lunes ([X]h):** Actividades específicas.
       - **Martes ([X]h):** Actividades específicas.
       - etc.

4. **Estrategia de Repasos:**
   - Explica brevemente cómo se integrarán los repasos.

5. **Próximos Pasos y Consejos:**
   - Consejos motivacionales y de seguimiento.

**Consideraciones para la IA al Generar la Respuesta:**

- **Lenguaje:** Empático, profesional, claro y motivador.
- **Personalización:** Usa la información del usuario para que el plan se sienta realmente adaptado a él/ella.
- **Realismo:** Evita sobrecargar las semanas. Es mejor un progreso constante que picos de trabajo insostenibles.
- **Accionable:** El plan debe darle al usuario una idea clara de qué hacer cada semana/día.

Genera el plan de estudios personalizado basándote en toda esta información.`;

interface DatosPlanificacion {
  tiempo_diario_promedio?: number;
  tiempo_por_dia?: Record<string, number>;
  fecha_examen?: string;
  fecha_examen_aproximada?: string;
  familiaridad_general?: number;
  preferencias_horario?: string[];
  frecuencia_repasos?: string;
}

interface DatosTema {
  id: string;
  numero: number;
  titulo: string;
  descripcion?: string;
  horasEstimadas?: number;
  esDificil: boolean;
  esMuyImportante: boolean;
  yaDominado: boolean;
  notas?: string;
}

/**
 * Genera un plan de estudios personalizado usando IA
 */
export async function generarPlanEstudios(temarioId: string): Promise<string> {
  try {
    // Obtener datos de planificación del usuario
    const planificacion = await obtenerPlanificacionUsuario(temarioId);
    if (!planificacion) {
      throw new Error('No se encontró planificación configurada para este temario');
    }

    // Obtener estimaciones de temas
    const estimaciones = await obtenerEstimacionesTemas(planificacion.id);
    
    // Obtener temas del temario
    const temas = await obtenerTemas(temarioId);

    // Combinar datos de temas con estimaciones
    const temasConEstimaciones: DatosTema[] = temas.map(tema => {
      const estimacion = estimaciones.find(est => est.tema_id === tema.id);
      return {
        id: tema.id,
        numero: tema.numero,
        titulo: tema.titulo,
        descripcion: tema.descripcion,
        horasEstimadas: estimacion?.horas_estimadas || 0,
        esDificil: estimacion?.es_dificil || false,
        esMuyImportante: estimacion?.es_muy_importante || false,
        yaDominado: estimacion?.ya_dominado || false,
        notas: estimacion?.notas || ''
      };
    });

    // Preparar información del usuario para el prompt
    const informacionUsuario = prepararInformacionUsuario(planificacion, temasConEstimaciones);

    // Construir el prompt final
    const promptFinal = PROMPT_PLAN_ESTUDIOS.replace('{informacionUsuario}', informacionUsuario);

    // Generar el plan con Gemini
    const result = await model.generateContent(promptFinal);
    const response = result.response.text();

    if (!response || response.trim().length === 0) {
      throw new Error('La IA no generó ningún contenido para el plan de estudios');
    }

    return response;
  } catch (error) {
    console.error('Error al generar plan de estudios:', error);
    throw error;
  }
}

/**
 * Prepara la información del usuario en formato legible para la IA
 */
function prepararInformacionUsuario(
  planificacion: DatosPlanificacion, 
  temas: DatosTema[]
): string {
  let info = '';

  // Disponibilidad de tiempo
  if (planificacion.tiempo_por_dia && Object.keys(planificacion.tiempo_por_dia).length > 0) {
    info += '**Disponibilidad de Tiempo Diario:**\n';
    const dias = ['lunes', 'martes', 'miercoles', 'jueves', 'viernes', 'sabado', 'domingo'];
    dias.forEach(dia => {
      const horas = planificacion.tiempo_por_dia![dia];
      if (horas) {
        info += `- ${dia.charAt(0).toUpperCase() + dia.slice(1)}: ${horas}h\n`;
      }
    });
  } else if (planificacion.tiempo_diario_promedio) {
    info += `**Disponibilidad de Tiempo Diario:** Promedio ${planificacion.tiempo_diario_promedio}h/día\n`;
  }

  // Fecha del examen
  if (planificacion.fecha_examen) {
    info += `\n**Fecha del Examen:** ${planificacion.fecha_examen}\n`;
  } else if (planificacion.fecha_examen_aproximada) {
    info += `\n**Fecha del Examen (aproximada):** ${planificacion.fecha_examen_aproximada}\n`;
  }

  // Familiaridad general
  if (planificacion.familiaridad_general) {
    info += `\n**Familiaridad General con el Temario (1-5):** ${planificacion.familiaridad_general}\n`;
  }

  // Preferencias de horario
  if (planificacion.preferencias_horario && planificacion.preferencias_horario.length > 0) {
    info += `\n**Preferencias de Horario:** ${planificacion.preferencias_horario.join(', ')}\n`;
  }

  // Frecuencia de repasos
  if (planificacion.frecuencia_repasos) {
    info += `\n**Frecuencia de Repasos Deseada:** ${planificacion.frecuencia_repasos}\n`;
  }

  // Índice del temario
  info += '\n**Índice del Temario del Opositor:**\n';
  temas.forEach(tema => {
    const caracteristicas = [];
    if (tema.esDificil) caracteristicas.push('dificil');
    if (tema.esMuyImportante) caracteristicas.push('muy_importante');
    if (tema.yaDominado) caracteristicas.push('ya_dominado');

    info += `- **Tema ${tema.numero}: ${tema.titulo}**\n`;
    if (tema.descripcion) {
      info += `  - Descripción: ${tema.descripcion}\n`;
    }
    if (tema.horasEstimadas && tema.horasEstimadas > 0) {
      info += `  - Estimación de horas: ${tema.horasEstimadas}h\n`;
    }
    if (caracteristicas.length > 0) {
      info += `  - Características: ${caracteristicas.join(', ')}\n`;
    }
    if (tema.notas) {
      info += `  - Notas: ${tema.notas}\n`;
    }
    info += '\n';
  });

  return info;
}
