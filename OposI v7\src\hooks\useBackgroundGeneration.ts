'use client';

import { useCallback } from 'react';
import { useBackgroundTasks, BackgroundTask } from '@/contexts/BackgroundTasksContext';

interface GenerationOptions {
  peticion: string;
  contextos: string[];
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

interface UseBackgroundGenerationReturn {
  generateMapaMental: (options: GenerationOptions) => Promise<string>;
  generateTest: (options: GenerationOptions) => Promise<string>;
  generateFlashcards: (options: GenerationOptions) => Promise<string>;
  isGenerating: (type: BackgroundTask['type']) => boolean;
  getActiveTask: (type: BackgroundTask['type']) => BackgroundTask | undefined;
}

export const useBackgroundGeneration = (): UseBackgroundGenerationReturn => {
  const { addTask, updateTask, getTasksByType } = useBackgroundTasks();

  const executeGeneration = useCallback(async (
    type: BackgroundTask['type'],
    action: string,
    options: GenerationOptions
  ): Promise<string> => {
    const { peticion, contextos, onComplete, onError } = options;

    // Crear la tarea en segundo plano
    const taskId = addTask({
      type,
      title: peticion.length > 50 ? `${peticion.substring(0, 50)}...` : peticion,
    });

    try {
      // Marcar como procesando
      updateTask(taskId, { status: 'processing' });

      // Realizar la petición
      const response = await fetch('/api/gemini', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          peticion,
          contextos
        })
      });

      if (!response.ok) {
        throw new Error(`Error en la API: ${response.status}`);
      }

      const { result } = await response.json();

      // Marcar como completado
      updateTask(taskId, {
        status: 'completed',
        result,
        progress: 100
      });

      // Ejecutar callback de éxito de forma asíncrona para evitar problemas de renderizado
      if (onComplete) {
        setTimeout(() => onComplete(result), 0);
      }

      return taskId;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      // Marcar como error
      updateTask(taskId, {
        status: 'error',
        error: errorMessage
      });

      // Ejecutar callback de error de forma asíncrona para evitar problemas de renderizado
      if (onError) {
        setTimeout(() => onError(errorMessage), 0);
      }

      throw error;
    }
  }, [addTask, updateTask]);

  const generateMapaMental = useCallback(async (options: GenerationOptions): Promise<string> => {
    return executeGeneration('mapa-mental', 'generarMapaMental', options);
  }, [executeGeneration]);

  const generateTest = useCallback(async (options: GenerationOptions): Promise<string> => {
    return executeGeneration('test', 'generarTest', options);
  }, [executeGeneration]);

  const generateFlashcards = useCallback(async (options: GenerationOptions): Promise<string> => {
    return executeGeneration('flashcards', 'generarFlashcards', options);
  }, [executeGeneration]);

  const isGenerating = useCallback((type: BackgroundTask['type']): boolean => {
    const tasks = getTasksByType(type);
    return tasks.some(task => task.status === 'pending' || task.status === 'processing');
  }, [getTasksByType]);

  const getActiveTask = useCallback((type: BackgroundTask['type']): BackgroundTask | undefined => {
    const tasks = getTasksByType(type);
    return tasks.find(task => task.status === 'pending' || task.status === 'processing');
  }, [getTasksByType]);

  return {
    generateMapaMental,
    generateTest,
    generateFlashcards,
    isGenerating,
    getActiveTask,
  };
};
