"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCalendar,FiCheck,FiCheckSquare,FiChevronRight,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiRefreshCw,FiSettings,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticoSupabase__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../features/shared/components/DiagnosticoSupabase */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticoSupabase.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TabButton = (param)=>{\n    let { active, onClick, icon, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 5\n            }, undefined),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 34,\n        columnNumber: 3\n    }, undefined);\n};\n_c = TabButton;\nfunction Home() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        // Recargar la lista de documentos automáticamente\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        // Ocultar el mensaje después de 5 segundos\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        // Recargar la lista de documentos cuando se elimina uno\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const tabs = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiRefreshCw, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 50\n            }, this),\n            color: 'bg-gradient-to-r from-blue-600 to-purple-600'\n        },\n        {\n            id: 'temario',\n            label: 'Mi Temario',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 49\n            }, this),\n            color: 'bg-green-600'\n        },\n        {\n            id: 'planEstudios',\n            label: 'Mi Plan de Estudios',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiCalendar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 63\n            }, this),\n            color: 'bg-teal-600'\n        },\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiMessageSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiLayers, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiFileText, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiList, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiCheckSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        },\n        {\n            id: 'gestionar',\n            label: 'Gestionar Documentos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiSettings, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 61\n            }, this),\n            color: 'bg-gray-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCalendar_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_18__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xfa de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: ()=>setActiveTab(tab.id),\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    ref: documentSelectorRef,\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onNavigateToTab: setActiveTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'diagnostico' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticoSupabase__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"yK1sn0d53DZX6s50SAW2Cl3m8ao=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_17__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});