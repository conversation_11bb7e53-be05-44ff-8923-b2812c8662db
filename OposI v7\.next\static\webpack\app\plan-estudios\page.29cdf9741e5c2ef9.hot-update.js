"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/plan-estudios/page",{

/***/ "(app-pages-browser)/./src/app/plan-estudios/page.tsx":
/*!****************************************!*\
  !*** ./src/app/plan-estudios/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiDownload,FiPrinter,FiRefreshCw,FiTarget!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst PlanEstudiosPage = ()=>{\n    _s();\n    const [temario, setTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [planGenerado, setPlanGenerado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlanEstudiosPage.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"PlanEstudiosPage.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            // Obtener temario del usuario\n            const temarioData = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemarioUsuario)();\n            if (!temarioData) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('No se encontró un temario configurado');\n                return;\n            }\n            setTemario(temarioData);\n            // Verificar si tiene planificación configurada\n            const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__.tienePlanificacionConfigurada)(temarioData.id);\n            setTienePlanificacion(tienePlan);\n            if (!tienePlan) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Necesitas configurar tu planificación antes de generar el plan de estudios');\n                return;\n            }\n        } catch (error) {\n            console.error('Error al cargar datos:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al cargar los datos');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGenerarPlan = async ()=>{\n        if (!temario) return;\n        // Verificar nuevamente que tiene planificación antes de generar\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Necesitas configurar tu planificación antes de generar el plan de estudios');\n            return;\n        }\n        setIsGenerating(true);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.loading('Generando tu plan de estudios personalizado con IA...');\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'generarPlanEstudios',\n                    temarioId: temario.id\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                const errorMessage = errorData.error || \"Error en la API: \".concat(response.status);\n                throw new Error(errorMessage);\n            }\n            const { result } = await response.json();\n            setPlanGenerado(result);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('¡Plan de estudios generado exitosamente!', {\n                id: loadingToastId\n            });\n        } catch (error) {\n            console.error('Error al generar plan:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Error desconocido';\n            if (errorMessage.includes('planificación configurada')) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"', {\n                    id: loadingToastId\n                });\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.', {\n                    id: loadingToastId\n                });\n            }\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const handleRegenerarPlan = ()=>{\n        setPlanGenerado('');\n        handleGenerarPlan();\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planGenerado) return;\n        const blob = new Blob([\n            planGenerado\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat((temario === null || temario === void 0 ? void 0 : temario.titulo) || 'temario', \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('Plan descargado exitosamente');\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planGenerado) return;\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write(\"\\n        <html>\\n          <head>\\n            <title>Plan de Estudios - \".concat(temario === null || temario === void 0 ? void 0 : temario.titulo, '</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              // Convertir markdown a HTML b\\xe1sico para impresi\\xf3n\\n              const markdown = ').concat(JSON.stringify(planGenerado), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando datos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!temario || !tienePlanificacion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {\n                            className: \"w-8 h-8 text-yellow-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Configuraci\\xf3n Requerida\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Para generar tu plan de estudios personalizado, necesitas:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-left text-sm text-gray-600 mb-6 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-blue-400 rounded-full mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Tener un temario configurado\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-blue-400 rounded-full mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Completar la planificaci\\xf3n inteligente\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/temario\",\n                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Ir a Mi Temario\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                        children: \"Mi Plan de Estudios\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"Plan personalizado generado con IA para: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: temario.titulo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 58\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: planGenerado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleRegenerarPlan,\n                                            disabled: isGenerating,\n                                            className: \"flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiRefreshCw, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Regenerar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDescargarPlan,\n                                            className: \"flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiDownload, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Descargar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleImprimirPlan,\n                                            className: \"flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPrinter, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Imprimir\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, undefined),\n                !planGenerado ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCalendar, {\n                                className: \"w-10 h-10 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                            children: \"Genera tu Plan de Estudios Personalizado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                            children: \"Nuestro asistente de IA analizar\\xe1 tu planificaci\\xf3n, disponibilidad de tiempo, y las caracter\\xedsticas de cada tema para crear un plan de estudios completamente personalizado y realista.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGenerarPlan,\n                            disabled: isGenerating,\n                            className: \"flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 mx-auto\",\n                            children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Generando plan con IA...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {\n                                        className: \"w-5 h-5 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Generar Plan de Estudios\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-blue max-w-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactMarkdown, {\n                            children: planGenerado\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlanEstudiosPage, \"UDrgtCB4qhT7Piap7qDZu9w8vO0=\");\n_c = PlanEstudiosPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlanEstudiosPage);\nvar _c;\n$RefreshReg$(_c, \"PlanEstudiosPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/plan-estudios/page.tsx\n"));

/***/ })

});