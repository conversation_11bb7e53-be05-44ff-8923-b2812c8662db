"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/planificacion/services/planEstudiosService.ts":
/*!********************************************************************!*\
  !*** ./src/features/planificacion/services/planEstudiosService.ts ***!
  \********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarPlan: () => (/* binding */ activarPlan),\n/* harmony export */   actualizarNotasPlan: () => (/* binding */ actualizarNotasPlan),\n/* harmony export */   eliminarPlan: () => (/* binding */ eliminarPlan),\n/* harmony export */   guardarPlanEstudios: () => (/* binding */ guardarPlanEstudios),\n/* harmony export */   guardarProgresoTarea: () => (/* binding */ guardarProgresoTarea),\n/* harmony export */   obtenerEstadisticasProgreso: () => (/* binding */ obtenerEstadisticasProgreso),\n/* harmony export */   obtenerHistorialPlanes: () => (/* binding */ obtenerHistorialPlanes),\n/* harmony export */   obtenerPlanEstudiosActivo: () => (/* binding */ obtenerPlanEstudiosActivo),\n/* harmony export */   obtenerProgresoPlan: () => (/* binding */ obtenerProgresoPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(app-pages-browser)/./src/features/auth/services/authService.ts\");\n\n\n/**\n * Guarda un plan de estudios generado en la base de datos\n */ async function guardarPlanEstudios(temarioId, planData, titulo) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').insert([\n            {\n                user_id: user.id,\n                temario_id: temarioId,\n                titulo: titulo || 'Plan de Estudios',\n                plan_data: planData,\n                activo: true,\n                version: 1\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al guardar plan de estudios:', error);\n            return null;\n        }\n        console.log('✅ Plan de estudios guardado exitosamente:', data.id);\n        return data.id;\n    } catch (error) {\n        console.error('Error al guardar plan de estudios:', error);\n        return null;\n    }\n}\n/**\n * Obtiene el plan de estudios activo para un temario\n */ async function obtenerPlanEstudiosActivo(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return null;\n        }\n        const { data, error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('*').eq('user_id', user.id).eq('temario_id', temarioId).eq('activo', true).single();\n        if (error) {\n            if (error.code === 'PGRST116') {\n                return null; // No hay plan activo\n            }\n            console.error('Error al obtener plan de estudios:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener plan de estudios:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todos los planes de estudios de un temario (historial)\n */ async function obtenerHistorialPlanes(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('*').eq('user_id', user.id).eq('temario_id', temarioId).order('fecha_generacion', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener historial de planes:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener historial de planes:', error);\n        return [];\n    }\n}\n/**\n * Actualiza las notas de un plan de estudios\n */ async function actualizarNotasPlan(planId, notas) {\n    try {\n        const { error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').update({\n            notas,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) {\n            console.error('Error al actualizar notas del plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar notas del plan:', error);\n        return false;\n    }\n}\n/**\n * Marca un plan como activo y desactiva los demás\n */ async function activarPlan(planId) {\n    try {\n        const { error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').update({\n            activo: true,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) {\n            console.error('Error al activar plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al activar plan:', error);\n        return false;\n    }\n}\n/**\n * Elimina un plan de estudios\n */ async function eliminarPlan(planId) {\n    try {\n        const { error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').delete().eq('id', planId);\n        if (error) {\n            console.error('Error al eliminar plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar plan:', error);\n        return false;\n    }\n}\n/**\n * Guarda el progreso de una tarea del plan\n */ async function guardarProgresoTarea(planId, semanaNúmero, diaNombre, tareaTitulo, tareaTipo, completado, tiempoRealMinutos, notasProgreso, calificacion) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return false;\n        }\n        // Verificar si ya existe un registro de progreso para esta tarea\n        const { data: existente } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('id').eq('plan_id', planId).eq('user_id', user.id).eq('semana_numero', semanaNúmero).eq('dia_nombre', diaNombre).eq('tarea_titulo', tareaTitulo).single();\n        if (existente) {\n            // Actualizar registro existente\n            const { error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').update({\n                completado,\n                fecha_completado: completado ? new Date().toISOString() : null,\n                tiempo_real_minutos: tiempoRealMinutos,\n                notas_progreso: notasProgreso,\n                calificacion,\n                actualizado_en: new Date().toISOString()\n            }).eq('id', existente.id);\n            if (error) {\n                console.error('Error al actualizar progreso:', error);\n                return false;\n            }\n        } else {\n            // Crear nuevo registro\n            const { error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').insert([\n                {\n                    plan_id: planId,\n                    user_id: user.id,\n                    semana_numero: semanaNúmero,\n                    dia_nombre: diaNombre,\n                    tarea_titulo: tareaTitulo,\n                    tarea_tipo: tareaTipo,\n                    completado,\n                    fecha_completado: completado ? new Date().toISOString() : null,\n                    tiempo_real_minutos: tiempoRealMinutos,\n                    notas_progreso: notasProgreso,\n                    calificacion\n                }\n            ]);\n            if (error) {\n                console.error('Error al crear progreso:', error);\n                return false;\n            }\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al guardar progreso de tarea:', error);\n        return false;\n    }\n}\n/**\n * Obtiene el progreso de un plan de estudios\n */ async function obtenerProgresoPlan(planId) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('*').eq('plan_id', planId).eq('user_id', user.id).order('semana_numero', {\n            ascending: true\n        }).order('creado_en', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener progreso del plan:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener progreso del plan:', error);\n        return [];\n    }\n}\n/**\n * Obtiene estadísticas del progreso del plan\n */ async function obtenerEstadisticasProgreso(planId) {\n    try {\n        const progreso = await obtenerProgresoPlan(planId);\n        const plan = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('plan_data').eq('id', planId).single();\n        if (!plan.data) {\n            return {\n                totalTareas: 0,\n                tareasCompletadas: 0,\n                porcentajeCompletado: 0,\n                tiempoTotalEstimado: 0,\n                tiempoTotalReal: 0,\n                semanasCompletadas: 0,\n                totalSemanas: 0\n            };\n        }\n        const planData = plan.data.plan_data;\n        const totalSemanas = planData.semanas.length;\n        // Calcular total de tareas\n        let totalTareas = 0;\n        planData.semanas.forEach((semana)=>{\n            semana.dias.forEach((dia)=>{\n                totalTareas += dia.tareas.length;\n            });\n        });\n        const tareasCompletadas = progreso.filter((p)=>p.completado).length;\n        const porcentajeCompletado = totalTareas > 0 ? tareasCompletadas / totalTareas * 100 : 0;\n        const tiempoTotalReal = progreso.filter((p)=>p.tiempo_real_minutos).reduce((total, p)=>total + (p.tiempo_real_minutos || 0), 0);\n        // Calcular semanas completadas (todas las tareas de la semana completadas)\n        let semanasCompletadas = 0;\n        planData.semanas.forEach((semana)=>{\n            const tareasSemanaTotales = semana.dias.reduce((total, dia)=>total + dia.tareas.length, 0);\n            const tareasSemanCompletadas = progreso.filter((p)=>p.semana_numero === semana.numero && p.completado).length;\n            if (tareasSemanaTotales > 0 && tareasSemanCompletadas === tareasSemanaTotales) {\n                semanasCompletadas++;\n            }\n        });\n        return {\n            totalTareas,\n            tareasCompletadas,\n            porcentajeCompletado: Math.round(porcentajeCompletado * 100) / 100,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal,\n            semanasCompletadas,\n            totalSemanas\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas de progreso:', error);\n        return {\n            totalTareas: 0,\n            tareasCompletadas: 0,\n            porcentajeCompletado: 0,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal: 0,\n            semanasCompletadas: 0,\n            totalSemanas: 0\n        };\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/planificacion/services/planEstudiosService.ts\n"));

/***/ })

});