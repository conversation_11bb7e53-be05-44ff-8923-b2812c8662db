import React, { useEffect, useState } from 'react';
import { FiTrash2, FiEdit3, <PERSON><PERSON><PERSON><PERSON>, FiX } from 'react-icons/fi';
import { Conversacion, obtenerConversaciones, eliminarConversacion, actualizarConversacion } from '../../../lib/supabase';
import { toast } from 'react-hot-toast';

interface ConversationSidebarProps {
  onSelectConversation: (conversacionId: string) => void;
  conversacionActualId: string | null;
  onConversationDeleted?: () => void;
}

export default function ConversationSidebar({
  onSelectConversation,
  conversacionActualId,
  onConversationDeleted
}: ConversationSidebarProps) {
  const [conversaciones, setConversaciones] = useState<Conversacion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  // Función para cargar las conversaciones
  const cargarConversaciones = async () => {
    setIsLoading(true);
    try {
      const data = await obtenerConversaciones();
      setConversaciones(data);
      setError('');
    } catch (error) {
      console.error('Error al cargar conversaciones:', error);
      setError('No se pudieron cargar las conversaciones');
    } finally {
      setIsLoading(false);
    }
  };

  // Cargar conversaciones al montar el componente
  useEffect(() => {
    cargarConversaciones();
  }, []);

  // Recargar conversaciones cuando cambia la conversación actual
  useEffect(() => {
    if (conversacionActualId) {
      cargarConversaciones();
    }
  }, [conversacionActualId]);

  // Formatear la fecha para mostrarla en la lista
  const formatearFecha = (fechaStr: string): string => {
    const fecha = new Date(fechaStr);
    const ahora = new Date();
    const diferencia = ahora.getTime() - fecha.getTime();
    const dias = Math.floor(diferencia / (1000 * 60 * 60 * 24));

    if (dias === 0) {
      return fecha.toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (dias === 1) {
      return 'Ayer';
    } else if (dias < 7) {
      return `Hace ${dias} días`;
    } else {
      return fecha.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      });
    }
  };

  // Generar un título automático si no existe
  const obtenerTitulo = (conversacion: Conversacion): string => {
    if (conversacion.titulo) {
      return conversacion.titulo;
    }
    return `Conversación del ${formatearFecha(conversacion.creado_en)}`;
  };

  // Manejar eliminación de conversación
  const handleEliminarConversacion = async (conversacionId: string) => {
    if (deletingId) return;

    setDeletingId(conversacionId);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Eliminando conversación...');

      const success = await eliminarConversacion(conversacionId);

      if (success) {
        toast.success('Conversación eliminada exitosamente', { id: loadingToastId });

        // Actualizar la lista local
        setConversaciones(prev => prev.filter(conv => conv.id !== conversacionId));

        // Si se eliminó la conversación actual, notificar al padre
        if (conversacionId === conversacionActualId) {
          onConversationDeleted?.();
        }
      } else {
        toast.error('Error al eliminar la conversación', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al eliminar conversación:', error);
      toast.error('Error al eliminar la conversación', { id: loadingToastId });
    } finally {
      setDeletingId(null);
      setShowDeleteConfirm(null);
    }
  };

  // Iniciar edición de título
  const iniciarEdicion = (conversacion: Conversacion) => {
    setEditingId(conversacion.id);
    setEditingTitle(obtenerTitulo(conversacion));
  };

  // Cancelar edición
  const cancelarEdicion = () => {
    setEditingId(null);
    setEditingTitle('');
  };

  // Guardar título editado
  const guardarTitulo = async (conversacionId: string) => {
    if (!editingTitle.trim()) {
      toast.error('El título no puede estar vacío');
      return;
    }

    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Actualizando título...');

      const success = await actualizarConversacion(conversacionId, editingTitle.trim());

      if (success) {
        toast.success('Título actualizado exitosamente', { id: loadingToastId });

        // Actualizar la lista local
        setConversaciones(prev =>
          prev.map(conv =>
            conv.id === conversacionId
              ? { ...conv, titulo: editingTitle.trim() }
              : conv
          )
        );

        setEditingId(null);
        setEditingTitle('');
      } else {
        toast.error('Error al actualizar el título', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al actualizar título:', error);
      toast.error('Error al actualizar el título', { id: loadingToastId });
    }
  };

  return (
    <div className="w-64 bg-gray-50 border border-gray-200 rounded-lg flex flex-col h-full">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Conversaciones</h3>
        <p className="text-sm text-gray-500 mt-1">
          {conversaciones.length} conversación{conversaciones.length !== 1 ? 'es' : ''}
        </p>
      </div>

      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p className="text-sm text-gray-500 mt-2">Cargando conversaciones...</p>
          </div>
        ) : error ? (
          <div className="p-4">
            <div className="text-red-500 text-sm">{error}</div>
          </div>
        ) : conversaciones.length === 0 ? (
          <div className="p-4">
            <div className="text-gray-500 text-sm text-center">
              No hay conversaciones guardadas
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {conversaciones.map((conversacion) => (
              <div
                key={conversacion.id}
                className={`p-4 hover:bg-gray-100 transition-colors ${
                  conversacionActualId === conversacion.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                }`}
              >
                {/* Título y fecha */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    {editingId === conversacion.id ? (
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={editingTitle}
                          onChange={(e) => setEditingTitle(e.target.value)}
                          className="flex-1 text-sm font-medium bg-white border border-gray-300 rounded px-2 py-1"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              guardarTitulo(conversacion.id);
                            } else if (e.key === 'Escape') {
                              cancelarEdicion();
                            }
                          }}
                          autoFocus
                        />
                        <button
                          onClick={() => guardarTitulo(conversacion.id)}
                          className="text-green-600 hover:text-green-800"
                        >
                          <FiCheck size={16} />
                        </button>
                        <button
                          onClick={cancelarEdicion}
                          className="text-gray-600 hover:text-gray-800"
                        >
                          <FiX size={16} />
                        </button>
                      </div>
                    ) : (
                      <h4
                        className="font-medium text-gray-800 truncate cursor-pointer hover:text-blue-600"
                        onClick={() => onSelectConversation(conversacion.id)}
                        title={obtenerTitulo(conversacion)}
                      >
                        {obtenerTitulo(conversacion)}
                      </h4>
                    )}
                  </div>
                </div>

                {/* Fecha y acciones */}
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    {formatearFecha(conversacion.actualizado_en)}
                  </div>

                  {editingId !== conversacion.id && (
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => iniciarEdicion(conversacion)}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title="Renombrar conversación"
                      >
                        <FiEdit3 size={14} />
                      </button>

                      {showDeleteConfirm === conversacion.id ? (
                        <div className="flex items-center space-x-1">
                          <button
                            onClick={() => handleEliminarConversacion(conversacion.id)}
                            disabled={deletingId === conversacion.id}
                            className="p-1 text-red-600 hover:text-red-800 transition-colors"
                            title="Confirmar eliminación"
                          >
                            <FiCheck size={14} />
                          </button>
                          <button
                            onClick={() => setShowDeleteConfirm(null)}
                            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                            title="Cancelar"
                          >
                            <FiX size={14} />
                          </button>
                        </div>
                      ) : (
                        <button
                          onClick={() => setShowDeleteConfirm(conversacion.id)}
                          disabled={deletingId === conversacion.id}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="Eliminar conversación"
                        >
                          <FiTrash2 size={14} />
                        </button>
                      )}
                    </div>
                  )}
                </div>

                {/* Indicador de conversación actual */}
                {conversacionActualId === conversacion.id && (
                  <div className="text-xs text-blue-600 mt-1 font-medium">
                    Conversación actual
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
