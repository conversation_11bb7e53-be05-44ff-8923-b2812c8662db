"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/plan-estudios/page",{

/***/ "(app-pages-browser)/./src/app/plan-estudios/page.tsx":
/*!****************************************!*\
  !*** ./src/app/plan-estudios/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiDownload,FiPrinter,FiRefreshCw,FiTarget!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst PlanEstudiosPage = ()=>{\n    _s();\n    const [temario, setTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [planGenerado, setPlanGenerado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlanEstudiosPage.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"PlanEstudiosPage.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            // Obtener temario del usuario\n            const temarioData = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_2__.obtenerTemarioUsuario)();\n            if (!temarioData) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('No se encontró un temario configurado');\n                return;\n            }\n            setTemario(temarioData);\n            // Verificar si tiene planificación configurada\n            const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_3__.tienePlanificacionConfigurada)(temarioData.id);\n            setTienePlanificacion(tienePlan);\n            if (!tienePlan) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Necesitas configurar tu planificación antes de generar el plan de estudios');\n                return;\n            }\n        } catch (error) {\n            console.error('Error al cargar datos:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al cargar los datos');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGenerarPlan = async ()=>{\n        if (!temario) return;\n        // Verificar nuevamente que tiene planificación antes de generar\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Necesitas configurar tu planificación antes de generar el plan de estudios');\n            return;\n        }\n        setIsGenerating(true);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.loading('Generando tu plan de estudios personalizado con IA...');\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'generarPlanEstudios',\n                    temarioId: temario.id\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                const errorMessage = errorData.error || \"Error en la API: \".concat(response.status);\n                throw new Error(errorMessage);\n            }\n            const { result } = await response.json();\n            setPlanGenerado(result);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('¡Plan de estudios generado exitosamente!', {\n                id: loadingToastId\n            });\n        } catch (error) {\n            console.error('Error al generar plan:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Error desconocido';\n            if (errorMessage.includes('planificación configurada')) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"', {\n                    id: loadingToastId\n                });\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.', {\n                    id: loadingToastId\n                });\n            }\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const handleRegenerarPlan = ()=>{\n        setPlanGenerado(null);\n        handleGenerarPlan();\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planGenerado) return;\n        // Convertir el plan estructurado a texto\n        const planTexto = convertirPlanATexto(planGenerado);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat((temario === null || temario === void 0 ? void 0 : temario.titulo) || 'temario', \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios - \".concat(temario === null || temario === void 0 ? void 0 : temario.titulo, \"\\n\\n\");\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planGenerado) return;\n        const planTexto = convertirPlanATexto(planGenerado);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write(\"\\n        <html>\\n          <head>\\n            <title>Plan de Estudios - \".concat(temario === null || temario === void 0 ? void 0 : temario.titulo, '</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              // Convertir markdown a HTML b\\xe1sico para impresi\\xf3n\\n              const markdown = ').concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando datos...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n            lineNumber: 201,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!temario || !tienePlanificacion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {\n                            className: \"w-8 h-8 text-yellow-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: \"Configuraci\\xf3n Requerida\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Para generar tu plan de estudios personalizado, necesitas:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-left text-sm text-gray-600 mb-6 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-blue-400 rounded-full mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Tener un temario configurado\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-blue-400 rounded-full mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Completar la planificaci\\xf3n inteligente\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/temario\",\n                        className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Ir a Mi Temario\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n            lineNumber: 212,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                        children: \"Mi Plan de Estudios\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"Plan personalizado generado con IA para: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: temario.titulo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 58\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: planGenerado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleRegenerarPlan,\n                                            disabled: isGenerating,\n                                            className: \"flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiRefreshCw, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Regenerar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDescargarPlan,\n                                            className: \"flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiDownload, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Descargar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleImprimirPlan,\n                                            className: \"flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPrinter, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Imprimir\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, undefined),\n                !planGenerado ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCalendar, {\n                                className: \"w-10 h-10 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                            children: \"Genera tu Plan de Estudios Personalizado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                            children: \"Nuestro asistente de IA analizar\\xe1 tu planificaci\\xf3n, disponibilidad de tiempo, y las caracter\\xedsticas de cada tema para crear un plan de estudios completamente personalizado y realista.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGenerarPlan,\n                            disabled: isGenerating,\n                            className: \"flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 mx-auto\",\n                            children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Generando plan con IA...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDownload_FiPrinter_FiRefreshCw_FiTarget_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {\n                                        className: \"w-5 h-5 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \"Generar Plan de Estudios\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-blue max-w-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactMarkdown, {\n                            children: planGenerado\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\plan-estudios\\\\page.tsx\",\n        lineNumber: 246,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlanEstudiosPage, \"qEWtGHmTZ49TPKWxi2yu9C3ZvgI=\");\n_c = PlanEstudiosPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlanEstudiosPage);\nvar _c;\n$RefreshReg$(_c, \"PlanEstudiosPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/plan-estudios/page.tsx\n"));

/***/ })

});