import { supabase } from '@/lib/supabase/client';
import { obtenerUsuarioActual } from '@/features/auth/services/authService';
import { Temario, Tema, PlanificacionEstudio } from '@/lib/supabase/supabaseClient';

/**
 * Verifica si el usuario tiene un temario configurado
 */
export async function tieneTemarioConfigurado(): Promise<boolean> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      console.log('No hay usuario autenticado o error:', authError);
      return false;
    }

    console.log('Verificando temario para usuario:', user.id);

    const { data, error } = await supabase
      .from('temarios')
      .select('id')
      .eq('user_id', user.id)
      .limit(1);

    if (error) {
      console.error('Error al verificar temario en Supabase:', error);
      // Si es un error de tabla no encontrada, devolver false sin error
      if (error.code === 'PGRST116' || error.message?.includes('relation') || error.message?.includes('does not exist')) {
        console.log('Tabla temarios no encontrada, devolviendo false');
        return false;
      }
      return false;
    }

    const tieneTemario = data && data.length > 0;
    console.log('Resultado verificación temario:', tieneTemario);
    return tieneTemario;
  } catch (error) {
    console.error('Error general al verificar temario:', error);
    return false;
  }
}

/**
 * Obtiene el temario del usuario actual
 */
export async function obtenerTemarioUsuario(): Promise<Temario | null> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      console.log('No hay usuario autenticado para obtener temario o error:', authError);
      return null;
    }

    console.log('Obteniendo temario para usuario:', user.id);

    const { data, error } = await supabase
      .from('temarios')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error al obtener temario en Supabase:', error);
      // Si no hay datos, es normal (usuario sin temario)
      if (error.code === 'PGRST116') {
        console.log('Usuario no tiene temario configurado');
        return null;
      }
      return null;
    }

    console.log('Temario obtenido:', data);
    return data;
  } catch (error) {
    console.error('Error general al obtener temario:', error);
    return null;
  }
}

/**
 * Crea un nuevo temario
 */
export async function crearTemario(
  titulo: string,
  descripcion: string,
  tipo: 'completo' | 'temas_sueltos'
): Promise<string | null> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      console.error('No hay usuario autenticado o error:', authError);
      return null;
    }

    const { data, error } = await supabase
      .from('temarios')
      .insert([{
        titulo,
        descripcion,
        tipo,
        user_id: user.id
      }])
      .select()
      .single();

    if (error) {
      console.error('Error al crear temario:', error);
      return null;
    }

    return data.id;
  } catch (error) {
    console.error('Error al crear temario:', error);
    return null;
  }
}

/**
 * Obtiene los temas de un temario
 */
export async function obtenerTemas(temarioId: string): Promise<Tema[]> {
  try {
    const { data, error } = await supabase
      .from('temas')
      .select('*')
      .eq('temario_id', temarioId)
      .order('orden', { ascending: true });

    if (error) {
      console.error('Error al obtener temas:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener temas:', error);
    return [];
  }
}

/**
 * Crea múltiples temas para un temario
 */
export async function crearTemas(
  temarioId: string,
  temas: Array<{
    numero: number;
    titulo: string;
    descripcion?: string;
    orden: number;
  }>
): Promise<boolean> {
  try {
    const temasConTemarioId = temas.map(tema => ({
      ...tema,
      temario_id: temarioId
    }));

    const { error } = await supabase
      .from('temas')
      .insert(temasConTemarioId);

    if (error) {
      console.error('Error al crear temas:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al crear temas:', error);
    return false;
  }
}

/**
 * Actualiza el estado de completado de un tema
 */
export async function actualizarEstadoTema(
  temaId: string,
  completado: boolean
): Promise<boolean> {
  try {
    const updateData: any = {
      completado,
      actualizado_en: new Date().toISOString()
    };

    if (completado) {
      updateData.fecha_completado = new Date().toISOString();
    } else {
      updateData.fecha_completado = null;
    }

    const { error } = await supabase
      .from('temas')
      .update(updateData)
      .eq('id', temaId);

    if (error) {
      console.error('Error al actualizar estado del tema:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al actualizar estado del tema:', error);
    return false;
  }
}

/**
 * Obtiene estadísticas del temario
 */
export async function obtenerEstadisticasTemario(temarioId: string): Promise<{
  totalTemas: number;
  temasCompletados: number;
  porcentajeCompletado: number;
} | null> {
  try {
    const { data, error } = await supabase
      .from('temas')
      .select('completado')
      .eq('temario_id', temarioId);

    if (error) {
      console.error('Error al obtener estadísticas del temario:', error);
      return null;
    }

    const totalTemas = data.length;
    const temasCompletados = data.filter(tema => tema.completado).length;
    const porcentajeCompletado = totalTemas > 0 ? (temasCompletados / totalTemas) * 100 : 0;

    return {
      totalTemas,
      temasCompletados,
      porcentajeCompletado
    };
  } catch (error) {
    console.error('Error al obtener estadísticas del temario:', error);
    return null;
  }
}

/**
 * Elimina un temario y todos sus temas asociados
 */
export async function eliminarTemario(temarioId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('temarios')
      .delete()
      .eq('id', temarioId);

    if (error) {
      console.error('Error al eliminar temario:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al eliminar temario:', error);
    return false;
  }
}
