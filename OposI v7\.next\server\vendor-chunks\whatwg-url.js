"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/whatwg-url";
exports.ids = ["vendor-chunks/whatwg-url"];
exports.modules = {

/***/ "(rsc)/./node_modules/whatwg-url/lib/URL-impl.js":
/*!*************************************************!*\
  !*** ./node_modules/whatwg-url/lib/URL-impl.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nconst usm = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\");\n\nexports.implementation = class URLImpl {\n  constructor(constructorArgs) {\n    const url = constructorArgs[0];\n    const base = constructorArgs[1];\n\n    let parsedBase = null;\n    if (base !== undefined) {\n      parsedBase = usm.basicURLParse(base);\n      if (parsedBase === \"failure\") {\n        throw new TypeError(\"Invalid base URL\");\n      }\n    }\n\n    const parsedURL = usm.basicURLParse(url, { baseURL: parsedBase });\n    if (parsedURL === \"failure\") {\n      throw new TypeError(\"Invalid URL\");\n    }\n\n    this._url = parsedURL;\n\n    // TODO: query stuff\n  }\n\n  get href() {\n    return usm.serializeURL(this._url);\n  }\n\n  set href(v) {\n    const parsedURL = usm.basicURLParse(v);\n    if (parsedURL === \"failure\") {\n      throw new TypeError(\"Invalid URL\");\n    }\n\n    this._url = parsedURL;\n  }\n\n  get origin() {\n    return usm.serializeURLOrigin(this._url);\n  }\n\n  get protocol() {\n    return this._url.scheme + \":\";\n  }\n\n  set protocol(v) {\n    usm.basicURLParse(v + \":\", { url: this._url, stateOverride: \"scheme start\" });\n  }\n\n  get username() {\n    return this._url.username;\n  }\n\n  set username(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    usm.setTheUsername(this._url, v);\n  }\n\n  get password() {\n    return this._url.password;\n  }\n\n  set password(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    usm.setThePassword(this._url, v);\n  }\n\n  get host() {\n    const url = this._url;\n\n    if (url.host === null) {\n      return \"\";\n    }\n\n    if (url.port === null) {\n      return usm.serializeHost(url.host);\n    }\n\n    return usm.serializeHost(url.host) + \":\" + usm.serializeInteger(url.port);\n  }\n\n  set host(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"host\" });\n  }\n\n  get hostname() {\n    if (this._url.host === null) {\n      return \"\";\n    }\n\n    return usm.serializeHost(this._url.host);\n  }\n\n  set hostname(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"hostname\" });\n  }\n\n  get port() {\n    if (this._url.port === null) {\n      return \"\";\n    }\n\n    return usm.serializeInteger(this._url.port);\n  }\n\n  set port(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    if (v === \"\") {\n      this._url.port = null;\n    } else {\n      usm.basicURLParse(v, { url: this._url, stateOverride: \"port\" });\n    }\n  }\n\n  get pathname() {\n    if (this._url.cannotBeABaseURL) {\n      return this._url.path[0];\n    }\n\n    if (this._url.path.length === 0) {\n      return \"\";\n    }\n\n    return \"/\" + this._url.path.join(\"/\");\n  }\n\n  set pathname(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    this._url.path = [];\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"path start\" });\n  }\n\n  get search() {\n    if (this._url.query === null || this._url.query === \"\") {\n      return \"\";\n    }\n\n    return \"?\" + this._url.query;\n  }\n\n  set search(v) {\n    // TODO: query stuff\n\n    const url = this._url;\n\n    if (v === \"\") {\n      url.query = null;\n      return;\n    }\n\n    const input = v[0] === \"?\" ? v.substring(1) : v;\n    url.query = \"\";\n    usm.basicURLParse(input, { url, stateOverride: \"query\" });\n  }\n\n  get hash() {\n    if (this._url.fragment === null || this._url.fragment === \"\") {\n      return \"\";\n    }\n\n    return \"#\" + this._url.fragment;\n  }\n\n  set hash(v) {\n    if (v === \"\") {\n      this._url.fragment = null;\n      return;\n    }\n\n    const input = v[0] === \"#\" ? v.substring(1) : v;\n    this._url.fragment = \"\";\n    usm.basicURLParse(input, { url: this._url, stateOverride: \"fragment\" });\n  }\n\n  toJSON() {\n    return this.href;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-url/lib/URL-impl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-url/lib/URL.js":
/*!********************************************!*\
  !*** ./node_modules/whatwg-url/lib/URL.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst conversions = __webpack_require__(/*! webidl-conversions */ \"(rsc)/./node_modules/webidl-conversions/lib/index.js\");\nconst utils = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/whatwg-url/lib/utils.js\");\nconst Impl = __webpack_require__(/*! .//URL-impl.js */ \"(rsc)/./node_modules/whatwg-url/lib/URL-impl.js\");\n\nconst impl = utils.implSymbol;\n\nfunction URL(url) {\n  if (!this || this[impl] || !(this instanceof URL)) {\n    throw new TypeError(\"Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.\");\n  }\n  if (arguments.length < 1) {\n    throw new TypeError(\"Failed to construct 'URL': 1 argument required, but only \" + arguments.length + \" present.\");\n  }\n  const args = [];\n  for (let i = 0; i < arguments.length && i < 2; ++i) {\n    args[i] = arguments[i];\n  }\n  args[0] = conversions[\"USVString\"](args[0]);\n  if (args[1] !== undefined) {\n  args[1] = conversions[\"USVString\"](args[1]);\n  }\n\n  module.exports.setup(this, args);\n}\n\nURL.prototype.toJSON = function toJSON() {\n  if (!this || !module.exports.is(this)) {\n    throw new TypeError(\"Illegal invocation\");\n  }\n  const args = [];\n  for (let i = 0; i < arguments.length && i < 0; ++i) {\n    args[i] = arguments[i];\n  }\n  return this[impl].toJSON.apply(this[impl], args);\n};\nObject.defineProperty(URL.prototype, \"href\", {\n  get() {\n    return this[impl].href;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].href = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nURL.prototype.toString = function () {\n  if (!this || !module.exports.is(this)) {\n    throw new TypeError(\"Illegal invocation\");\n  }\n  return this.href;\n};\n\nObject.defineProperty(URL.prototype, \"origin\", {\n  get() {\n    return this[impl].origin;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"protocol\", {\n  get() {\n    return this[impl].protocol;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].protocol = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"username\", {\n  get() {\n    return this[impl].username;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].username = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"password\", {\n  get() {\n    return this[impl].password;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].password = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"host\", {\n  get() {\n    return this[impl].host;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].host = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"hostname\", {\n  get() {\n    return this[impl].hostname;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].hostname = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"port\", {\n  get() {\n    return this[impl].port;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].port = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"pathname\", {\n  get() {\n    return this[impl].pathname;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].pathname = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"search\", {\n  get() {\n    return this[impl].search;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].search = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"hash\", {\n  get() {\n    return this[impl].hash;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].hash = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\n\nmodule.exports = {\n  is(obj) {\n    return !!obj && obj[impl] instanceof Impl.implementation;\n  },\n  create(constructorArgs, privateData) {\n    let obj = Object.create(URL.prototype);\n    this.setup(obj, constructorArgs, privateData);\n    return obj;\n  },\n  setup(obj, constructorArgs, privateData) {\n    if (!privateData) privateData = {};\n    privateData.wrapper = obj;\n\n    obj[impl] = new Impl.implementation(constructorArgs, privateData);\n    obj[impl][utils.wrapperSymbol] = obj;\n  },\n  interface: URL,\n  expose: {\n    Window: { URL: URL },\n    Worker: { URL: URL }\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-url/lib/URL.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-url/lib/public-api.js":
/*!***************************************************!*\
  !*** ./node_modules/whatwg-url/lib/public-api.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nexports.URL = __webpack_require__(/*! ./URL */ \"(rsc)/./node_modules/whatwg-url/lib/URL.js\")[\"interface\"];\nexports.serializeURL = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeURL;\nexports.serializeURLOrigin = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeURLOrigin;\nexports.basicURLParse = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").basicURLParse;\nexports.setTheUsername = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").setTheUsername;\nexports.setThePassword = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").setThePassword;\nexports.serializeHost = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeHost;\nexports.serializeInteger = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeInteger;\nexports.parseURL = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").parseURL;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvcHVibGljLWFwaS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYix5R0FBd0M7QUFDeEMsOElBQWtFO0FBQ2xFLDBKQUE4RTtBQUM5RSxnSkFBb0U7QUFDcEUsa0pBQXNFO0FBQ3RFLGtKQUFzRTtBQUN0RSxnSkFBb0U7QUFDcEUsc0pBQTBFO0FBQzFFLHNJQUEwRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXG5vZGVfbW9kdWxlc1xcd2hhdHdnLXVybFxcbGliXFxwdWJsaWMtYXBpLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5leHBvcnRzLlVSTCA9IHJlcXVpcmUoXCIuL1VSTFwiKS5pbnRlcmZhY2U7XG5leHBvcnRzLnNlcmlhbGl6ZVVSTCA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNlcmlhbGl6ZVVSTDtcbmV4cG9ydHMuc2VyaWFsaXplVVJMT3JpZ2luID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2VyaWFsaXplVVJMT3JpZ2luO1xuZXhwb3J0cy5iYXNpY1VSTFBhcnNlID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuYmFzaWNVUkxQYXJzZTtcbmV4cG9ydHMuc2V0VGhlVXNlcm5hbWUgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXRUaGVVc2VybmFtZTtcbmV4cG9ydHMuc2V0VGhlUGFzc3dvcmQgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXRUaGVQYXNzd29yZDtcbmV4cG9ydHMuc2VyaWFsaXplSG9zdCA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNlcmlhbGl6ZUhvc3Q7XG5leHBvcnRzLnNlcmlhbGl6ZUludGVnZXIgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXJpYWxpemVJbnRlZ2VyO1xuZXhwb3J0cy5wYXJzZVVSTCA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnBhcnNlVVJMO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-url/lib/public-api.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js":
/*!**********************************************************!*\
  !*** ./node_modules/whatwg-url/lib/url-state-machine.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\r\nconst punycode = __webpack_require__(/*! punycode */ \"punycode\");\r\nconst tr46 = __webpack_require__(/*! tr46 */ \"(rsc)/./node_modules/tr46/index.js\");\r\n\r\nconst specialSchemes = {\r\n  ftp: 21,\r\n  file: null,\r\n  gopher: 70,\r\n  http: 80,\r\n  https: 443,\r\n  ws: 80,\r\n  wss: 443\r\n};\r\n\r\nconst failure = Symbol(\"failure\");\r\n\r\nfunction countSymbols(str) {\r\n  return punycode.ucs2.decode(str).length;\r\n}\r\n\r\nfunction at(input, idx) {\r\n  const c = input[idx];\r\n  return isNaN(c) ? undefined : String.fromCodePoint(c);\r\n}\r\n\r\nfunction isASCIIDigit(c) {\r\n  return c >= 0x30 && c <= 0x39;\r\n}\r\n\r\nfunction isASCIIAlpha(c) {\r\n  return (c >= 0x41 && c <= 0x5A) || (c >= 0x61 && c <= 0x7A);\r\n}\r\n\r\nfunction isASCIIAlphanumeric(c) {\r\n  return isASCIIAlpha(c) || isASCIIDigit(c);\r\n}\r\n\r\nfunction isASCIIHex(c) {\r\n  return isASCIIDigit(c) || (c >= 0x41 && c <= 0x46) || (c >= 0x61 && c <= 0x66);\r\n}\r\n\r\nfunction isSingleDot(buffer) {\r\n  return buffer === \".\" || buffer.toLowerCase() === \"%2e\";\r\n}\r\n\r\nfunction isDoubleDot(buffer) {\r\n  buffer = buffer.toLowerCase();\r\n  return buffer === \"..\" || buffer === \"%2e.\" || buffer === \".%2e\" || buffer === \"%2e%2e\";\r\n}\r\n\r\nfunction isWindowsDriveLetterCodePoints(cp1, cp2) {\r\n  return isASCIIAlpha(cp1) && (cp2 === 58 || cp2 === 124);\r\n}\r\n\r\nfunction isWindowsDriveLetterString(string) {\r\n  return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && (string[1] === \":\" || string[1] === \"|\");\r\n}\r\n\r\nfunction isNormalizedWindowsDriveLetterString(string) {\r\n  return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && string[1] === \":\";\r\n}\r\n\r\nfunction containsForbiddenHostCodePoint(string) {\r\n  return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|%|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\r\n}\r\n\r\nfunction containsForbiddenHostCodePointExcludingPercent(string) {\r\n  return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\r\n}\r\n\r\nfunction isSpecialScheme(scheme) {\r\n  return specialSchemes[scheme] !== undefined;\r\n}\r\n\r\nfunction isSpecial(url) {\r\n  return isSpecialScheme(url.scheme);\r\n}\r\n\r\nfunction defaultPort(scheme) {\r\n  return specialSchemes[scheme];\r\n}\r\n\r\nfunction percentEncode(c) {\r\n  let hex = c.toString(16).toUpperCase();\r\n  if (hex.length === 1) {\r\n    hex = \"0\" + hex;\r\n  }\r\n\r\n  return \"%\" + hex;\r\n}\r\n\r\nfunction utf8PercentEncode(c) {\r\n  const buf = new Buffer(c);\r\n\r\n  let str = \"\";\r\n\r\n  for (let i = 0; i < buf.length; ++i) {\r\n    str += percentEncode(buf[i]);\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\nfunction utf8PercentDecode(str) {\r\n  const input = new Buffer(str);\r\n  const output = [];\r\n  for (let i = 0; i < input.length; ++i) {\r\n    if (input[i] !== 37) {\r\n      output.push(input[i]);\r\n    } else if (input[i] === 37 && isASCIIHex(input[i + 1]) && isASCIIHex(input[i + 2])) {\r\n      output.push(parseInt(input.slice(i + 1, i + 3).toString(), 16));\r\n      i += 2;\r\n    } else {\r\n      output.push(input[i]);\r\n    }\r\n  }\r\n  return new Buffer(output).toString();\r\n}\r\n\r\nfunction isC0ControlPercentEncode(c) {\r\n  return c <= 0x1F || c > 0x7E;\r\n}\r\n\r\nconst extraPathPercentEncodeSet = new Set([32, 34, 35, 60, 62, 63, 96, 123, 125]);\r\nfunction isPathPercentEncode(c) {\r\n  return isC0ControlPercentEncode(c) || extraPathPercentEncodeSet.has(c);\r\n}\r\n\r\nconst extraUserinfoPercentEncodeSet =\r\n  new Set([47, 58, 59, 61, 64, 91, 92, 93, 94, 124]);\r\nfunction isUserinfoPercentEncode(c) {\r\n  return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);\r\n}\r\n\r\nfunction percentEncodeChar(c, encodeSetPredicate) {\r\n  const cStr = String.fromCodePoint(c);\r\n\r\n  if (encodeSetPredicate(c)) {\r\n    return utf8PercentEncode(cStr);\r\n  }\r\n\r\n  return cStr;\r\n}\r\n\r\nfunction parseIPv4Number(input) {\r\n  let R = 10;\r\n\r\n  if (input.length >= 2 && input.charAt(0) === \"0\" && input.charAt(1).toLowerCase() === \"x\") {\r\n    input = input.substring(2);\r\n    R = 16;\r\n  } else if (input.length >= 2 && input.charAt(0) === \"0\") {\r\n    input = input.substring(1);\r\n    R = 8;\r\n  }\r\n\r\n  if (input === \"\") {\r\n    return 0;\r\n  }\r\n\r\n  const regex = R === 10 ? /[^0-9]/ : (R === 16 ? /[^0-9A-Fa-f]/ : /[^0-7]/);\r\n  if (regex.test(input)) {\r\n    return failure;\r\n  }\r\n\r\n  return parseInt(input, R);\r\n}\r\n\r\nfunction parseIPv4(input) {\r\n  const parts = input.split(\".\");\r\n  if (parts[parts.length - 1] === \"\") {\r\n    if (parts.length > 1) {\r\n      parts.pop();\r\n    }\r\n  }\r\n\r\n  if (parts.length > 4) {\r\n    return input;\r\n  }\r\n\r\n  const numbers = [];\r\n  for (const part of parts) {\r\n    if (part === \"\") {\r\n      return input;\r\n    }\r\n    const n = parseIPv4Number(part);\r\n    if (n === failure) {\r\n      return input;\r\n    }\r\n\r\n    numbers.push(n);\r\n  }\r\n\r\n  for (let i = 0; i < numbers.length - 1; ++i) {\r\n    if (numbers[i] > 255) {\r\n      return failure;\r\n    }\r\n  }\r\n  if (numbers[numbers.length - 1] >= Math.pow(256, 5 - numbers.length)) {\r\n    return failure;\r\n  }\r\n\r\n  let ipv4 = numbers.pop();\r\n  let counter = 0;\r\n\r\n  for (const n of numbers) {\r\n    ipv4 += n * Math.pow(256, 3 - counter);\r\n    ++counter;\r\n  }\r\n\r\n  return ipv4;\r\n}\r\n\r\nfunction serializeIPv4(address) {\r\n  let output = \"\";\r\n  let n = address;\r\n\r\n  for (let i = 1; i <= 4; ++i) {\r\n    output = String(n % 256) + output;\r\n    if (i !== 4) {\r\n      output = \".\" + output;\r\n    }\r\n    n = Math.floor(n / 256);\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction parseIPv6(input) {\r\n  const address = [0, 0, 0, 0, 0, 0, 0, 0];\r\n  let pieceIndex = 0;\r\n  let compress = null;\r\n  let pointer = 0;\r\n\r\n  input = punycode.ucs2.decode(input);\r\n\r\n  if (input[pointer] === 58) {\r\n    if (input[pointer + 1] !== 58) {\r\n      return failure;\r\n    }\r\n\r\n    pointer += 2;\r\n    ++pieceIndex;\r\n    compress = pieceIndex;\r\n  }\r\n\r\n  while (pointer < input.length) {\r\n    if (pieceIndex === 8) {\r\n      return failure;\r\n    }\r\n\r\n    if (input[pointer] === 58) {\r\n      if (compress !== null) {\r\n        return failure;\r\n      }\r\n      ++pointer;\r\n      ++pieceIndex;\r\n      compress = pieceIndex;\r\n      continue;\r\n    }\r\n\r\n    let value = 0;\r\n    let length = 0;\r\n\r\n    while (length < 4 && isASCIIHex(input[pointer])) {\r\n      value = value * 0x10 + parseInt(at(input, pointer), 16);\r\n      ++pointer;\r\n      ++length;\r\n    }\r\n\r\n    if (input[pointer] === 46) {\r\n      if (length === 0) {\r\n        return failure;\r\n      }\r\n\r\n      pointer -= length;\r\n\r\n      if (pieceIndex > 6) {\r\n        return failure;\r\n      }\r\n\r\n      let numbersSeen = 0;\r\n\r\n      while (input[pointer] !== undefined) {\r\n        let ipv4Piece = null;\r\n\r\n        if (numbersSeen > 0) {\r\n          if (input[pointer] === 46 && numbersSeen < 4) {\r\n            ++pointer;\r\n          } else {\r\n            return failure;\r\n          }\r\n        }\r\n\r\n        if (!isASCIIDigit(input[pointer])) {\r\n          return failure;\r\n        }\r\n\r\n        while (isASCIIDigit(input[pointer])) {\r\n          const number = parseInt(at(input, pointer));\r\n          if (ipv4Piece === null) {\r\n            ipv4Piece = number;\r\n          } else if (ipv4Piece === 0) {\r\n            return failure;\r\n          } else {\r\n            ipv4Piece = ipv4Piece * 10 + number;\r\n          }\r\n          if (ipv4Piece > 255) {\r\n            return failure;\r\n          }\r\n          ++pointer;\r\n        }\r\n\r\n        address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;\r\n\r\n        ++numbersSeen;\r\n\r\n        if (numbersSeen === 2 || numbersSeen === 4) {\r\n          ++pieceIndex;\r\n        }\r\n      }\r\n\r\n      if (numbersSeen !== 4) {\r\n        return failure;\r\n      }\r\n\r\n      break;\r\n    } else if (input[pointer] === 58) {\r\n      ++pointer;\r\n      if (input[pointer] === undefined) {\r\n        return failure;\r\n      }\r\n    } else if (input[pointer] !== undefined) {\r\n      return failure;\r\n    }\r\n\r\n    address[pieceIndex] = value;\r\n    ++pieceIndex;\r\n  }\r\n\r\n  if (compress !== null) {\r\n    let swaps = pieceIndex - compress;\r\n    pieceIndex = 7;\r\n    while (pieceIndex !== 0 && swaps > 0) {\r\n      const temp = address[compress + swaps - 1];\r\n      address[compress + swaps - 1] = address[pieceIndex];\r\n      address[pieceIndex] = temp;\r\n      --pieceIndex;\r\n      --swaps;\r\n    }\r\n  } else if (compress === null && pieceIndex !== 8) {\r\n    return failure;\r\n  }\r\n\r\n  return address;\r\n}\r\n\r\nfunction serializeIPv6(address) {\r\n  let output = \"\";\r\n  const seqResult = findLongestZeroSequence(address);\r\n  const compress = seqResult.idx;\r\n  let ignore0 = false;\r\n\r\n  for (let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex) {\r\n    if (ignore0 && address[pieceIndex] === 0) {\r\n      continue;\r\n    } else if (ignore0) {\r\n      ignore0 = false;\r\n    }\r\n\r\n    if (compress === pieceIndex) {\r\n      const separator = pieceIndex === 0 ? \"::\" : \":\";\r\n      output += separator;\r\n      ignore0 = true;\r\n      continue;\r\n    }\r\n\r\n    output += address[pieceIndex].toString(16);\r\n\r\n    if (pieceIndex !== 7) {\r\n      output += \":\";\r\n    }\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction parseHost(input, isSpecialArg) {\r\n  if (input[0] === \"[\") {\r\n    if (input[input.length - 1] !== \"]\") {\r\n      return failure;\r\n    }\r\n\r\n    return parseIPv6(input.substring(1, input.length - 1));\r\n  }\r\n\r\n  if (!isSpecialArg) {\r\n    return parseOpaqueHost(input);\r\n  }\r\n\r\n  const domain = utf8PercentDecode(input);\r\n  const asciiDomain = tr46.toASCII(domain, false, tr46.PROCESSING_OPTIONS.NONTRANSITIONAL, false);\r\n  if (asciiDomain === null) {\r\n    return failure;\r\n  }\r\n\r\n  if (containsForbiddenHostCodePoint(asciiDomain)) {\r\n    return failure;\r\n  }\r\n\r\n  const ipv4Host = parseIPv4(asciiDomain);\r\n  if (typeof ipv4Host === \"number\" || ipv4Host === failure) {\r\n    return ipv4Host;\r\n  }\r\n\r\n  return asciiDomain;\r\n}\r\n\r\nfunction parseOpaqueHost(input) {\r\n  if (containsForbiddenHostCodePointExcludingPercent(input)) {\r\n    return failure;\r\n  }\r\n\r\n  let output = \"\";\r\n  const decoded = punycode.ucs2.decode(input);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    output += percentEncodeChar(decoded[i], isC0ControlPercentEncode);\r\n  }\r\n  return output;\r\n}\r\n\r\nfunction findLongestZeroSequence(arr) {\r\n  let maxIdx = null;\r\n  let maxLen = 1; // only find elements > 1\r\n  let currStart = null;\r\n  let currLen = 0;\r\n\r\n  for (let i = 0; i < arr.length; ++i) {\r\n    if (arr[i] !== 0) {\r\n      if (currLen > maxLen) {\r\n        maxIdx = currStart;\r\n        maxLen = currLen;\r\n      }\r\n\r\n      currStart = null;\r\n      currLen = 0;\r\n    } else {\r\n      if (currStart === null) {\r\n        currStart = i;\r\n      }\r\n      ++currLen;\r\n    }\r\n  }\r\n\r\n  // if trailing zeros\r\n  if (currLen > maxLen) {\r\n    maxIdx = currStart;\r\n    maxLen = currLen;\r\n  }\r\n\r\n  return {\r\n    idx: maxIdx,\r\n    len: maxLen\r\n  };\r\n}\r\n\r\nfunction serializeHost(host) {\r\n  if (typeof host === \"number\") {\r\n    return serializeIPv4(host);\r\n  }\r\n\r\n  // IPv6 serializer\r\n  if (host instanceof Array) {\r\n    return \"[\" + serializeIPv6(host) + \"]\";\r\n  }\r\n\r\n  return host;\r\n}\r\n\r\nfunction trimControlChars(url) {\r\n  return url.replace(/^[\\u0000-\\u001F\\u0020]+|[\\u0000-\\u001F\\u0020]+$/g, \"\");\r\n}\r\n\r\nfunction trimTabAndNewline(url) {\r\n  return url.replace(/\\u0009|\\u000A|\\u000D/g, \"\");\r\n}\r\n\r\nfunction shortenPath(url) {\r\n  const path = url.path;\r\n  if (path.length === 0) {\r\n    return;\r\n  }\r\n  if (url.scheme === \"file\" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {\r\n    return;\r\n  }\r\n\r\n  path.pop();\r\n}\r\n\r\nfunction includesCredentials(url) {\r\n  return url.username !== \"\" || url.password !== \"\";\r\n}\r\n\r\nfunction cannotHaveAUsernamePasswordPort(url) {\r\n  return url.host === null || url.host === \"\" || url.cannotBeABaseURL || url.scheme === \"file\";\r\n}\r\n\r\nfunction isNormalizedWindowsDriveLetter(string) {\r\n  return /^[A-Za-z]:$/.test(string);\r\n}\r\n\r\nfunction URLStateMachine(input, base, encodingOverride, url, stateOverride) {\r\n  this.pointer = 0;\r\n  this.input = input;\r\n  this.base = base || null;\r\n  this.encodingOverride = encodingOverride || \"utf-8\";\r\n  this.stateOverride = stateOverride;\r\n  this.url = url;\r\n  this.failure = false;\r\n  this.parseError = false;\r\n\r\n  if (!this.url) {\r\n    this.url = {\r\n      scheme: \"\",\r\n      username: \"\",\r\n      password: \"\",\r\n      host: null,\r\n      port: null,\r\n      path: [],\r\n      query: null,\r\n      fragment: null,\r\n\r\n      cannotBeABaseURL: false\r\n    };\r\n\r\n    const res = trimControlChars(this.input);\r\n    if (res !== this.input) {\r\n      this.parseError = true;\r\n    }\r\n    this.input = res;\r\n  }\r\n\r\n  const res = trimTabAndNewline(this.input);\r\n  if (res !== this.input) {\r\n    this.parseError = true;\r\n  }\r\n  this.input = res;\r\n\r\n  this.state = stateOverride || \"scheme start\";\r\n\r\n  this.buffer = \"\";\r\n  this.atFlag = false;\r\n  this.arrFlag = false;\r\n  this.passwordTokenSeenFlag = false;\r\n\r\n  this.input = punycode.ucs2.decode(this.input);\r\n\r\n  for (; this.pointer <= this.input.length; ++this.pointer) {\r\n    const c = this.input[this.pointer];\r\n    const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);\r\n\r\n    // exec state machine\r\n    const ret = this[\"parse \" + this.state](c, cStr);\r\n    if (!ret) {\r\n      break; // terminate algorithm\r\n    } else if (ret === failure) {\r\n      this.failure = true;\r\n      break;\r\n    }\r\n  }\r\n}\r\n\r\nURLStateMachine.prototype[\"parse scheme start\"] = function parseSchemeStart(c, cStr) {\r\n  if (isASCIIAlpha(c)) {\r\n    this.buffer += cStr.toLowerCase();\r\n    this.state = \"scheme\";\r\n  } else if (!this.stateOverride) {\r\n    this.state = \"no scheme\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse scheme\"] = function parseScheme(c, cStr) {\r\n  if (isASCIIAlphanumeric(c) || c === 43 || c === 45 || c === 46) {\r\n    this.buffer += cStr.toLowerCase();\r\n  } else if (c === 58) {\r\n    if (this.stateOverride) {\r\n      if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {\r\n        return false;\r\n      }\r\n\r\n      if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {\r\n        return false;\r\n      }\r\n\r\n      if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === \"file\") {\r\n        return false;\r\n      }\r\n\r\n      if (this.url.scheme === \"file\" && (this.url.host === \"\" || this.url.host === null)) {\r\n        return false;\r\n      }\r\n    }\r\n    this.url.scheme = this.buffer;\r\n    this.buffer = \"\";\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n    if (this.url.scheme === \"file\") {\r\n      if (this.input[this.pointer + 1] !== 47 || this.input[this.pointer + 2] !== 47) {\r\n        this.parseError = true;\r\n      }\r\n      this.state = \"file\";\r\n    } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {\r\n      this.state = \"special relative or authority\";\r\n    } else if (isSpecial(this.url)) {\r\n      this.state = \"special authority slashes\";\r\n    } else if (this.input[this.pointer + 1] === 47) {\r\n      this.state = \"path or authority\";\r\n      ++this.pointer;\r\n    } else {\r\n      this.url.cannotBeABaseURL = true;\r\n      this.url.path.push(\"\");\r\n      this.state = \"cannot-be-a-base-URL path\";\r\n    }\r\n  } else if (!this.stateOverride) {\r\n    this.buffer = \"\";\r\n    this.state = \"no scheme\";\r\n    this.pointer = -1;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse no scheme\"] = function parseNoScheme(c) {\r\n  if (this.base === null || (this.base.cannotBeABaseURL && c !== 35)) {\r\n    return failure;\r\n  } else if (this.base.cannotBeABaseURL && c === 35) {\r\n    this.url.scheme = this.base.scheme;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n    this.url.fragment = \"\";\r\n    this.url.cannotBeABaseURL = true;\r\n    this.state = \"fragment\";\r\n  } else if (this.base.scheme === \"file\") {\r\n    this.state = \"file\";\r\n    --this.pointer;\r\n  } else {\r\n    this.state = \"relative\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special relative or authority\"] = function parseSpecialRelativeOrAuthority(c) {\r\n  if (c === 47 && this.input[this.pointer + 1] === 47) {\r\n    this.state = \"special authority ignore slashes\";\r\n    ++this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    this.state = \"relative\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path or authority\"] = function parsePathOrAuthority(c) {\r\n  if (c === 47) {\r\n    this.state = \"authority\";\r\n  } else {\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse relative\"] = function parseRelative(c) {\r\n  this.url.scheme = this.base.scheme;\r\n  if (isNaN(c)) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n  } else if (c === 47) {\r\n    this.state = \"relative slash\";\r\n  } else if (c === 63) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (c === 35) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else if (isSpecial(this.url) && c === 92) {\r\n    this.parseError = true;\r\n    this.state = \"relative slash\";\r\n  } else {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice(0, this.base.path.length - 1);\r\n\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse relative slash\"] = function parseRelativeSlash(c) {\r\n  if (isSpecial(this.url) && (c === 47 || c === 92)) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"special authority ignore slashes\";\r\n  } else if (c === 47) {\r\n    this.state = \"authority\";\r\n  } else {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special authority slashes\"] = function parseSpecialAuthoritySlashes(c) {\r\n  if (c === 47 && this.input[this.pointer + 1] === 47) {\r\n    this.state = \"special authority ignore slashes\";\r\n    ++this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    this.state = \"special authority ignore slashes\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special authority ignore slashes\"] = function parseSpecialAuthorityIgnoreSlashes(c) {\r\n  if (c !== 47 && c !== 92) {\r\n    this.state = \"authority\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse authority\"] = function parseAuthority(c, cStr) {\r\n  if (c === 64) {\r\n    this.parseError = true;\r\n    if (this.atFlag) {\r\n      this.buffer = \"%40\" + this.buffer;\r\n    }\r\n    this.atFlag = true;\r\n\r\n    // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars\r\n    const len = countSymbols(this.buffer);\r\n    for (let pointer = 0; pointer < len; ++pointer) {\r\n      const codePoint = this.buffer.codePointAt(pointer);\r\n\r\n      if (codePoint === 58 && !this.passwordTokenSeenFlag) {\r\n        this.passwordTokenSeenFlag = true;\r\n        continue;\r\n      }\r\n      const encodedCodePoints = percentEncodeChar(codePoint, isUserinfoPercentEncode);\r\n      if (this.passwordTokenSeenFlag) {\r\n        this.url.password += encodedCodePoints;\r\n      } else {\r\n        this.url.username += encodedCodePoints;\r\n      }\r\n    }\r\n    this.buffer = \"\";\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92)) {\r\n    if (this.atFlag && this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    }\r\n    this.pointer -= countSymbols(this.buffer) + 1;\r\n    this.buffer = \"\";\r\n    this.state = \"host\";\r\n  } else {\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse hostname\"] =\r\nURLStateMachine.prototype[\"parse host\"] = function parseHostName(c, cStr) {\r\n  if (this.stateOverride && this.url.scheme === \"file\") {\r\n    --this.pointer;\r\n    this.state = \"file host\";\r\n  } else if (c === 58 && !this.arrFlag) {\r\n    if (this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    }\r\n\r\n    const host = parseHost(this.buffer, isSpecial(this.url));\r\n    if (host === failure) {\r\n      return failure;\r\n    }\r\n\r\n    this.url.host = host;\r\n    this.buffer = \"\";\r\n    this.state = \"port\";\r\n    if (this.stateOverride === \"hostname\") {\r\n      return false;\r\n    }\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92)) {\r\n    --this.pointer;\r\n    if (isSpecial(this.url) && this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    } else if (this.stateOverride && this.buffer === \"\" &&\r\n               (includesCredentials(this.url) || this.url.port !== null)) {\r\n      this.parseError = true;\r\n      return false;\r\n    }\r\n\r\n    const host = parseHost(this.buffer, isSpecial(this.url));\r\n    if (host === failure) {\r\n      return failure;\r\n    }\r\n\r\n    this.url.host = host;\r\n    this.buffer = \"\";\r\n    this.state = \"path start\";\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n  } else {\r\n    if (c === 91) {\r\n      this.arrFlag = true;\r\n    } else if (c === 93) {\r\n      this.arrFlag = false;\r\n    }\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse port\"] = function parsePort(c, cStr) {\r\n  if (isASCIIDigit(c)) {\r\n    this.buffer += cStr;\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92) ||\r\n             this.stateOverride) {\r\n    if (this.buffer !== \"\") {\r\n      const port = parseInt(this.buffer);\r\n      if (port > Math.pow(2, 16) - 1) {\r\n        this.parseError = true;\r\n        return failure;\r\n      }\r\n      this.url.port = port === defaultPort(this.url.scheme) ? null : port;\r\n      this.buffer = \"\";\r\n    }\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n    this.state = \"path start\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nconst fileOtherwiseCodePoints = new Set([47, 92, 63, 35]);\r\n\r\nURLStateMachine.prototype[\"parse file\"] = function parseFile(c) {\r\n  this.url.scheme = \"file\";\r\n\r\n  if (c === 47 || c === 92) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"file slash\";\r\n  } else if (this.base !== null && this.base.scheme === \"file\") {\r\n    if (isNaN(c)) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = this.base.query;\r\n    } else if (c === 63) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = \"\";\r\n      this.state = \"query\";\r\n    } else if (c === 35) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = this.base.query;\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    } else {\r\n      if (this.input.length - this.pointer - 1 === 0 || // remaining consists of 0 code points\r\n          !isWindowsDriveLetterCodePoints(c, this.input[this.pointer + 1]) ||\r\n          (this.input.length - this.pointer - 1 >= 2 && // remaining has at least 2 code points\r\n           !fileOtherwiseCodePoints.has(this.input[this.pointer + 2]))) {\r\n        this.url.host = this.base.host;\r\n        this.url.path = this.base.path.slice();\r\n        shortenPath(this.url);\r\n      } else {\r\n        this.parseError = true;\r\n      }\r\n\r\n      this.state = \"path\";\r\n      --this.pointer;\r\n    }\r\n  } else {\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse file slash\"] = function parseFileSlash(c) {\r\n  if (c === 47 || c === 92) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"file host\";\r\n  } else {\r\n    if (this.base !== null && this.base.scheme === \"file\") {\r\n      if (isNormalizedWindowsDriveLetterString(this.base.path[0])) {\r\n        this.url.path.push(this.base.path[0]);\r\n      } else {\r\n        this.url.host = this.base.host;\r\n      }\r\n    }\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse file host\"] = function parseFileHost(c, cStr) {\r\n  if (isNaN(c) || c === 47 || c === 92 || c === 63 || c === 35) {\r\n    --this.pointer;\r\n    if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {\r\n      this.parseError = true;\r\n      this.state = \"path\";\r\n    } else if (this.buffer === \"\") {\r\n      this.url.host = \"\";\r\n      if (this.stateOverride) {\r\n        return false;\r\n      }\r\n      this.state = \"path start\";\r\n    } else {\r\n      let host = parseHost(this.buffer, isSpecial(this.url));\r\n      if (host === failure) {\r\n        return failure;\r\n      }\r\n      if (host === \"localhost\") {\r\n        host = \"\";\r\n      }\r\n      this.url.host = host;\r\n\r\n      if (this.stateOverride) {\r\n        return false;\r\n      }\r\n\r\n      this.buffer = \"\";\r\n      this.state = \"path start\";\r\n    }\r\n  } else {\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path start\"] = function parsePathStart(c) {\r\n  if (isSpecial(this.url)) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"path\";\r\n\r\n    if (c !== 47 && c !== 92) {\r\n      --this.pointer;\r\n    }\r\n  } else if (!this.stateOverride && c === 63) {\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (!this.stateOverride && c === 35) {\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else if (c !== undefined) {\r\n    this.state = \"path\";\r\n    if (c !== 47) {\r\n      --this.pointer;\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path\"] = function parsePath(c) {\r\n  if (isNaN(c) || c === 47 || (isSpecial(this.url) && c === 92) ||\r\n      (!this.stateOverride && (c === 63 || c === 35))) {\r\n    if (isSpecial(this.url) && c === 92) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (isDoubleDot(this.buffer)) {\r\n      shortenPath(this.url);\r\n      if (c !== 47 && !(isSpecial(this.url) && c === 92)) {\r\n        this.url.path.push(\"\");\r\n      }\r\n    } else if (isSingleDot(this.buffer) && c !== 47 &&\r\n               !(isSpecial(this.url) && c === 92)) {\r\n      this.url.path.push(\"\");\r\n    } else if (!isSingleDot(this.buffer)) {\r\n      if (this.url.scheme === \"file\" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {\r\n        if (this.url.host !== \"\" && this.url.host !== null) {\r\n          this.parseError = true;\r\n          this.url.host = \"\";\r\n        }\r\n        this.buffer = this.buffer[0] + \":\";\r\n      }\r\n      this.url.path.push(this.buffer);\r\n    }\r\n    this.buffer = \"\";\r\n    if (this.url.scheme === \"file\" && (c === undefined || c === 63 || c === 35)) {\r\n      while (this.url.path.length > 1 && this.url.path[0] === \"\") {\r\n        this.parseError = true;\r\n        this.url.path.shift();\r\n      }\r\n    }\r\n    if (c === 63) {\r\n      this.url.query = \"\";\r\n      this.state = \"query\";\r\n    }\r\n    if (c === 35) {\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    }\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.buffer += percentEncodeChar(c, isPathPercentEncode);\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse cannot-be-a-base-URL path\"] = function parseCannotBeABaseURLPath(c) {\r\n  if (c === 63) {\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (c === 35) {\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else {\r\n    // TODO: Add: not a URL code point\r\n    if (!isNaN(c) && c !== 37) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (c === 37 &&\r\n        (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n         !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (!isNaN(c)) {\r\n      this.url.path[0] = this.url.path[0] + percentEncodeChar(c, isC0ControlPercentEncode);\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse query\"] = function parseQuery(c, cStr) {\r\n  if (isNaN(c) || (!this.stateOverride && c === 35)) {\r\n    if (!isSpecial(this.url) || this.url.scheme === \"ws\" || this.url.scheme === \"wss\") {\r\n      this.encodingOverride = \"utf-8\";\r\n    }\r\n\r\n    const buffer = new Buffer(this.buffer); // TODO: Use encoding override instead\r\n    for (let i = 0; i < buffer.length; ++i) {\r\n      if (buffer[i] < 0x21 || buffer[i] > 0x7E || buffer[i] === 0x22 || buffer[i] === 0x23 ||\r\n          buffer[i] === 0x3C || buffer[i] === 0x3E) {\r\n        this.url.query += percentEncode(buffer[i]);\r\n      } else {\r\n        this.url.query += String.fromCodePoint(buffer[i]);\r\n      }\r\n    }\r\n\r\n    this.buffer = \"\";\r\n    if (c === 35) {\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    }\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse fragment\"] = function parseFragment(c) {\r\n  if (isNaN(c)) { // do nothing\r\n  } else if (c === 0x0) {\r\n    this.parseError = true;\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.url.fragment += percentEncodeChar(c, isC0ControlPercentEncode);\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nfunction serializeURL(url, excludeFragment) {\r\n  let output = url.scheme + \":\";\r\n  if (url.host !== null) {\r\n    output += \"//\";\r\n\r\n    if (url.username !== \"\" || url.password !== \"\") {\r\n      output += url.username;\r\n      if (url.password !== \"\") {\r\n        output += \":\" + url.password;\r\n      }\r\n      output += \"@\";\r\n    }\r\n\r\n    output += serializeHost(url.host);\r\n\r\n    if (url.port !== null) {\r\n      output += \":\" + url.port;\r\n    }\r\n  } else if (url.host === null && url.scheme === \"file\") {\r\n    output += \"//\";\r\n  }\r\n\r\n  if (url.cannotBeABaseURL) {\r\n    output += url.path[0];\r\n  } else {\r\n    for (const string of url.path) {\r\n      output += \"/\" + string;\r\n    }\r\n  }\r\n\r\n  if (url.query !== null) {\r\n    output += \"?\" + url.query;\r\n  }\r\n\r\n  if (!excludeFragment && url.fragment !== null) {\r\n    output += \"#\" + url.fragment;\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction serializeOrigin(tuple) {\r\n  let result = tuple.scheme + \"://\";\r\n  result += serializeHost(tuple.host);\r\n\r\n  if (tuple.port !== null) {\r\n    result += \":\" + tuple.port;\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\nmodule.exports.serializeURL = serializeURL;\r\n\r\nmodule.exports.serializeURLOrigin = function (url) {\r\n  // https://url.spec.whatwg.org/#concept-url-origin\r\n  switch (url.scheme) {\r\n    case \"blob\":\r\n      try {\r\n        return module.exports.serializeURLOrigin(module.exports.parseURL(url.path[0]));\r\n      } catch (e) {\r\n        // serializing an opaque origin returns \"null\"\r\n        return \"null\";\r\n      }\r\n    case \"ftp\":\r\n    case \"gopher\":\r\n    case \"http\":\r\n    case \"https\":\r\n    case \"ws\":\r\n    case \"wss\":\r\n      return serializeOrigin({\r\n        scheme: url.scheme,\r\n        host: url.host,\r\n        port: url.port\r\n      });\r\n    case \"file\":\r\n      // spec says \"exercise to the reader\", chrome says \"file://\"\r\n      return \"file://\";\r\n    default:\r\n      // serializing an opaque origin returns \"null\"\r\n      return \"null\";\r\n  }\r\n};\r\n\r\nmodule.exports.basicURLParse = function (input, options) {\r\n  if (options === undefined) {\r\n    options = {};\r\n  }\r\n\r\n  const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);\r\n  if (usm.failure) {\r\n    return \"failure\";\r\n  }\r\n\r\n  return usm.url;\r\n};\r\n\r\nmodule.exports.setTheUsername = function (url, username) {\r\n  url.username = \"\";\r\n  const decoded = punycode.ucs2.decode(username);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    url.username += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\r\n  }\r\n};\r\n\r\nmodule.exports.setThePassword = function (url, password) {\r\n  url.password = \"\";\r\n  const decoded = punycode.ucs2.decode(password);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    url.password += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\r\n  }\r\n};\r\n\r\nmodule.exports.serializeHost = serializeHost;\r\n\r\nmodule.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;\r\n\r\nmodule.exports.serializeInteger = function (integer) {\r\n  return String(integer);\r\n};\r\n\r\nmodule.exports.parseURL = function (input, options) {\r\n  if (options === undefined) {\r\n    options = {};\r\n  }\r\n\r\n  // We don't handle blobs, so this just delegates:\r\n  return module.exports.basicURLParse(input, { baseURL: options.baseURL, encodingOverride: options.encodingOverride });\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-url/lib/utils.js":
/*!**********************************************!*\
  !*** ./node_modules/whatwg-url/lib/utils.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports.mixin = function mixin(target, source) {\n  const keys = Object.getOwnPropertyNames(source);\n  for (let i = 0; i < keys.length; ++i) {\n    Object.defineProperty(target, keys[i], Object.getOwnPropertyDescriptor(source, keys[i]));\n  }\n};\n\nmodule.exports.wrapperSymbol = Symbol(\"wrapper\");\nmodule.exports.implSymbol = Symbol(\"impl\");\n\nmodule.exports.wrapperForImpl = function (impl) {\n  return impl[module.exports.wrapperSymbol];\n};\n\nmodule.exports.implForWrapper = function (wrapper) {\n  return wrapper[module.exports.implSymbol];\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsb0JBQW9CO0FBQ3BCO0FBQ0Esa0JBQWtCLGlCQUFpQjtBQUNuQztBQUNBO0FBQ0E7O0FBRUEsNEJBQTRCO0FBQzVCLHlCQUF5Qjs7QUFFekIsNkJBQTZCO0FBQzdCO0FBQ0E7O0FBRUEsNkJBQTZCO0FBQzdCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXE9wb3NJIHY3XFxub2RlX21vZHVsZXNcXHdoYXR3Zy11cmxcXGxpYlxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbm1vZHVsZS5leHBvcnRzLm1peGluID0gZnVuY3Rpb24gbWl4aW4odGFyZ2V0LCBzb3VyY2UpIHtcbiAgY29uc3Qga2V5cyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzKHNvdXJjZSk7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwga2V5cy5sZW5ndGg7ICsraSkge1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGtleXNbaV0sIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3Ioc291cmNlLCBrZXlzW2ldKSk7XG4gIH1cbn07XG5cbm1vZHVsZS5leHBvcnRzLndyYXBwZXJTeW1ib2wgPSBTeW1ib2woXCJ3cmFwcGVyXCIpO1xubW9kdWxlLmV4cG9ydHMuaW1wbFN5bWJvbCA9IFN5bWJvbChcImltcGxcIik7XG5cbm1vZHVsZS5leHBvcnRzLndyYXBwZXJGb3JJbXBsID0gZnVuY3Rpb24gKGltcGwpIHtcbiAgcmV0dXJuIGltcGxbbW9kdWxlLmV4cG9ydHMud3JhcHBlclN5bWJvbF07XG59O1xuXG5tb2R1bGUuZXhwb3J0cy5pbXBsRm9yV3JhcHBlciA9IGZ1bmN0aW9uICh3cmFwcGVyKSB7XG4gIHJldHVybiB3cmFwcGVyW21vZHVsZS5leHBvcnRzLmltcGxTeW1ib2xdO1xufTtcblxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-url/lib/utils.js\n");

/***/ })

};
;