/**
 * @param {CompileOptions | null | undefined} [options]
 * @returns {Compile}
 */
export function compile(options?: CompileOptions | null | undefined): Compile;
export type Media = {
    image?: boolean | undefined;
    labelId?: string | undefined;
    label?: string | undefined;
    referenceId?: string | undefined;
    destination?: string | undefined;
    title?: string | undefined;
};
import type { CompileOptions } from 'micromark-util-types';
import type { Compile } from 'micromark-util-types';
//# sourceMappingURL=compile.d.ts.map