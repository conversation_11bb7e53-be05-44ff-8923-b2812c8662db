"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/features/temario/services/temarioService.ts":
/*!*********************************************************!*\
  !*** ./src/features/temario/services/temarioService.ts ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarEstadoTema: () => (/* binding */ actualizarEstadoTema),\n/* harmony export */   actualizarTema: () => (/* binding */ actualizarTema),\n/* harmony export */   actualizarTemario: () => (/* binding */ actualizarTemario),\n/* harmony export */   crearTemario: () => (/* binding */ crearTemario),\n/* harmony export */   crearTemas: () => (/* binding */ crearTemas),\n/* harmony export */   eliminarTema: () => (/* binding */ eliminarTema),\n/* harmony export */   eliminarTemario: () => (/* binding */ eliminarTemario),\n/* harmony export */   obtenerEstadisticasTemario: () => (/* binding */ obtenerEstadisticasTemario),\n/* harmony export */   obtenerTemarioUsuario: () => (/* binding */ obtenerTemarioUsuario),\n/* harmony export */   obtenerTemas: () => (/* binding */ obtenerTemas),\n/* harmony export */   tieneTemarioConfigurado: () => (/* binding */ tieneTemarioConfigurado)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(app-pages-browser)/./src/features/auth/services/authService.ts\");\n\n\n/**\n * Verifica si el usuario tiene un temario configurado\n */ async function tieneTemarioConfigurado() {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.log('No hay usuario autenticado o error:', authError);\n            return false;\n        }\n        console.log('Verificando temario para usuario:', user.id);\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').select('id').eq('user_id', user.id).limit(1);\n        if (error) {\n            var _error_message, _error_message1;\n            console.error('Error al verificar temario en Supabase:', error);\n            // Si es un error de tabla no encontrada, devolver false sin error\n            if (error.code === 'PGRST116' || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('relation')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('does not exist'))) {\n                console.log('Tabla temarios no encontrada, devolviendo false');\n                return false;\n            }\n            return false;\n        }\n        const tieneTemario = data && data.length > 0;\n        console.log('Resultado verificación temario:', tieneTemario);\n        return tieneTemario;\n    } catch (error) {\n        console.error('Error general al verificar temario:', error);\n        return false;\n    }\n}\n/**\n * Obtiene el temario del usuario actual\n */ async function obtenerTemarioUsuario() {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.log('No hay usuario autenticado para obtener temario o error:', authError);\n            return null;\n        }\n        console.log('Obteniendo temario para usuario:', user.id);\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').select('*').eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener temario en Supabase:', error);\n            // Si no hay datos, es normal (usuario sin temario)\n            if (error.code === 'PGRST116') {\n                console.log('Usuario no tiene temario configurado');\n                return null;\n            }\n            return null;\n        }\n        console.log('Temario obtenido:', data);\n        return data;\n    } catch (error) {\n        console.error('Error general al obtener temario:', error);\n        return null;\n    }\n}\n/**\n * Crea un nuevo temario\n */ async function crearTemario(titulo, descripcion, tipo) {\n    try {\n        const { user, error: authError } = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            console.error('No hay usuario autenticado o error:', authError);\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').insert([\n            {\n                titulo,\n                descripcion,\n                tipo,\n                user_id: user.id\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al crear temario:', error);\n            return null;\n        }\n        return data.id;\n    } catch (error) {\n        console.error('Error al crear temario:', error);\n        return null;\n    }\n}\n/**\n * Obtiene los temas de un temario\n */ async function obtenerTemas(temarioId) {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').select('*').eq('temario_id', temarioId).order('orden', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener temas:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener temas:', error);\n        return [];\n    }\n}\n/**\n * Crea múltiples temas para un temario\n */ async function crearTemas(temarioId, temas) {\n    try {\n        const temasConTemarioId = temas.map((tema)=>({\n                ...tema,\n                temario_id: temarioId\n            }));\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').insert(temasConTemarioId);\n        if (error) {\n            console.error('Error al crear temas:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al crear temas:', error);\n        return false;\n    }\n}\n/**\n * Actualiza los datos básicos de un temario\n */ async function actualizarTemario(temarioId, titulo, descripcion) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').update({\n            titulo,\n            descripcion,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', temarioId);\n        if (error) {\n            console.error('Error al actualizar temario:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar temario:', error);\n        return false;\n    }\n}\n/**\n * Actualiza los datos de un tema\n */ async function actualizarTema(temaId, titulo, descripcion) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').update({\n            titulo,\n            descripcion,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', temaId);\n        if (error) {\n            console.error('Error al actualizar tema:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar tema:', error);\n        return false;\n    }\n}\n/**\n * Elimina un tema\n */ async function eliminarTema(temaId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').delete().eq('id', temaId);\n        if (error) {\n            console.error('Error al eliminar tema:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar tema:', error);\n        return false;\n    }\n}\n/**\n * Actualiza el estado de completado de un tema\n */ async function actualizarEstadoTema(temaId, completado) {\n    try {\n        const updateData = {\n            completado,\n            actualizado_en: new Date().toISOString()\n        };\n        if (completado) {\n            updateData.fecha_completado = new Date().toISOString();\n        } else {\n            updateData.fecha_completado = null;\n        }\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').update(updateData).eq('id', temaId);\n        if (error) {\n            console.error('Error al actualizar estado del tema:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar estado del tema:', error);\n        return false;\n    }\n}\n/**\n * Obtiene estadísticas del temario\n */ async function obtenerEstadisticasTemario(temarioId) {\n    try {\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temas').select('completado').eq('temario_id', temarioId);\n        if (error) {\n            console.error('Error al obtener estadísticas del temario:', error);\n            return null;\n        }\n        const totalTemas = data.length;\n        const temasCompletados = data.filter((tema)=>tema.completado).length;\n        const porcentajeCompletado = totalTemas > 0 ? temasCompletados / totalTemas * 100 : 0;\n        return {\n            totalTemas,\n            temasCompletados,\n            porcentajeCompletado\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas del temario:', error);\n        return null;\n    }\n}\n/**\n * Elimina un temario y todos sus temas asociados\n */ async function eliminarTemario(temarioId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('temarios').delete().eq('id', temarioId);\n        if (error) {\n            console.error('Error al eliminar temario:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar temario:', error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/temario/services/temarioService.ts\n"));

/***/ })

});