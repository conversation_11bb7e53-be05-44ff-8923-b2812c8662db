import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Plus, FiTrash2 } from 'react-icons/fi';
import { crearTemario, crearTemas } from '../services/temarioService';
import { toast } from 'react-hot-toast';

interface TemarioSetupProps {
  onComplete: () => void;
}

interface TemaInput {
  numero: number;
  titulo: string;
  descripcion: string;
}

const TemarioSetup: React.FC<TemarioSetupProps> = ({ onComplete }) => {
  const [paso, setPaso] = useState<'seleccion' | 'configuracion'>('seleccion');
  const [tipoSeleccionado, setTipoSeleccionado] = useState<'completo' | 'temas_sueltos' | null>(null);
  const [tituloTemario, setTituloTemario] = useState('');
  const [descripcionTemario, setDescripcionTemario] = useState('');
  const [temas, setTemas] = useState<TemaInput[]>([
    { numero: 1, titulo: '', descripcion: '' }
  ]);
  const [isLoading, setIsLoading] = useState(false);

  const handleTipoSeleccion = (tipo: 'completo' | 'temas_sueltos') => {
    setTipoSeleccionado(tipo);
    setPaso('configuracion');
  };

  const agregarTema = () => {
    const nuevoNumero = temas.length + 1;
    setTemas([...temas, { numero: nuevoNumero, titulo: '', descripcion: '' }]);
  };

  const eliminarTema = (index: number) => {
    if (temas.length > 1) {
      const nuevosTemasTemp = temas.filter((_, i) => i !== index);
      // Reordenar números
      const nuevosTemasReordenados = nuevosTemasTemp.map((tema, i) => ({
        ...tema,
        numero: i + 1
      }));
      setTemas(nuevosTemasReordenados);
    }
  };

  const actualizarTema = (index: number, campo: keyof TemaInput, valor: string | number) => {
    const nuevosTemasTemp = [...temas];
    nuevosTemasTemp[index] = { ...nuevosTemasTemp[index], [campo]: valor };
    setTemas(nuevosTemasTemp);
  };

  const validarFormulario = (): boolean => {
    if (!tituloTemario.trim()) {
      toast.error('El título del temario es obligatorio');
      return false;
    }

    if (temas.some(tema => !tema.titulo.trim())) {
      toast.error('Todos los temas deben tener un título');
      return false;
    }

    return true;
  };

  const handleGuardar = async () => {
    if (!validarFormulario() || !tipoSeleccionado) return;

    setIsLoading(true);
    try {
      // Crear el temario
      const temarioId = await crearTemario(tituloTemario, descripcionTemario, tipoSeleccionado);
      
      if (!temarioId) {
        toast.error('Error al crear el temario');
        return;
      }

      // Crear los temas
      const temasParaCrear = temas.map((tema, index) => ({
        numero: tema.numero,
        titulo: tema.titulo,
        descripcion: tema.descripcion,
        orden: index + 1
      }));

      const temasCreados = await crearTemas(temarioId, temasParaCrear);
      
      if (!temasCreados) {
        toast.error('Error al crear los temas');
        return;
      }

      toast.success('¡Temario configurado exitosamente!');
      onComplete();
    } catch (error) {
      console.error('Error al guardar temario:', error);
      toast.error('Error al configurar el temario');
    } finally {
      setIsLoading(false);
    }
  };

  if (paso === 'seleccion') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-4xl w-full">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              ¡Bienvenido a OposiAI! 🎉
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              Para comenzar, necesitamos configurar tu temario de estudio.
            </p>
            <p className="text-gray-500">
              Esto nos permitirá crear una planificación personalizada y hacer un seguimiento de tu progreso.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {/* Opción: Temario Completo */}
            <div 
              className="bg-white rounded-xl p-6 shadow-sm border-2 border-gray-200 hover:border-blue-500 cursor-pointer transition-all duration-200 hover:shadow-md"
              onClick={() => handleTipoSeleccion('completo')}
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FiBook className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  Temario Completo
                </h3>
                <p className="text-gray-600 mb-4">
                  Configura todos los temas de tu oposición de forma estructurada.
                </p>
                <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                  <div className="flex items-center text-green-800 text-sm">
                    <FiCheck className="w-4 h-4 mr-2 flex-shrink-0" />
                    <span>La IA podrá crear una planificación completa y personalizada</span>
                  </div>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <div className="flex items-center text-green-800 text-sm">
                    <FiCheck className="w-4 h-4 mr-2 flex-shrink-0" />
                    <span>Seguimiento detallado del progreso por temas</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Opción: Temas Sueltos */}
            <div 
              className="bg-white rounded-xl p-6 shadow-sm border-2 border-gray-200 hover:border-orange-500 cursor-pointer transition-all duration-200 hover:shadow-md"
              onClick={() => handleTipoSeleccion('temas_sueltos')}
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FiList className="w-8 h-8 text-orange-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  Temas Sueltos
                </h3>
                <p className="text-gray-600 mb-4">
                  Añade solo los temas específicos que quieres estudiar.
                </p>
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                  <div className="flex items-start text-orange-800 text-sm">
                    <FiAlertTriangle className="w-4 h-4 mr-2 flex-shrink-0 mt-0.5" />
                    <span>La IA no podrá realizar una planificación completa</span>
                  </div>
                </div>
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                  <div className="flex items-start text-orange-800 text-sm">
                    <FiAlertTriangle className="w-4 h-4 mr-2 flex-shrink-0 mt-0.5" />
                    <span>El seguimiento de progreso será limitado</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-8">
            <p className="text-sm text-gray-500">
              Podrás modificar tu temario más adelante desde la configuración
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="mb-6">
            <button
              onClick={() => setPaso('seleccion')}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium mb-4"
            >
              ← Volver a la selección
            </button>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Configurar {tipoSeleccionado === 'completo' ? 'Temario Completo' : 'Temas Sueltos'}
            </h2>
            {tipoSeleccionado === 'temas_sueltos' && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <FiAlertTriangle className="w-5 h-5 text-orange-600 mr-3 flex-shrink-0 mt-0.5" />
                  <div className="text-orange-800 text-sm">
                    <p className="font-medium mb-1">Limitaciones con temas sueltos:</p>
                    <ul className="list-disc list-inside space-y-1">
                      <li>La IA no podrá crear una planificación temporal completa</li>
                      <li>El seguimiento de progreso será básico</li>
                      <li>No habrá recomendaciones de orden de estudio</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Información del temario */}
          <div className="space-y-4 mb-6">
            <div>
              <label htmlFor="titulo" className="block text-sm font-medium text-gray-700 mb-1">
                Título del temario *
              </label>
              <input
                type="text"
                id="titulo"
                value={tituloTemario}
                onChange={(e) => setTituloTemario(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ej: Oposiciones Auxiliar Administrativo 2024"
              />
            </div>
            <div>
              <label htmlFor="descripcion" className="block text-sm font-medium text-gray-700 mb-1">
                Descripción (opcional)
              </label>
              <textarea
                id="descripcion"
                value={descripcionTemario}
                onChange={(e) => setDescripcionTemario(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe brevemente tu temario..."
              />
            </div>
          </div>

          {/* Lista de temas */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Temas</h3>
              <button
                onClick={agregarTema}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm flex items-center"
              >
                <FiPlus className="w-4 h-4 mr-1" />
                Añadir tema
              </button>
            </div>

            <div className="space-y-3">
              {temas.map((tema, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg">
                  <div className="w-16">
                    <label className="block text-xs text-gray-500 mb-1">Tema</label>
                    <input
                      type="number"
                      value={tema.numero}
                      onChange={(e) => actualizarTema(index, 'numero', parseInt(e.target.value) || 1)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      min="1"
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-xs text-gray-500 mb-1">Título *</label>
                    <input
                      type="text"
                      value={tema.titulo}
                      onChange={(e) => actualizarTema(index, 'titulo', e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="Título del tema"
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-xs text-gray-500 mb-1">Descripción</label>
                    <input
                      type="text"
                      value={tema.descripcion}
                      onChange={(e) => actualizarTema(index, 'descripcion', e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="Descripción opcional"
                    />
                  </div>
                  {temas.length > 1 && (
                    <button
                      onClick={() => eliminarTema(index)}
                      className="text-red-600 hover:text-red-700 p-1"
                      title="Eliminar tema"
                    >
                      <FiTrash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Botones de acción */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setPaso('seleccion')}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              disabled={isLoading}
            >
              Cancelar
            </button>
            <button
              onClick={handleGuardar}
              disabled={isLoading}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Guardando...
                </>
              ) : (
                'Guardar temario'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemarioSetup;
