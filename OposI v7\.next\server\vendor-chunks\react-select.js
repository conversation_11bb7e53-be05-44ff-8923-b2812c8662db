"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-select";
exports.ids = ["vendor-chunks/react-select"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-select/dist/Select-aab027f3.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-select/dist/Select-aab027f3.esm.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S: () => (/* binding */ Select),\n/* harmony export */   a: () => (/* binding */ defaultProps),\n/* harmony export */   b: () => (/* binding */ getOptionLabel$1),\n/* harmony export */   c: () => (/* binding */ createFilter),\n/* harmony export */   d: () => (/* binding */ defaultTheme),\n/* harmony export */   g: () => (/* binding */ getOptionValue$1),\n/* harmony export */   m: () => (/* binding */ mergeStyles)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./index-641ee5b8.esm.js */ \"(ssr)/./node_modules/react-select/dist/index-641ee5b8.esm.js\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @emotion/react */ \"(ssr)/./node_modules/@emotion/react/dist/emotion-react.development.esm.js\");\n/* harmony import */ var memoize_one__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! memoize-one */ \"(ssr)/./node_modules/memoize-one/dist/memoize-one.esm.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction _EMOTION_STRINGIFIED_CSS_ERROR__$2() { return \"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).\"; }\n\n// Assistive text to describe visual elements. Hidden for sighted users.\nvar _ref =  false ? 0 : {\n  name: \"1f43avz-a11yText-A11yText\",\n  styles: \"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap;label:A11yText;\",\n  map: \"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkExMXlUZXh0LnRzeCJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFPSSIsImZpbGUiOiJBMTF5VGV4dC50c3giLCJzb3VyY2VzQ29udGVudCI6WyIvKiogQGpzeCBqc3ggKi9cbmltcG9ydCB7IEpTWCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGpzeCB9IGZyb20gJ0BlbW90aW9uL3JlYWN0JztcblxuLy8gQXNzaXN0aXZlIHRleHQgdG8gZGVzY3JpYmUgdmlzdWFsIGVsZW1lbnRzLiBIaWRkZW4gZm9yIHNpZ2h0ZWQgdXNlcnMuXG5jb25zdCBBMTF5VGV4dCA9IChwcm9wczogSlNYLkludHJpbnNpY0VsZW1lbnRzWydzcGFuJ10pID0+IChcbiAgPHNwYW5cbiAgICBjc3M9e3tcbiAgICAgIGxhYmVsOiAnYTExeVRleHQnLFxuICAgICAgekluZGV4OiA5OTk5LFxuICAgICAgYm9yZGVyOiAwLFxuICAgICAgY2xpcDogJ3JlY3QoMXB4LCAxcHgsIDFweCwgMXB4KScsXG4gICAgICBoZWlnaHQ6IDEsXG4gICAgICB3aWR0aDogMSxcbiAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nLFxuICAgICAgcGFkZGluZzogMCxcbiAgICAgIHdoaXRlU3BhY2U6ICdub3dyYXAnLFxuICAgIH19XG4gICAgey4uLnByb3BzfVxuICAvPlxuKTtcblxuZXhwb3J0IGRlZmF1bHQgQTExeVRleHQ7XG4iXX0= */\",\n  toString: _EMOTION_STRINGIFIED_CSS_ERROR__$2\n};\nvar A11yText = function A11yText(props) {\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"span\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    css: _ref\n  }, props));\n};\nvar A11yText$1 = A11yText;\n\nvar defaultAriaLiveMessages = {\n  guidance: function guidance(props) {\n    var isSearchable = props.isSearchable,\n      isMulti = props.isMulti,\n      tabSelectsValue = props.tabSelectsValue,\n      context = props.context,\n      isInitialFocus = props.isInitialFocus;\n    switch (context) {\n      case 'menu':\n        return \"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu\".concat(tabSelectsValue ? ', press Tab to select the option and exit the menu' : '', \".\");\n      case 'input':\n        return isInitialFocus ? \"\".concat(props['aria-label'] || 'Select', \" is focused \").concat(isSearchable ? ',type to refine list' : '', \", press Down to open the menu, \").concat(isMulti ? ' press left to focus selected values' : '') : '';\n      case 'value':\n        return 'Use left and right to toggle between focused values, press Backspace to remove the currently focused value';\n      default:\n        return '';\n    }\n  },\n  onChange: function onChange(props) {\n    var action = props.action,\n      _props$label = props.label,\n      label = _props$label === void 0 ? '' : _props$label,\n      labels = props.labels,\n      isDisabled = props.isDisabled;\n    switch (action) {\n      case 'deselect-option':\n      case 'pop-value':\n      case 'remove-value':\n        return \"option \".concat(label, \", deselected.\");\n      case 'clear':\n        return 'All selected options have been cleared.';\n      case 'initial-input-focus':\n        return \"option\".concat(labels.length > 1 ? 's' : '', \" \").concat(labels.join(','), \", selected.\");\n      case 'select-option':\n        return isDisabled ? \"option \".concat(label, \" is disabled. Select another option.\") : \"option \".concat(label, \", selected.\");\n      default:\n        return '';\n    }\n  },\n  onFocus: function onFocus(props) {\n    var context = props.context,\n      focused = props.focused,\n      options = props.options,\n      _props$label2 = props.label,\n      label = _props$label2 === void 0 ? '' : _props$label2,\n      selectValue = props.selectValue,\n      isDisabled = props.isDisabled,\n      isSelected = props.isSelected,\n      isAppleDevice = props.isAppleDevice;\n    var getArrayIndex = function getArrayIndex(arr, item) {\n      return arr && arr.length ? \"\".concat(arr.indexOf(item) + 1, \" of \").concat(arr.length) : '';\n    };\n    if (context === 'value' && selectValue) {\n      return \"value \".concat(label, \" focused, \").concat(getArrayIndex(selectValue, focused), \".\");\n    }\n    if (context === 'menu' && isAppleDevice) {\n      var disabled = isDisabled ? ' disabled' : '';\n      var status = \"\".concat(isSelected ? ' selected' : '').concat(disabled);\n      return \"\".concat(label).concat(status, \", \").concat(getArrayIndex(options, focused), \".\");\n    }\n    return '';\n  },\n  onFilter: function onFilter(props) {\n    var inputValue = props.inputValue,\n      resultsMessage = props.resultsMessage;\n    return \"\".concat(resultsMessage).concat(inputValue ? ' for search term ' + inputValue : '', \".\");\n  }\n};\n\nvar LiveRegion = function LiveRegion(props) {\n  var ariaSelection = props.ariaSelection,\n    focusedOption = props.focusedOption,\n    focusedValue = props.focusedValue,\n    focusableOptions = props.focusableOptions,\n    isFocused = props.isFocused,\n    selectValue = props.selectValue,\n    selectProps = props.selectProps,\n    id = props.id,\n    isAppleDevice = props.isAppleDevice;\n  var ariaLiveMessages = selectProps.ariaLiveMessages,\n    getOptionLabel = selectProps.getOptionLabel,\n    inputValue = selectProps.inputValue,\n    isMulti = selectProps.isMulti,\n    isOptionDisabled = selectProps.isOptionDisabled,\n    isSearchable = selectProps.isSearchable,\n    menuIsOpen = selectProps.menuIsOpen,\n    options = selectProps.options,\n    screenReaderStatus = selectProps.screenReaderStatus,\n    tabSelectsValue = selectProps.tabSelectsValue,\n    isLoading = selectProps.isLoading;\n  var ariaLabel = selectProps['aria-label'];\n  var ariaLive = selectProps['aria-live'];\n\n  // Update aria live message configuration when prop changes\n  var messages = (0,react__WEBPACK_IMPORTED_MODULE_7__.useMemo)(function () {\n    return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, defaultAriaLiveMessages), ariaLiveMessages || {});\n  }, [ariaLiveMessages]);\n\n  // Update aria live selected option when prop changes\n  var ariaSelected = (0,react__WEBPACK_IMPORTED_MODULE_7__.useMemo)(function () {\n    var message = '';\n    if (ariaSelection && messages.onChange) {\n      var option = ariaSelection.option,\n        selectedOptions = ariaSelection.options,\n        removedValue = ariaSelection.removedValue,\n        removedValues = ariaSelection.removedValues,\n        value = ariaSelection.value;\n      // select-option when !isMulti does not return option so we assume selected option is value\n      var asOption = function asOption(val) {\n        return !Array.isArray(val) ? val : null;\n      };\n\n      // If there is just one item from the action then get its label\n      var selected = removedValue || option || asOption(value);\n      var label = selected ? getOptionLabel(selected) : '';\n\n      // If there are multiple items from the action then return an array of labels\n      var multiSelected = selectedOptions || removedValues || undefined;\n      var labels = multiSelected ? multiSelected.map(getOptionLabel) : [];\n      var onChangeProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        // multiSelected items are usually items that have already been selected\n        // or set by the user as a default value so we assume they are not disabled\n        isDisabled: selected && isOptionDisabled(selected, selectValue),\n        label: label,\n        labels: labels\n      }, ariaSelection);\n      message = messages.onChange(onChangeProps);\n    }\n    return message;\n  }, [ariaSelection, messages, isOptionDisabled, selectValue, getOptionLabel]);\n  var ariaFocused = (0,react__WEBPACK_IMPORTED_MODULE_7__.useMemo)(function () {\n    var focusMsg = '';\n    var focused = focusedOption || focusedValue;\n    var isSelected = !!(focusedOption && selectValue && selectValue.includes(focusedOption));\n    if (focused && messages.onFocus) {\n      var onFocusProps = {\n        focused: focused,\n        label: getOptionLabel(focused),\n        isDisabled: isOptionDisabled(focused, selectValue),\n        isSelected: isSelected,\n        options: focusableOptions,\n        context: focused === focusedOption ? 'menu' : 'value',\n        selectValue: selectValue,\n        isAppleDevice: isAppleDevice\n      };\n      focusMsg = messages.onFocus(onFocusProps);\n    }\n    return focusMsg;\n  }, [focusedOption, focusedValue, getOptionLabel, isOptionDisabled, messages, focusableOptions, selectValue, isAppleDevice]);\n  var ariaResults = (0,react__WEBPACK_IMPORTED_MODULE_7__.useMemo)(function () {\n    var resultsMsg = '';\n    if (menuIsOpen && options.length && !isLoading && messages.onFilter) {\n      var resultsMessage = screenReaderStatus({\n        count: focusableOptions.length\n      });\n      resultsMsg = messages.onFilter({\n        inputValue: inputValue,\n        resultsMessage: resultsMessage\n      });\n    }\n    return resultsMsg;\n  }, [focusableOptions, inputValue, menuIsOpen, messages, options, screenReaderStatus, isLoading]);\n  var isInitialFocus = (ariaSelection === null || ariaSelection === void 0 ? void 0 : ariaSelection.action) === 'initial-input-focus';\n  var ariaGuidance = (0,react__WEBPACK_IMPORTED_MODULE_7__.useMemo)(function () {\n    var guidanceMsg = '';\n    if (messages.guidance) {\n      var context = focusedValue ? 'value' : menuIsOpen ? 'menu' : 'input';\n      guidanceMsg = messages.guidance({\n        'aria-label': ariaLabel,\n        context: context,\n        isDisabled: focusedOption && isOptionDisabled(focusedOption, selectValue),\n        isMulti: isMulti,\n        isSearchable: isSearchable,\n        tabSelectsValue: tabSelectsValue,\n        isInitialFocus: isInitialFocus\n      });\n    }\n    return guidanceMsg;\n  }, [ariaLabel, focusedOption, focusedValue, isMulti, isOptionDisabled, isSearchable, menuIsOpen, messages, selectValue, tabSelectsValue, isInitialFocus]);\n  var ScreenReaderText = (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(react__WEBPACK_IMPORTED_MODULE_7__.Fragment, null, (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"span\", {\n    id: \"aria-selection\"\n  }, ariaSelected), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"span\", {\n    id: \"aria-focused\"\n  }, ariaFocused), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"span\", {\n    id: \"aria-results\"\n  }, ariaResults), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"span\", {\n    id: \"aria-guidance\"\n  }, ariaGuidance));\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(react__WEBPACK_IMPORTED_MODULE_7__.Fragment, null, (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(A11yText$1, {\n    id: id\n  }, isInitialFocus && ScreenReaderText), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(A11yText$1, {\n    \"aria-live\": ariaLive,\n    \"aria-atomic\": \"false\",\n    \"aria-relevant\": \"additions text\",\n    role: \"log\"\n  }, isFocused && !isInitialFocus && ScreenReaderText));\n};\nvar LiveRegion$1 = LiveRegion;\n\nvar diacritics = [{\n  base: 'A',\n  letters: \"A\\u24B6\\uFF21\\xC0\\xC1\\xC2\\u1EA6\\u1EA4\\u1EAA\\u1EA8\\xC3\\u0100\\u0102\\u1EB0\\u1EAE\\u1EB4\\u1EB2\\u0226\\u01E0\\xC4\\u01DE\\u1EA2\\xC5\\u01FA\\u01CD\\u0200\\u0202\\u1EA0\\u1EAC\\u1EB6\\u1E00\\u0104\\u023A\\u2C6F\"\n}, {\n  base: 'AA',\n  letters: \"\\uA732\"\n}, {\n  base: 'AE',\n  letters: \"\\xC6\\u01FC\\u01E2\"\n}, {\n  base: 'AO',\n  letters: \"\\uA734\"\n}, {\n  base: 'AU',\n  letters: \"\\uA736\"\n}, {\n  base: 'AV',\n  letters: \"\\uA738\\uA73A\"\n}, {\n  base: 'AY',\n  letters: \"\\uA73C\"\n}, {\n  base: 'B',\n  letters: \"B\\u24B7\\uFF22\\u1E02\\u1E04\\u1E06\\u0243\\u0182\\u0181\"\n}, {\n  base: 'C',\n  letters: \"C\\u24B8\\uFF23\\u0106\\u0108\\u010A\\u010C\\xC7\\u1E08\\u0187\\u023B\\uA73E\"\n}, {\n  base: 'D',\n  letters: \"D\\u24B9\\uFF24\\u1E0A\\u010E\\u1E0C\\u1E10\\u1E12\\u1E0E\\u0110\\u018B\\u018A\\u0189\\uA779\"\n}, {\n  base: 'DZ',\n  letters: \"\\u01F1\\u01C4\"\n}, {\n  base: 'Dz',\n  letters: \"\\u01F2\\u01C5\"\n}, {\n  base: 'E',\n  letters: \"E\\u24BA\\uFF25\\xC8\\xC9\\xCA\\u1EC0\\u1EBE\\u1EC4\\u1EC2\\u1EBC\\u0112\\u1E14\\u1E16\\u0114\\u0116\\xCB\\u1EBA\\u011A\\u0204\\u0206\\u1EB8\\u1EC6\\u0228\\u1E1C\\u0118\\u1E18\\u1E1A\\u0190\\u018E\"\n}, {\n  base: 'F',\n  letters: \"F\\u24BB\\uFF26\\u1E1E\\u0191\\uA77B\"\n}, {\n  base: 'G',\n  letters: \"G\\u24BC\\uFF27\\u01F4\\u011C\\u1E20\\u011E\\u0120\\u01E6\\u0122\\u01E4\\u0193\\uA7A0\\uA77D\\uA77E\"\n}, {\n  base: 'H',\n  letters: \"H\\u24BD\\uFF28\\u0124\\u1E22\\u1E26\\u021E\\u1E24\\u1E28\\u1E2A\\u0126\\u2C67\\u2C75\\uA78D\"\n}, {\n  base: 'I',\n  letters: \"I\\u24BE\\uFF29\\xCC\\xCD\\xCE\\u0128\\u012A\\u012C\\u0130\\xCF\\u1E2E\\u1EC8\\u01CF\\u0208\\u020A\\u1ECA\\u012E\\u1E2C\\u0197\"\n}, {\n  base: 'J',\n  letters: \"J\\u24BF\\uFF2A\\u0134\\u0248\"\n}, {\n  base: 'K',\n  letters: \"K\\u24C0\\uFF2B\\u1E30\\u01E8\\u1E32\\u0136\\u1E34\\u0198\\u2C69\\uA740\\uA742\\uA744\\uA7A2\"\n}, {\n  base: 'L',\n  letters: \"L\\u24C1\\uFF2C\\u013F\\u0139\\u013D\\u1E36\\u1E38\\u013B\\u1E3C\\u1E3A\\u0141\\u023D\\u2C62\\u2C60\\uA748\\uA746\\uA780\"\n}, {\n  base: 'LJ',\n  letters: \"\\u01C7\"\n}, {\n  base: 'Lj',\n  letters: \"\\u01C8\"\n}, {\n  base: 'M',\n  letters: \"M\\u24C2\\uFF2D\\u1E3E\\u1E40\\u1E42\\u2C6E\\u019C\"\n}, {\n  base: 'N',\n  letters: \"N\\u24C3\\uFF2E\\u01F8\\u0143\\xD1\\u1E44\\u0147\\u1E46\\u0145\\u1E4A\\u1E48\\u0220\\u019D\\uA790\\uA7A4\"\n}, {\n  base: 'NJ',\n  letters: \"\\u01CA\"\n}, {\n  base: 'Nj',\n  letters: \"\\u01CB\"\n}, {\n  base: 'O',\n  letters: \"O\\u24C4\\uFF2F\\xD2\\xD3\\xD4\\u1ED2\\u1ED0\\u1ED6\\u1ED4\\xD5\\u1E4C\\u022C\\u1E4E\\u014C\\u1E50\\u1E52\\u014E\\u022E\\u0230\\xD6\\u022A\\u1ECE\\u0150\\u01D1\\u020C\\u020E\\u01A0\\u1EDC\\u1EDA\\u1EE0\\u1EDE\\u1EE2\\u1ECC\\u1ED8\\u01EA\\u01EC\\xD8\\u01FE\\u0186\\u019F\\uA74A\\uA74C\"\n}, {\n  base: 'OI',\n  letters: \"\\u01A2\"\n}, {\n  base: 'OO',\n  letters: \"\\uA74E\"\n}, {\n  base: 'OU',\n  letters: \"\\u0222\"\n}, {\n  base: 'P',\n  letters: \"P\\u24C5\\uFF30\\u1E54\\u1E56\\u01A4\\u2C63\\uA750\\uA752\\uA754\"\n}, {\n  base: 'Q',\n  letters: \"Q\\u24C6\\uFF31\\uA756\\uA758\\u024A\"\n}, {\n  base: 'R',\n  letters: \"R\\u24C7\\uFF32\\u0154\\u1E58\\u0158\\u0210\\u0212\\u1E5A\\u1E5C\\u0156\\u1E5E\\u024C\\u2C64\\uA75A\\uA7A6\\uA782\"\n}, {\n  base: 'S',\n  letters: \"S\\u24C8\\uFF33\\u1E9E\\u015A\\u1E64\\u015C\\u1E60\\u0160\\u1E66\\u1E62\\u1E68\\u0218\\u015E\\u2C7E\\uA7A8\\uA784\"\n}, {\n  base: 'T',\n  letters: \"T\\u24C9\\uFF34\\u1E6A\\u0164\\u1E6C\\u021A\\u0162\\u1E70\\u1E6E\\u0166\\u01AC\\u01AE\\u023E\\uA786\"\n}, {\n  base: 'TZ',\n  letters: \"\\uA728\"\n}, {\n  base: 'U',\n  letters: \"U\\u24CA\\uFF35\\xD9\\xDA\\xDB\\u0168\\u1E78\\u016A\\u1E7A\\u016C\\xDC\\u01DB\\u01D7\\u01D5\\u01D9\\u1EE6\\u016E\\u0170\\u01D3\\u0214\\u0216\\u01AF\\u1EEA\\u1EE8\\u1EEE\\u1EEC\\u1EF0\\u1EE4\\u1E72\\u0172\\u1E76\\u1E74\\u0244\"\n}, {\n  base: 'V',\n  letters: \"V\\u24CB\\uFF36\\u1E7C\\u1E7E\\u01B2\\uA75E\\u0245\"\n}, {\n  base: 'VY',\n  letters: \"\\uA760\"\n}, {\n  base: 'W',\n  letters: \"W\\u24CC\\uFF37\\u1E80\\u1E82\\u0174\\u1E86\\u1E84\\u1E88\\u2C72\"\n}, {\n  base: 'X',\n  letters: \"X\\u24CD\\uFF38\\u1E8A\\u1E8C\"\n}, {\n  base: 'Y',\n  letters: \"Y\\u24CE\\uFF39\\u1EF2\\xDD\\u0176\\u1EF8\\u0232\\u1E8E\\u0178\\u1EF6\\u1EF4\\u01B3\\u024E\\u1EFE\"\n}, {\n  base: 'Z',\n  letters: \"Z\\u24CF\\uFF3A\\u0179\\u1E90\\u017B\\u017D\\u1E92\\u1E94\\u01B5\\u0224\\u2C7F\\u2C6B\\uA762\"\n}, {\n  base: 'a',\n  letters: \"a\\u24D0\\uFF41\\u1E9A\\xE0\\xE1\\xE2\\u1EA7\\u1EA5\\u1EAB\\u1EA9\\xE3\\u0101\\u0103\\u1EB1\\u1EAF\\u1EB5\\u1EB3\\u0227\\u01E1\\xE4\\u01DF\\u1EA3\\xE5\\u01FB\\u01CE\\u0201\\u0203\\u1EA1\\u1EAD\\u1EB7\\u1E01\\u0105\\u2C65\\u0250\"\n}, {\n  base: 'aa',\n  letters: \"\\uA733\"\n}, {\n  base: 'ae',\n  letters: \"\\xE6\\u01FD\\u01E3\"\n}, {\n  base: 'ao',\n  letters: \"\\uA735\"\n}, {\n  base: 'au',\n  letters: \"\\uA737\"\n}, {\n  base: 'av',\n  letters: \"\\uA739\\uA73B\"\n}, {\n  base: 'ay',\n  letters: \"\\uA73D\"\n}, {\n  base: 'b',\n  letters: \"b\\u24D1\\uFF42\\u1E03\\u1E05\\u1E07\\u0180\\u0183\\u0253\"\n}, {\n  base: 'c',\n  letters: \"c\\u24D2\\uFF43\\u0107\\u0109\\u010B\\u010D\\xE7\\u1E09\\u0188\\u023C\\uA73F\\u2184\"\n}, {\n  base: 'd',\n  letters: \"d\\u24D3\\uFF44\\u1E0B\\u010F\\u1E0D\\u1E11\\u1E13\\u1E0F\\u0111\\u018C\\u0256\\u0257\\uA77A\"\n}, {\n  base: 'dz',\n  letters: \"\\u01F3\\u01C6\"\n}, {\n  base: 'e',\n  letters: \"e\\u24D4\\uFF45\\xE8\\xE9\\xEA\\u1EC1\\u1EBF\\u1EC5\\u1EC3\\u1EBD\\u0113\\u1E15\\u1E17\\u0115\\u0117\\xEB\\u1EBB\\u011B\\u0205\\u0207\\u1EB9\\u1EC7\\u0229\\u1E1D\\u0119\\u1E19\\u1E1B\\u0247\\u025B\\u01DD\"\n}, {\n  base: 'f',\n  letters: \"f\\u24D5\\uFF46\\u1E1F\\u0192\\uA77C\"\n}, {\n  base: 'g',\n  letters: \"g\\u24D6\\uFF47\\u01F5\\u011D\\u1E21\\u011F\\u0121\\u01E7\\u0123\\u01E5\\u0260\\uA7A1\\u1D79\\uA77F\"\n}, {\n  base: 'h',\n  letters: \"h\\u24D7\\uFF48\\u0125\\u1E23\\u1E27\\u021F\\u1E25\\u1E29\\u1E2B\\u1E96\\u0127\\u2C68\\u2C76\\u0265\"\n}, {\n  base: 'hv',\n  letters: \"\\u0195\"\n}, {\n  base: 'i',\n  letters: \"i\\u24D8\\uFF49\\xEC\\xED\\xEE\\u0129\\u012B\\u012D\\xEF\\u1E2F\\u1EC9\\u01D0\\u0209\\u020B\\u1ECB\\u012F\\u1E2D\\u0268\\u0131\"\n}, {\n  base: 'j',\n  letters: \"j\\u24D9\\uFF4A\\u0135\\u01F0\\u0249\"\n}, {\n  base: 'k',\n  letters: \"k\\u24DA\\uFF4B\\u1E31\\u01E9\\u1E33\\u0137\\u1E35\\u0199\\u2C6A\\uA741\\uA743\\uA745\\uA7A3\"\n}, {\n  base: 'l',\n  letters: \"l\\u24DB\\uFF4C\\u0140\\u013A\\u013E\\u1E37\\u1E39\\u013C\\u1E3D\\u1E3B\\u017F\\u0142\\u019A\\u026B\\u2C61\\uA749\\uA781\\uA747\"\n}, {\n  base: 'lj',\n  letters: \"\\u01C9\"\n}, {\n  base: 'm',\n  letters: \"m\\u24DC\\uFF4D\\u1E3F\\u1E41\\u1E43\\u0271\\u026F\"\n}, {\n  base: 'n',\n  letters: \"n\\u24DD\\uFF4E\\u01F9\\u0144\\xF1\\u1E45\\u0148\\u1E47\\u0146\\u1E4B\\u1E49\\u019E\\u0272\\u0149\\uA791\\uA7A5\"\n}, {\n  base: 'nj',\n  letters: \"\\u01CC\"\n}, {\n  base: 'o',\n  letters: \"o\\u24DE\\uFF4F\\xF2\\xF3\\xF4\\u1ED3\\u1ED1\\u1ED7\\u1ED5\\xF5\\u1E4D\\u022D\\u1E4F\\u014D\\u1E51\\u1E53\\u014F\\u022F\\u0231\\xF6\\u022B\\u1ECF\\u0151\\u01D2\\u020D\\u020F\\u01A1\\u1EDD\\u1EDB\\u1EE1\\u1EDF\\u1EE3\\u1ECD\\u1ED9\\u01EB\\u01ED\\xF8\\u01FF\\u0254\\uA74B\\uA74D\\u0275\"\n}, {\n  base: 'oi',\n  letters: \"\\u01A3\"\n}, {\n  base: 'ou',\n  letters: \"\\u0223\"\n}, {\n  base: 'oo',\n  letters: \"\\uA74F\"\n}, {\n  base: 'p',\n  letters: \"p\\u24DF\\uFF50\\u1E55\\u1E57\\u01A5\\u1D7D\\uA751\\uA753\\uA755\"\n}, {\n  base: 'q',\n  letters: \"q\\u24E0\\uFF51\\u024B\\uA757\\uA759\"\n}, {\n  base: 'r',\n  letters: \"r\\u24E1\\uFF52\\u0155\\u1E59\\u0159\\u0211\\u0213\\u1E5B\\u1E5D\\u0157\\u1E5F\\u024D\\u027D\\uA75B\\uA7A7\\uA783\"\n}, {\n  base: 's',\n  letters: \"s\\u24E2\\uFF53\\xDF\\u015B\\u1E65\\u015D\\u1E61\\u0161\\u1E67\\u1E63\\u1E69\\u0219\\u015F\\u023F\\uA7A9\\uA785\\u1E9B\"\n}, {\n  base: 't',\n  letters: \"t\\u24E3\\uFF54\\u1E6B\\u1E97\\u0165\\u1E6D\\u021B\\u0163\\u1E71\\u1E6F\\u0167\\u01AD\\u0288\\u2C66\\uA787\"\n}, {\n  base: 'tz',\n  letters: \"\\uA729\"\n}, {\n  base: 'u',\n  letters: \"u\\u24E4\\uFF55\\xF9\\xFA\\xFB\\u0169\\u1E79\\u016B\\u1E7B\\u016D\\xFC\\u01DC\\u01D8\\u01D6\\u01DA\\u1EE7\\u016F\\u0171\\u01D4\\u0215\\u0217\\u01B0\\u1EEB\\u1EE9\\u1EEF\\u1EED\\u1EF1\\u1EE5\\u1E73\\u0173\\u1E77\\u1E75\\u0289\"\n}, {\n  base: 'v',\n  letters: \"v\\u24E5\\uFF56\\u1E7D\\u1E7F\\u028B\\uA75F\\u028C\"\n}, {\n  base: 'vy',\n  letters: \"\\uA761\"\n}, {\n  base: 'w',\n  letters: \"w\\u24E6\\uFF57\\u1E81\\u1E83\\u0175\\u1E87\\u1E85\\u1E98\\u1E89\\u2C73\"\n}, {\n  base: 'x',\n  letters: \"x\\u24E7\\uFF58\\u1E8B\\u1E8D\"\n}, {\n  base: 'y',\n  letters: \"y\\u24E8\\uFF59\\u1EF3\\xFD\\u0177\\u1EF9\\u0233\\u1E8F\\xFF\\u1EF7\\u1E99\\u1EF5\\u01B4\\u024F\\u1EFF\"\n}, {\n  base: 'z',\n  letters: \"z\\u24E9\\uFF5A\\u017A\\u1E91\\u017C\\u017E\\u1E93\\u1E95\\u01B6\\u0225\\u0240\\u2C6C\\uA763\"\n}];\nvar anyDiacritic = new RegExp('[' + diacritics.map(function (d) {\n  return d.letters;\n}).join('') + ']', 'g');\nvar diacriticToBase = {};\nfor (var i = 0; i < diacritics.length; i++) {\n  var diacritic = diacritics[i];\n  for (var j = 0; j < diacritic.letters.length; j++) {\n    diacriticToBase[diacritic.letters[j]] = diacritic.base;\n  }\n}\nvar stripDiacritics = function stripDiacritics(str) {\n  return str.replace(anyDiacritic, function (match) {\n    return diacriticToBase[match];\n  });\n};\n\nvar memoizedStripDiacriticsForInput = (0,memoize_one__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(stripDiacritics);\nvar trimString = function trimString(str) {\n  return str.replace(/^\\s+|\\s+$/g, '');\n};\nvar defaultStringify = function defaultStringify(option) {\n  return \"\".concat(option.label, \" \").concat(option.value);\n};\nvar createFilter = function createFilter(config) {\n  return function (option, rawInput) {\n    // eslint-disable-next-line no-underscore-dangle\n    if (option.data.__isNew__) return true;\n    var _ignoreCase$ignoreAcc = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        ignoreCase: true,\n        ignoreAccents: true,\n        stringify: defaultStringify,\n        trim: true,\n        matchFrom: 'any'\n      }, config),\n      ignoreCase = _ignoreCase$ignoreAcc.ignoreCase,\n      ignoreAccents = _ignoreCase$ignoreAcc.ignoreAccents,\n      stringify = _ignoreCase$ignoreAcc.stringify,\n      trim = _ignoreCase$ignoreAcc.trim,\n      matchFrom = _ignoreCase$ignoreAcc.matchFrom;\n    var input = trim ? trimString(rawInput) : rawInput;\n    var candidate = trim ? trimString(stringify(option)) : stringify(option);\n    if (ignoreCase) {\n      input = input.toLowerCase();\n      candidate = candidate.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = memoizedStripDiacriticsForInput(input);\n      candidate = stripDiacritics(candidate);\n    }\n    return matchFrom === 'start' ? candidate.substr(0, input.length) === input : candidate.indexOf(input) > -1;\n  };\n};\n\nvar _excluded = [\"innerRef\"];\nfunction DummyInput(_ref) {\n  var innerRef = _ref.innerRef,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(_ref, _excluded);\n  // Remove animation props not meant for HTML elements\n  var filteredProps = (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.r)(props, 'onExited', 'in', 'enter', 'exit', 'appear');\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"input\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: innerRef\n  }, filteredProps, {\n    css: /*#__PURE__*/(0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.css)({\n      label: 'dummyInput',\n      // get rid of any default styles\n      background: 0,\n      border: 0,\n      // important! this hides the flashing cursor\n      caretColor: 'transparent',\n      fontSize: 'inherit',\n      gridArea: '1 / 1 / 2 / 3',\n      outline: 0,\n      padding: 0,\n      // important! without `width` browsers won't allow focus\n      width: 1,\n      // remove cursor on desktop\n      color: 'transparent',\n      // remove cursor on mobile whilst maintaining \"scroll into view\" behaviour\n      left: -100,\n      opacity: 0,\n      position: 'relative',\n      transform: 'scale(.01)'\n    },  false ? 0 : \";label:DummyInput;\",  false ? 0 : \"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkR1bW15SW5wdXQudHN4Il0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQXlCTSIsImZpbGUiOiJEdW1teUlucHV0LnRzeCIsInNvdXJjZXNDb250ZW50IjpbIi8qKiBAanN4IGpzeCAqL1xuaW1wb3J0IHsgSlNYLCBSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBqc3ggfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5pbXBvcnQgeyByZW1vdmVQcm9wcyB9IGZyb20gJy4uL3V0aWxzJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRHVtbXlJbnB1dCh7XG4gIGlubmVyUmVmLFxuICAuLi5wcm9wc1xufTogSlNYLkludHJpbnNpY0VsZW1lbnRzWydpbnB1dCddICYge1xuICByZWFkb25seSBpbm5lclJlZjogUmVmPEhUTUxJbnB1dEVsZW1lbnQ+O1xufSkge1xuICAvLyBSZW1vdmUgYW5pbWF0aW9uIHByb3BzIG5vdCBtZWFudCBmb3IgSFRNTCBlbGVtZW50c1xuICBjb25zdCBmaWx0ZXJlZFByb3BzID0gcmVtb3ZlUHJvcHMoXG4gICAgcHJvcHMsXG4gICAgJ29uRXhpdGVkJyxcbiAgICAnaW4nLFxuICAgICdlbnRlcicsXG4gICAgJ2V4aXQnLFxuICAgICdhcHBlYXInXG4gICk7XG5cbiAgcmV0dXJuIChcbiAgICA8aW5wdXRcbiAgICAgIHJlZj17aW5uZXJSZWZ9XG4gICAgICB7Li4uZmlsdGVyZWRQcm9wc31cbiAgICAgIGNzcz17e1xuICAgICAgICBsYWJlbDogJ2R1bW15SW5wdXQnLFxuICAgICAgICAvLyBnZXQgcmlkIG9mIGFueSBkZWZhdWx0IHN0eWxlc1xuICAgICAgICBiYWNrZ3JvdW5kOiAwLFxuICAgICAgICBib3JkZXI6IDAsXG4gICAgICAgIC8vIGltcG9ydGFudCEgdGhpcyBoaWRlcyB0aGUgZmxhc2hpbmcgY3Vyc29yXG4gICAgICAgIGNhcmV0Q29sb3I6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgIGZvbnRTaXplOiAnaW5oZXJpdCcsXG4gICAgICAgIGdyaWRBcmVhOiAnMSAvIDEgLyAyIC8gMycsXG4gICAgICAgIG91dGxpbmU6IDAsXG4gICAgICAgIHBhZGRpbmc6IDAsXG4gICAgICAgIC8vIGltcG9ydGFudCEgd2l0aG91dCBgd2lkdGhgIGJyb3dzZXJzIHdvbid0IGFsbG93IGZvY3VzXG4gICAgICAgIHdpZHRoOiAxLFxuXG4gICAgICAgIC8vIHJlbW92ZSBjdXJzb3Igb24gZGVza3RvcFxuICAgICAgICBjb2xvcjogJ3RyYW5zcGFyZW50JyxcblxuICAgICAgICAvLyByZW1vdmUgY3Vyc29yIG9uIG1vYmlsZSB3aGlsc3QgbWFpbnRhaW5pbmcgXCJzY3JvbGwgaW50byB2aWV3XCIgYmVoYXZpb3VyXG4gICAgICAgIGxlZnQ6IC0xMDAsXG4gICAgICAgIG9wYWNpdHk6IDAsXG4gICAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxuICAgICAgICB0cmFuc2Zvcm06ICdzY2FsZSguMDEpJyxcbiAgICAgIH19XG4gICAgLz5cbiAgKTtcbn1cbiJdfQ== */\")\n  }));\n}\n\nvar cancelScroll = function cancelScroll(event) {\n  if (event.cancelable) event.preventDefault();\n  event.stopPropagation();\n};\nfunction useScrollCapture(_ref) {\n  var isEnabled = _ref.isEnabled,\n    onBottomArrive = _ref.onBottomArrive,\n    onBottomLeave = _ref.onBottomLeave,\n    onTopArrive = _ref.onTopArrive,\n    onTopLeave = _ref.onTopLeave;\n  var isBottom = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(false);\n  var isTop = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(false);\n  var touchStart = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(0);\n  var scrollTarget = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n  var handleEventDelta = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (event, delta) {\n    if (scrollTarget.current === null) return;\n    var _scrollTarget$current = scrollTarget.current,\n      scrollTop = _scrollTarget$current.scrollTop,\n      scrollHeight = _scrollTarget$current.scrollHeight,\n      clientHeight = _scrollTarget$current.clientHeight;\n    var target = scrollTarget.current;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = scrollHeight - clientHeight - scrollTop;\n    var shouldCancelScroll = false;\n\n    // reset bottom/top flags\n    if (availableScroll > delta && isBottom.current) {\n      if (onBottomLeave) onBottomLeave(event);\n      isBottom.current = false;\n    }\n    if (isDeltaPositive && isTop.current) {\n      if (onTopLeave) onTopLeave(event);\n      isTop.current = false;\n    }\n\n    // bottom limit\n    if (isDeltaPositive && delta > availableScroll) {\n      if (onBottomArrive && !isBottom.current) {\n        onBottomArrive(event);\n      }\n      target.scrollTop = scrollHeight;\n      shouldCancelScroll = true;\n      isBottom.current = true;\n\n      // top limit\n    } else if (!isDeltaPositive && -delta > scrollTop) {\n      if (onTopArrive && !isTop.current) {\n        onTopArrive(event);\n      }\n      target.scrollTop = 0;\n      shouldCancelScroll = true;\n      isTop.current = true;\n    }\n\n    // cancel scroll\n    if (shouldCancelScroll) {\n      cancelScroll(event);\n    }\n  }, [onBottomArrive, onBottomLeave, onTopArrive, onTopLeave]);\n  var onWheel = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (event) {\n    handleEventDelta(event, event.deltaY);\n  }, [handleEventDelta]);\n  var onTouchStart = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (event) {\n    // set touch start so we can calculate touchmove delta\n    touchStart.current = event.changedTouches[0].clientY;\n  }, []);\n  var onTouchMove = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (event) {\n    var deltaY = touchStart.current - event.changedTouches[0].clientY;\n    handleEventDelta(event, deltaY);\n  }, [handleEventDelta]);\n  var startListening = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (el) {\n    // bail early if no element is available to attach to\n    if (!el) return;\n    var notPassive = _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.s ? {\n      passive: false\n    } : false;\n    el.addEventListener('wheel', onWheel, notPassive);\n    el.addEventListener('touchstart', onTouchStart, notPassive);\n    el.addEventListener('touchmove', onTouchMove, notPassive);\n  }, [onTouchMove, onTouchStart, onWheel]);\n  var stopListening = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (el) {\n    // bail early if no element is available to detach from\n    if (!el) return;\n    el.removeEventListener('wheel', onWheel, false);\n    el.removeEventListener('touchstart', onTouchStart, false);\n    el.removeEventListener('touchmove', onTouchMove, false);\n  }, [onTouchMove, onTouchStart, onWheel]);\n  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {\n    if (!isEnabled) return;\n    var element = scrollTarget.current;\n    startListening(element);\n    return function () {\n      stopListening(element);\n    };\n  }, [isEnabled, startListening, stopListening]);\n  return function (element) {\n    scrollTarget.current = element;\n  };\n}\n\nvar STYLE_KEYS = ['boxSizing', 'height', 'overflow', 'paddingRight', 'position'];\nvar LOCK_STYLES = {\n  boxSizing: 'border-box',\n  // account for possible declaration `width: 100%;` on body\n  overflow: 'hidden',\n  position: 'relative',\n  height: '100%'\n};\nfunction preventTouchMove(e) {\n  if (e.cancelable) e.preventDefault();\n}\nfunction allowTouchMove(e) {\n  e.stopPropagation();\n}\nfunction preventInertiaScroll() {\n  var top = this.scrollTop;\n  var totalScroll = this.scrollHeight;\n  var currentScroll = top + this.offsetHeight;\n  if (top === 0) {\n    this.scrollTop = 1;\n  } else if (currentScroll === totalScroll) {\n    this.scrollTop = top - 1;\n  }\n}\n\n// `ontouchstart` check works on most browsers\n// `maxTouchPoints` works on IE10/11 and Surface\nfunction isTouchDevice() {\n  return 'ontouchstart' in window || navigator.maxTouchPoints;\n}\nvar canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nvar activeScrollLocks = 0;\nvar listenerOptions = {\n  capture: false,\n  passive: false\n};\nfunction useScrollLock(_ref) {\n  var isEnabled = _ref.isEnabled,\n    _ref$accountForScroll = _ref.accountForScrollbars,\n    accountForScrollbars = _ref$accountForScroll === void 0 ? true : _ref$accountForScroll;\n  var originalStyles = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)({});\n  var scrollTarget = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n  var addScrollLock = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (touchScrollTarget) {\n    if (!canUseDOM) return;\n    var target = document.body;\n    var targetStyle = target && target.style;\n    if (accountForScrollbars) {\n      // store any styles already applied to the body\n      STYLE_KEYS.forEach(function (key) {\n        var val = targetStyle && targetStyle[key];\n        originalStyles.current[key] = val;\n      });\n    }\n\n    // apply the lock styles and padding if this is the first scroll lock\n    if (accountForScrollbars && activeScrollLocks < 1) {\n      var currentPadding = parseInt(originalStyles.current.paddingRight, 10) || 0;\n      var clientWidth = document.body ? document.body.clientWidth : 0;\n      var adjustedPadding = window.innerWidth - clientWidth + currentPadding || 0;\n      Object.keys(LOCK_STYLES).forEach(function (key) {\n        var val = LOCK_STYLES[key];\n        if (targetStyle) {\n          targetStyle[key] = val;\n        }\n      });\n      if (targetStyle) {\n        targetStyle.paddingRight = \"\".concat(adjustedPadding, \"px\");\n      }\n    }\n\n    // account for touch devices\n    if (target && isTouchDevice()) {\n      // Mobile Safari ignores { overflow: hidden } declaration on the body.\n      target.addEventListener('touchmove', preventTouchMove, listenerOptions);\n\n      // Allow scroll on provided target\n      if (touchScrollTarget) {\n        touchScrollTarget.addEventListener('touchstart', preventInertiaScroll, listenerOptions);\n        touchScrollTarget.addEventListener('touchmove', allowTouchMove, listenerOptions);\n      }\n    }\n\n    // increment active scroll locks\n    activeScrollLocks += 1;\n  }, [accountForScrollbars]);\n  var removeScrollLock = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (touchScrollTarget) {\n    if (!canUseDOM) return;\n    var target = document.body;\n    var targetStyle = target && target.style;\n\n    // safely decrement active scroll locks\n    activeScrollLocks = Math.max(activeScrollLocks - 1, 0);\n\n    // reapply original body styles, if any\n    if (accountForScrollbars && activeScrollLocks < 1) {\n      STYLE_KEYS.forEach(function (key) {\n        var val = originalStyles.current[key];\n        if (targetStyle) {\n          targetStyle[key] = val;\n        }\n      });\n    }\n\n    // remove touch listeners\n    if (target && isTouchDevice()) {\n      target.removeEventListener('touchmove', preventTouchMove, listenerOptions);\n      if (touchScrollTarget) {\n        touchScrollTarget.removeEventListener('touchstart', preventInertiaScroll, listenerOptions);\n        touchScrollTarget.removeEventListener('touchmove', allowTouchMove, listenerOptions);\n      }\n    }\n  }, [accountForScrollbars]);\n  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {\n    if (!isEnabled) return;\n    var element = scrollTarget.current;\n    addScrollLock(element);\n    return function () {\n      removeScrollLock(element);\n    };\n  }, [isEnabled, addScrollLock, removeScrollLock]);\n  return function (element) {\n    scrollTarget.current = element;\n  };\n}\n\nfunction _EMOTION_STRINGIFIED_CSS_ERROR__$1() { return \"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).\"; }\nvar blurSelectInput = function blurSelectInput(event) {\n  var element = event.target;\n  return element.ownerDocument.activeElement && element.ownerDocument.activeElement.blur();\n};\nvar _ref2$1 =  false ? 0 : {\n  name: \"bp8cua-ScrollManager\",\n  styles: \"position:fixed;left:0;bottom:0;right:0;top:0;label:ScrollManager;\",\n  map: \"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\",\n  toString: _EMOTION_STRINGIFIED_CSS_ERROR__$1\n};\nfunction ScrollManager(_ref) {\n  var children = _ref.children,\n    lockEnabled = _ref.lockEnabled,\n    _ref$captureEnabled = _ref.captureEnabled,\n    captureEnabled = _ref$captureEnabled === void 0 ? true : _ref$captureEnabled,\n    onBottomArrive = _ref.onBottomArrive,\n    onBottomLeave = _ref.onBottomLeave,\n    onTopArrive = _ref.onTopArrive,\n    onTopLeave = _ref.onTopLeave;\n  var setScrollCaptureTarget = useScrollCapture({\n    isEnabled: captureEnabled,\n    onBottomArrive: onBottomArrive,\n    onBottomLeave: onBottomLeave,\n    onTopArrive: onTopArrive,\n    onTopLeave: onTopLeave\n  });\n  var setScrollLockTarget = useScrollLock({\n    isEnabled: lockEnabled\n  });\n  var targetRef = function targetRef(element) {\n    setScrollCaptureTarget(element);\n    setScrollLockTarget(element);\n  };\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(react__WEBPACK_IMPORTED_MODULE_7__.Fragment, null, lockEnabled && (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"div\", {\n    onClick: blurSelectInput,\n    css: _ref2$1\n  }), children(targetRef));\n}\n\nfunction _EMOTION_STRINGIFIED_CSS_ERROR__() { return \"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).\"; }\nvar _ref2 =  false ? 0 : {\n  name: \"5kkxb2-requiredInput-RequiredInput\",\n  styles: \"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%;label:RequiredInput;\",\n  map: \"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIlJlcXVpcmVkSW5wdXQudHN4Il0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQWNJIiwiZmlsZSI6IlJlcXVpcmVkSW5wdXQudHN4Iiwic291cmNlc0NvbnRlbnQiOlsiLyoqIEBqc3gganN4ICovXG5pbXBvcnQgeyBGb2N1c0V2ZW50SGFuZGxlciwgRnVuY3Rpb25Db21wb25lbnQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBqc3ggfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5cbmNvbnN0IFJlcXVpcmVkSW5wdXQ6IEZ1bmN0aW9uQ29tcG9uZW50PHtcbiAgcmVhZG9ubHkgbmFtZT86IHN0cmluZztcbiAgcmVhZG9ubHkgb25Gb2N1czogRm9jdXNFdmVudEhhbmRsZXI8SFRNTElucHV0RWxlbWVudD47XG59PiA9ICh7IG5hbWUsIG9uRm9jdXMgfSkgPT4gKFxuICA8aW5wdXRcbiAgICByZXF1aXJlZFxuICAgIG5hbWU9e25hbWV9XG4gICAgdGFiSW5kZXg9ey0xfVxuICAgIGFyaWEtaGlkZGVuPVwidHJ1ZVwiXG4gICAgb25Gb2N1cz17b25Gb2N1c31cbiAgICBjc3M9e3tcbiAgICAgIGxhYmVsOiAncmVxdWlyZWRJbnB1dCcsXG4gICAgICBvcGFjaXR5OiAwLFxuICAgICAgcG9pbnRlckV2ZW50czogJ25vbmUnLFxuICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICBib3R0b206IDAsXG4gICAgICBsZWZ0OiAwLFxuICAgICAgcmlnaHQ6IDAsXG4gICAgICB3aWR0aDogJzEwMCUnLFxuICAgIH19XG4gICAgLy8gUHJldmVudCBgU3dpdGNoaW5nIGZyb20gdW5jb250cm9sbGVkIHRvIGNvbnRyb2xsZWRgIGVycm9yXG4gICAgdmFsdWU9XCJcIlxuICAgIG9uQ2hhbmdlPXsoKSA9PiB7fX1cbiAgLz5cbik7XG5cbmV4cG9ydCBkZWZhdWx0IFJlcXVpcmVkSW5wdXQ7XG4iXX0= */\",\n  toString: _EMOTION_STRINGIFIED_CSS_ERROR__\n};\nvar RequiredInput = function RequiredInput(_ref) {\n  var name = _ref.name,\n    onFocus = _ref.onFocus;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_9__.jsx)(\"input\", {\n    required: true,\n    name: name,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\",\n    onFocus: onFocus,\n    css: _ref2\n    // Prevent `Switching from uncontrolled to controlled` error\n    ,\n    value: \"\",\n    onChange: function onChange() {}\n  });\n};\nvar RequiredInput$1 = RequiredInput;\n\n/// <reference types=\"user-agent-data-types\" />\n\nfunction testPlatform(re) {\n  var _window$navigator$use;\n  return typeof window !== 'undefined' && window.navigator != null ? re.test(((_window$navigator$use = window.navigator['userAgentData']) === null || _window$navigator$use === void 0 ? void 0 : _window$navigator$use.platform) || window.navigator.platform) : false;\n}\nfunction isIPhone() {\n  return testPlatform(/^iPhone/i);\n}\nfunction isMac() {\n  return testPlatform(/^Mac/i);\n}\nfunction isIPad() {\n  return testPlatform(/^iPad/i) ||\n  // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n  isMac() && navigator.maxTouchPoints > 1;\n}\nfunction isIOS() {\n  return isIPhone() || isIPad();\n}\nfunction isAppleDevice() {\n  return isMac() || isIOS();\n}\n\nvar formatGroupLabel = function formatGroupLabel(group) {\n  return group.label;\n};\nvar getOptionLabel$1 = function getOptionLabel(option) {\n  return option.label;\n};\nvar getOptionValue$1 = function getOptionValue(option) {\n  return option.value;\n};\nvar isOptionDisabled = function isOptionDisabled(option) {\n  return !!option.isDisabled;\n};\n\nvar defaultStyles = {\n  clearIndicator: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.a,\n  container: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.b,\n  control: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.d,\n  dropdownIndicator: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.e,\n  group: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.g,\n  groupHeading: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.f,\n  indicatorsContainer: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.i,\n  indicatorSeparator: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.h,\n  input: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.j,\n  loadingIndicator: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.l,\n  loadingMessage: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.k,\n  menu: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.m,\n  menuList: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.n,\n  menuPortal: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.o,\n  multiValue: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.p,\n  multiValueLabel: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.q,\n  multiValueRemove: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.t,\n  noOptionsMessage: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.u,\n  option: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.v,\n  placeholder: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.w,\n  singleValue: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.x,\n  valueContainer: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.y\n};\n// Merge Utility\n// Allows consumers to extend a base Select with additional styles\n\nfunction mergeStyles(source) {\n  var target = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  // initialize with source styles\n  var styles = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, source);\n\n  // massage in target styles\n  Object.keys(target).forEach(function (keyAsString) {\n    var key = keyAsString;\n    if (source[key]) {\n      styles[key] = function (rsCss, props) {\n        return target[key](source[key](rsCss, props), props);\n      };\n    } else {\n      styles[key] = target[key];\n    }\n  });\n  return styles;\n}\n\nvar colors = {\n  primary: '#2684FF',\n  primary75: '#4C9AFF',\n  primary50: '#B2D4FF',\n  primary25: '#DEEBFF',\n  danger: '#DE350B',\n  dangerLight: '#FFBDAD',\n  neutral0: 'hsl(0, 0%, 100%)',\n  neutral5: 'hsl(0, 0%, 95%)',\n  neutral10: 'hsl(0, 0%, 90%)',\n  neutral20: 'hsl(0, 0%, 80%)',\n  neutral30: 'hsl(0, 0%, 70%)',\n  neutral40: 'hsl(0, 0%, 60%)',\n  neutral50: 'hsl(0, 0%, 50%)',\n  neutral60: 'hsl(0, 0%, 40%)',\n  neutral70: 'hsl(0, 0%, 30%)',\n  neutral80: 'hsl(0, 0%, 20%)',\n  neutral90: 'hsl(0, 0%, 10%)'\n};\nvar borderRadius = 4;\n// Used to calculate consistent margin/padding on elements\nvar baseUnit = 4;\n// The minimum height of the control\nvar controlHeight = 38;\n// The amount of space between the control and menu */\nvar menuGutter = baseUnit * 2;\nvar spacing = {\n  baseUnit: baseUnit,\n  controlHeight: controlHeight,\n  menuGutter: menuGutter\n};\nvar defaultTheme = {\n  borderRadius: borderRadius,\n  colors: colors,\n  spacing: spacing\n};\n\nvar defaultProps = {\n  'aria-live': 'polite',\n  backspaceRemovesValue: true,\n  blurInputOnSelect: (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.z)(),\n  captureMenuScroll: !(0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.z)(),\n  classNames: {},\n  closeMenuOnSelect: true,\n  closeMenuOnScroll: false,\n  components: {},\n  controlShouldRenderValue: true,\n  escapeClearsValue: false,\n  filterOption: createFilter(),\n  formatGroupLabel: formatGroupLabel,\n  getOptionLabel: getOptionLabel$1,\n  getOptionValue: getOptionValue$1,\n  isDisabled: false,\n  isLoading: false,\n  isMulti: false,\n  isRtl: false,\n  isSearchable: true,\n  isOptionDisabled: isOptionDisabled,\n  loadingMessage: function loadingMessage() {\n    return 'Loading...';\n  },\n  maxMenuHeight: 300,\n  minMenuHeight: 140,\n  menuIsOpen: false,\n  menuPlacement: 'bottom',\n  menuPosition: 'absolute',\n  menuShouldBlockScroll: false,\n  menuShouldScrollIntoView: !(0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.A)(),\n  noOptionsMessage: function noOptionsMessage() {\n    return 'No options';\n  },\n  openMenuOnFocus: false,\n  openMenuOnClick: true,\n  options: [],\n  pageSize: 5,\n  placeholder: 'Select...',\n  screenReaderStatus: function screenReaderStatus(_ref) {\n    var count = _ref.count;\n    return \"\".concat(count, \" result\").concat(count !== 1 ? 's' : '', \" available\");\n  },\n  styles: {},\n  tabIndex: 0,\n  tabSelectsValue: true,\n  unstyled: false\n};\nfunction toCategorizedOption(props, option, selectValue, index) {\n  var isDisabled = _isOptionDisabled(props, option, selectValue);\n  var isSelected = _isOptionSelected(props, option, selectValue);\n  var label = getOptionLabel(props, option);\n  var value = getOptionValue(props, option);\n  return {\n    type: 'option',\n    data: option,\n    isDisabled: isDisabled,\n    isSelected: isSelected,\n    label: label,\n    value: value,\n    index: index\n  };\n}\nfunction buildCategorizedOptions(props, selectValue) {\n  return props.options.map(function (groupOrOption, groupOrOptionIndex) {\n    if ('options' in groupOrOption) {\n      var categorizedOptions = groupOrOption.options.map(function (option, optionIndex) {\n        return toCategorizedOption(props, option, selectValue, optionIndex);\n      }).filter(function (categorizedOption) {\n        return isFocusable(props, categorizedOption);\n      });\n      return categorizedOptions.length > 0 ? {\n        type: 'group',\n        data: groupOrOption,\n        options: categorizedOptions,\n        index: groupOrOptionIndex\n      } : undefined;\n    }\n    var categorizedOption = toCategorizedOption(props, groupOrOption, selectValue, groupOrOptionIndex);\n    return isFocusable(props, categorizedOption) ? categorizedOption : undefined;\n  }).filter(_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.K);\n}\nfunction buildFocusableOptionsFromCategorizedOptions(categorizedOptions) {\n  return categorizedOptions.reduce(function (optionsAccumulator, categorizedOption) {\n    if (categorizedOption.type === 'group') {\n      optionsAccumulator.push.apply(optionsAccumulator, (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(categorizedOption.options.map(function (option) {\n        return option.data;\n      })));\n    } else {\n      optionsAccumulator.push(categorizedOption.data);\n    }\n    return optionsAccumulator;\n  }, []);\n}\nfunction buildFocusableOptionsWithIds(categorizedOptions, optionId) {\n  return categorizedOptions.reduce(function (optionsAccumulator, categorizedOption) {\n    if (categorizedOption.type === 'group') {\n      optionsAccumulator.push.apply(optionsAccumulator, (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(categorizedOption.options.map(function (option) {\n        return {\n          data: option.data,\n          id: \"\".concat(optionId, \"-\").concat(categorizedOption.index, \"-\").concat(option.index)\n        };\n      })));\n    } else {\n      optionsAccumulator.push({\n        data: categorizedOption.data,\n        id: \"\".concat(optionId, \"-\").concat(categorizedOption.index)\n      });\n    }\n    return optionsAccumulator;\n  }, []);\n}\nfunction buildFocusableOptions(props, selectValue) {\n  return buildFocusableOptionsFromCategorizedOptions(buildCategorizedOptions(props, selectValue));\n}\nfunction isFocusable(props, categorizedOption) {\n  var _props$inputValue = props.inputValue,\n    inputValue = _props$inputValue === void 0 ? '' : _props$inputValue;\n  var data = categorizedOption.data,\n    isSelected = categorizedOption.isSelected,\n    label = categorizedOption.label,\n    value = categorizedOption.value;\n  return (!shouldHideSelectedOptions(props) || !isSelected) && _filterOption(props, {\n    label: label,\n    value: value,\n    data: data\n  }, inputValue);\n}\nfunction getNextFocusedValue(state, nextSelectValue) {\n  var focusedValue = state.focusedValue,\n    lastSelectValue = state.selectValue;\n  var lastFocusedIndex = lastSelectValue.indexOf(focusedValue);\n  if (lastFocusedIndex > -1) {\n    var nextFocusedIndex = nextSelectValue.indexOf(focusedValue);\n    if (nextFocusedIndex > -1) {\n      // the focused value is still in the selectValue, return it\n      return focusedValue;\n    } else if (lastFocusedIndex < nextSelectValue.length) {\n      // the focusedValue is not present in the next selectValue array by\n      // reference, so return the new value at the same index\n      return nextSelectValue[lastFocusedIndex];\n    }\n  }\n  return null;\n}\nfunction getNextFocusedOption(state, options) {\n  var lastFocusedOption = state.focusedOption;\n  return lastFocusedOption && options.indexOf(lastFocusedOption) > -1 ? lastFocusedOption : options[0];\n}\nvar getFocusedOptionId = function getFocusedOptionId(focusableOptionsWithIds, focusedOption) {\n  var _focusableOptionsWith;\n  var focusedOptionId = (_focusableOptionsWith = focusableOptionsWithIds.find(function (option) {\n    return option.data === focusedOption;\n  })) === null || _focusableOptionsWith === void 0 ? void 0 : _focusableOptionsWith.id;\n  return focusedOptionId || null;\n};\nvar getOptionLabel = function getOptionLabel(props, data) {\n  return props.getOptionLabel(data);\n};\nvar getOptionValue = function getOptionValue(props, data) {\n  return props.getOptionValue(data);\n};\nfunction _isOptionDisabled(props, option, selectValue) {\n  return typeof props.isOptionDisabled === 'function' ? props.isOptionDisabled(option, selectValue) : false;\n}\nfunction _isOptionSelected(props, option, selectValue) {\n  if (selectValue.indexOf(option) > -1) return true;\n  if (typeof props.isOptionSelected === 'function') {\n    return props.isOptionSelected(option, selectValue);\n  }\n  var candidate = getOptionValue(props, option);\n  return selectValue.some(function (i) {\n    return getOptionValue(props, i) === candidate;\n  });\n}\nfunction _filterOption(props, option, inputValue) {\n  return props.filterOption ? props.filterOption(option, inputValue) : true;\n}\nvar shouldHideSelectedOptions = function shouldHideSelectedOptions(props) {\n  var hideSelectedOptions = props.hideSelectedOptions,\n    isMulti = props.isMulti;\n  if (hideSelectedOptions === undefined) return isMulti;\n  return hideSelectedOptions;\n};\nvar instanceId = 1;\nvar Select = /*#__PURE__*/function (_Component) {\n  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(Select, _Component);\n  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(Select);\n  // Misc. Instance Properties\n  // ------------------------------\n\n  // TODO\n\n  // Refs\n  // ------------------------------\n\n  // Lifecycle\n  // ------------------------------\n\n  function Select(_props) {\n    var _this;\n    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, Select);\n    _this = _super.call(this, _props);\n    _this.state = {\n      ariaSelection: null,\n      focusedOption: null,\n      focusedOptionId: null,\n      focusableOptionsWithIds: [],\n      focusedValue: null,\n      inputIsHidden: false,\n      isFocused: false,\n      selectValue: [],\n      clearFocusValueOnUpdate: false,\n      prevWasFocused: false,\n      inputIsHiddenAfterUpdate: undefined,\n      prevProps: undefined,\n      instancePrefix: ''\n    };\n    _this.blockOptionHover = false;\n    _this.isComposing = false;\n    _this.commonProps = void 0;\n    _this.initialTouchX = 0;\n    _this.initialTouchY = 0;\n    _this.openAfterFocus = false;\n    _this.scrollToFocusedOptionOnUpdate = false;\n    _this.userIsDragging = void 0;\n    _this.isAppleDevice = isAppleDevice();\n    _this.controlRef = null;\n    _this.getControlRef = function (ref) {\n      _this.controlRef = ref;\n    };\n    _this.focusedOptionRef = null;\n    _this.getFocusedOptionRef = function (ref) {\n      _this.focusedOptionRef = ref;\n    };\n    _this.menuListRef = null;\n    _this.getMenuListRef = function (ref) {\n      _this.menuListRef = ref;\n    };\n    _this.inputRef = null;\n    _this.getInputRef = function (ref) {\n      _this.inputRef = ref;\n    };\n    _this.focus = _this.focusInput;\n    _this.blur = _this.blurInput;\n    _this.onChange = function (newValue, actionMeta) {\n      var _this$props = _this.props,\n        onChange = _this$props.onChange,\n        name = _this$props.name;\n      actionMeta.name = name;\n      _this.ariaOnChange(newValue, actionMeta);\n      onChange(newValue, actionMeta);\n    };\n    _this.setValue = function (newValue, action, option) {\n      var _this$props2 = _this.props,\n        closeMenuOnSelect = _this$props2.closeMenuOnSelect,\n        isMulti = _this$props2.isMulti,\n        inputValue = _this$props2.inputValue;\n      _this.onInputChange('', {\n        action: 'set-value',\n        prevInputValue: inputValue\n      });\n      if (closeMenuOnSelect) {\n        _this.setState({\n          inputIsHiddenAfterUpdate: !isMulti\n        });\n        _this.onMenuClose();\n      }\n      // when the select value should change, we should reset focusedValue\n      _this.setState({\n        clearFocusValueOnUpdate: true\n      });\n      _this.onChange(newValue, {\n        action: action,\n        option: option\n      });\n    };\n    _this.selectOption = function (newValue) {\n      var _this$props3 = _this.props,\n        blurInputOnSelect = _this$props3.blurInputOnSelect,\n        isMulti = _this$props3.isMulti,\n        name = _this$props3.name;\n      var selectValue = _this.state.selectValue;\n      var deselected = isMulti && _this.isOptionSelected(newValue, selectValue);\n      var isDisabled = _this.isOptionDisabled(newValue, selectValue);\n      if (deselected) {\n        var candidate = _this.getOptionValue(newValue);\n        _this.setValue((0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.B)(selectValue.filter(function (i) {\n          return _this.getOptionValue(i) !== candidate;\n        })), 'deselect-option', newValue);\n      } else if (!isDisabled) {\n        // Select option if option is not disabled\n        if (isMulti) {\n          _this.setValue((0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.B)([].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectValue), [newValue])), 'select-option', newValue);\n        } else {\n          _this.setValue((0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.C)(newValue), 'select-option');\n        }\n      } else {\n        _this.ariaOnChange((0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.C)(newValue), {\n          action: 'select-option',\n          option: newValue,\n          name: name\n        });\n        return;\n      }\n      if (blurInputOnSelect) {\n        _this.blurInput();\n      }\n    };\n    _this.removeValue = function (removedValue) {\n      var isMulti = _this.props.isMulti;\n      var selectValue = _this.state.selectValue;\n      var candidate = _this.getOptionValue(removedValue);\n      var newValueArray = selectValue.filter(function (i) {\n        return _this.getOptionValue(i) !== candidate;\n      });\n      var newValue = (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.D)(isMulti, newValueArray, newValueArray[0] || null);\n      _this.onChange(newValue, {\n        action: 'remove-value',\n        removedValue: removedValue\n      });\n      _this.focusInput();\n    };\n    _this.clearValue = function () {\n      var selectValue = _this.state.selectValue;\n      _this.onChange((0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.D)(_this.props.isMulti, [], null), {\n        action: 'clear',\n        removedValues: selectValue\n      });\n    };\n    _this.popValue = function () {\n      var isMulti = _this.props.isMulti;\n      var selectValue = _this.state.selectValue;\n      var lastSelectedValue = selectValue[selectValue.length - 1];\n      var newValueArray = selectValue.slice(0, selectValue.length - 1);\n      var newValue = (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.D)(isMulti, newValueArray, newValueArray[0] || null);\n      if (lastSelectedValue) {\n        _this.onChange(newValue, {\n          action: 'pop-value',\n          removedValue: lastSelectedValue\n        });\n      }\n    };\n    _this.getFocusedOptionId = function (focusedOption) {\n      return getFocusedOptionId(_this.state.focusableOptionsWithIds, focusedOption);\n    };\n    _this.getFocusableOptionsWithIds = function () {\n      return buildFocusableOptionsWithIds(buildCategorizedOptions(_this.props, _this.state.selectValue), _this.getElementId('option'));\n    };\n    _this.getValue = function () {\n      return _this.state.selectValue;\n    };\n    _this.cx = function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.E.apply(void 0, [_this.props.classNamePrefix].concat(args));\n    };\n    _this.getOptionLabel = function (data) {\n      return getOptionLabel(_this.props, data);\n    };\n    _this.getOptionValue = function (data) {\n      return getOptionValue(_this.props, data);\n    };\n    _this.getStyles = function (key, props) {\n      var unstyled = _this.props.unstyled;\n      var base = defaultStyles[key](props, unstyled);\n      base.boxSizing = 'border-box';\n      var custom = _this.props.styles[key];\n      return custom ? custom(base, props) : base;\n    };\n    _this.getClassNames = function (key, props) {\n      var _this$props$className, _this$props$className2;\n      return (_this$props$className = (_this$props$className2 = _this.props.classNames)[key]) === null || _this$props$className === void 0 ? void 0 : _this$props$className.call(_this$props$className2, props);\n    };\n    _this.getElementId = function (element) {\n      return \"\".concat(_this.state.instancePrefix, \"-\").concat(element);\n    };\n    _this.getComponents = function () {\n      return (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.F)(_this.props);\n    };\n    _this.buildCategorizedOptions = function () {\n      return buildCategorizedOptions(_this.props, _this.state.selectValue);\n    };\n    _this.getCategorizedOptions = function () {\n      return _this.props.menuIsOpen ? _this.buildCategorizedOptions() : [];\n    };\n    _this.buildFocusableOptions = function () {\n      return buildFocusableOptionsFromCategorizedOptions(_this.buildCategorizedOptions());\n    };\n    _this.getFocusableOptions = function () {\n      return _this.props.menuIsOpen ? _this.buildFocusableOptions() : [];\n    };\n    _this.ariaOnChange = function (value, actionMeta) {\n      _this.setState({\n        ariaSelection: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n          value: value\n        }, actionMeta)\n      });\n    };\n    _this.onMenuMouseDown = function (event) {\n      if (event.button !== 0) {\n        return;\n      }\n      event.stopPropagation();\n      event.preventDefault();\n      _this.focusInput();\n    };\n    _this.onMenuMouseMove = function (event) {\n      _this.blockOptionHover = false;\n    };\n    _this.onControlMouseDown = function (event) {\n      // Event captured by dropdown indicator\n      if (event.defaultPrevented) {\n        return;\n      }\n      var openMenuOnClick = _this.props.openMenuOnClick;\n      if (!_this.state.isFocused) {\n        if (openMenuOnClick) {\n          _this.openAfterFocus = true;\n        }\n        _this.focusInput();\n      } else if (!_this.props.menuIsOpen) {\n        if (openMenuOnClick) {\n          _this.openMenu('first');\n        }\n      } else {\n        if (event.target.tagName !== 'INPUT' && event.target.tagName !== 'TEXTAREA') {\n          _this.onMenuClose();\n        }\n      }\n      if (event.target.tagName !== 'INPUT' && event.target.tagName !== 'TEXTAREA') {\n        event.preventDefault();\n      }\n    };\n    _this.onDropdownIndicatorMouseDown = function (event) {\n      // ignore mouse events that weren't triggered by the primary button\n      if (event && event.type === 'mousedown' && event.button !== 0) {\n        return;\n      }\n      if (_this.props.isDisabled) return;\n      var _this$props4 = _this.props,\n        isMulti = _this$props4.isMulti,\n        menuIsOpen = _this$props4.menuIsOpen;\n      _this.focusInput();\n      if (menuIsOpen) {\n        _this.setState({\n          inputIsHiddenAfterUpdate: !isMulti\n        });\n        _this.onMenuClose();\n      } else {\n        _this.openMenu('first');\n      }\n      event.preventDefault();\n    };\n    _this.onClearIndicatorMouseDown = function (event) {\n      // ignore mouse events that weren't triggered by the primary button\n      if (event && event.type === 'mousedown' && event.button !== 0) {\n        return;\n      }\n      _this.clearValue();\n      event.preventDefault();\n      _this.openAfterFocus = false;\n      if (event.type === 'touchend') {\n        _this.focusInput();\n      } else {\n        setTimeout(function () {\n          return _this.focusInput();\n        });\n      }\n    };\n    _this.onScroll = function (event) {\n      if (typeof _this.props.closeMenuOnScroll === 'boolean') {\n        if (event.target instanceof HTMLElement && (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.G)(event.target)) {\n          _this.props.onMenuClose();\n        }\n      } else if (typeof _this.props.closeMenuOnScroll === 'function') {\n        if (_this.props.closeMenuOnScroll(event)) {\n          _this.props.onMenuClose();\n        }\n      }\n    };\n    _this.onCompositionStart = function () {\n      _this.isComposing = true;\n    };\n    _this.onCompositionEnd = function () {\n      _this.isComposing = false;\n    };\n    _this.onTouchStart = function (_ref2) {\n      var touches = _ref2.touches;\n      var touch = touches && touches.item(0);\n      if (!touch) {\n        return;\n      }\n      _this.initialTouchX = touch.clientX;\n      _this.initialTouchY = touch.clientY;\n      _this.userIsDragging = false;\n    };\n    _this.onTouchMove = function (_ref3) {\n      var touches = _ref3.touches;\n      var touch = touches && touches.item(0);\n      if (!touch) {\n        return;\n      }\n      var deltaX = Math.abs(touch.clientX - _this.initialTouchX);\n      var deltaY = Math.abs(touch.clientY - _this.initialTouchY);\n      var moveThreshold = 5;\n      _this.userIsDragging = deltaX > moveThreshold || deltaY > moveThreshold;\n    };\n    _this.onTouchEnd = function (event) {\n      if (_this.userIsDragging) return;\n\n      // close the menu if the user taps outside\n      // we're checking on event.target here instead of event.currentTarget, because we want to assert information\n      // on events on child elements, not the document (which we've attached this handler to).\n      if (_this.controlRef && !_this.controlRef.contains(event.target) && _this.menuListRef && !_this.menuListRef.contains(event.target)) {\n        _this.blurInput();\n      }\n\n      // reset move vars\n      _this.initialTouchX = 0;\n      _this.initialTouchY = 0;\n    };\n    _this.onControlTouchEnd = function (event) {\n      if (_this.userIsDragging) return;\n      _this.onControlMouseDown(event);\n    };\n    _this.onClearIndicatorTouchEnd = function (event) {\n      if (_this.userIsDragging) return;\n      _this.onClearIndicatorMouseDown(event);\n    };\n    _this.onDropdownIndicatorTouchEnd = function (event) {\n      if (_this.userIsDragging) return;\n      _this.onDropdownIndicatorMouseDown(event);\n    };\n    _this.handleInputChange = function (event) {\n      var prevInputValue = _this.props.inputValue;\n      var inputValue = event.currentTarget.value;\n      _this.setState({\n        inputIsHiddenAfterUpdate: false\n      });\n      _this.onInputChange(inputValue, {\n        action: 'input-change',\n        prevInputValue: prevInputValue\n      });\n      if (!_this.props.menuIsOpen) {\n        _this.onMenuOpen();\n      }\n    };\n    _this.onInputFocus = function (event) {\n      if (_this.props.onFocus) {\n        _this.props.onFocus(event);\n      }\n      _this.setState({\n        inputIsHiddenAfterUpdate: false,\n        isFocused: true\n      });\n      if (_this.openAfterFocus || _this.props.openMenuOnFocus) {\n        _this.openMenu('first');\n      }\n      _this.openAfterFocus = false;\n    };\n    _this.onInputBlur = function (event) {\n      var prevInputValue = _this.props.inputValue;\n      if (_this.menuListRef && _this.menuListRef.contains(document.activeElement)) {\n        _this.inputRef.focus();\n        return;\n      }\n      if (_this.props.onBlur) {\n        _this.props.onBlur(event);\n      }\n      _this.onInputChange('', {\n        action: 'input-blur',\n        prevInputValue: prevInputValue\n      });\n      _this.onMenuClose();\n      _this.setState({\n        focusedValue: null,\n        isFocused: false\n      });\n    };\n    _this.onOptionHover = function (focusedOption) {\n      if (_this.blockOptionHover || _this.state.focusedOption === focusedOption) {\n        return;\n      }\n      var options = _this.getFocusableOptions();\n      var focusedOptionIndex = options.indexOf(focusedOption);\n      _this.setState({\n        focusedOption: focusedOption,\n        focusedOptionId: focusedOptionIndex > -1 ? _this.getFocusedOptionId(focusedOption) : null\n      });\n    };\n    _this.shouldHideSelectedOptions = function () {\n      return shouldHideSelectedOptions(_this.props);\n    };\n    _this.onValueInputFocus = function (e) {\n      e.preventDefault();\n      e.stopPropagation();\n      _this.focus();\n    };\n    _this.onKeyDown = function (event) {\n      var _this$props5 = _this.props,\n        isMulti = _this$props5.isMulti,\n        backspaceRemovesValue = _this$props5.backspaceRemovesValue,\n        escapeClearsValue = _this$props5.escapeClearsValue,\n        inputValue = _this$props5.inputValue,\n        isClearable = _this$props5.isClearable,\n        isDisabled = _this$props5.isDisabled,\n        menuIsOpen = _this$props5.menuIsOpen,\n        onKeyDown = _this$props5.onKeyDown,\n        tabSelectsValue = _this$props5.tabSelectsValue,\n        openMenuOnFocus = _this$props5.openMenuOnFocus;\n      var _this$state = _this.state,\n        focusedOption = _this$state.focusedOption,\n        focusedValue = _this$state.focusedValue,\n        selectValue = _this$state.selectValue;\n      if (isDisabled) return;\n      if (typeof onKeyDown === 'function') {\n        onKeyDown(event);\n        if (event.defaultPrevented) {\n          return;\n        }\n      }\n\n      // Block option hover events when the user has just pressed a key\n      _this.blockOptionHover = true;\n      switch (event.key) {\n        case 'ArrowLeft':\n          if (!isMulti || inputValue) return;\n          _this.focusValue('previous');\n          break;\n        case 'ArrowRight':\n          if (!isMulti || inputValue) return;\n          _this.focusValue('next');\n          break;\n        case 'Delete':\n        case 'Backspace':\n          if (inputValue) return;\n          if (focusedValue) {\n            _this.removeValue(focusedValue);\n          } else {\n            if (!backspaceRemovesValue) return;\n            if (isMulti) {\n              _this.popValue();\n            } else if (isClearable) {\n              _this.clearValue();\n            }\n          }\n          break;\n        case 'Tab':\n          if (_this.isComposing) return;\n          if (event.shiftKey || !menuIsOpen || !tabSelectsValue || !focusedOption ||\n          // don't capture the event if the menu opens on focus and the focused\n          // option is already selected; it breaks the flow of navigation\n          openMenuOnFocus && _this.isOptionSelected(focusedOption, selectValue)) {\n            return;\n          }\n          _this.selectOption(focusedOption);\n          break;\n        case 'Enter':\n          if (event.keyCode === 229) {\n            // ignore the keydown event from an Input Method Editor(IME)\n            // ref. https://www.w3.org/TR/uievents/#determine-keydown-keyup-keyCode\n            break;\n          }\n          if (menuIsOpen) {\n            if (!focusedOption) return;\n            if (_this.isComposing) return;\n            _this.selectOption(focusedOption);\n            break;\n          }\n          return;\n        case 'Escape':\n          if (menuIsOpen) {\n            _this.setState({\n              inputIsHiddenAfterUpdate: false\n            });\n            _this.onInputChange('', {\n              action: 'menu-close',\n              prevInputValue: inputValue\n            });\n            _this.onMenuClose();\n          } else if (isClearable && escapeClearsValue) {\n            _this.clearValue();\n          }\n          break;\n        case ' ':\n          // space\n          if (inputValue) {\n            return;\n          }\n          if (!menuIsOpen) {\n            _this.openMenu('first');\n            break;\n          }\n          if (!focusedOption) return;\n          _this.selectOption(focusedOption);\n          break;\n        case 'ArrowUp':\n          if (menuIsOpen) {\n            _this.focusOption('up');\n          } else {\n            _this.openMenu('last');\n          }\n          break;\n        case 'ArrowDown':\n          if (menuIsOpen) {\n            _this.focusOption('down');\n          } else {\n            _this.openMenu('first');\n          }\n          break;\n        case 'PageUp':\n          if (!menuIsOpen) return;\n          _this.focusOption('pageup');\n          break;\n        case 'PageDown':\n          if (!menuIsOpen) return;\n          _this.focusOption('pagedown');\n          break;\n        case 'Home':\n          if (!menuIsOpen) return;\n          _this.focusOption('first');\n          break;\n        case 'End':\n          if (!menuIsOpen) return;\n          _this.focusOption('last');\n          break;\n        default:\n          return;\n      }\n      event.preventDefault();\n    };\n    _this.state.instancePrefix = 'react-select-' + (_this.props.instanceId || ++instanceId);\n    _this.state.selectValue = (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.H)(_props.value);\n    // Set focusedOption if menuIsOpen is set on init (e.g. defaultMenuIsOpen)\n    if (_props.menuIsOpen && _this.state.selectValue.length) {\n      var focusableOptionsWithIds = _this.getFocusableOptionsWithIds();\n      var focusableOptions = _this.buildFocusableOptions();\n      var optionIndex = focusableOptions.indexOf(_this.state.selectValue[0]);\n      _this.state.focusableOptionsWithIds = focusableOptionsWithIds;\n      _this.state.focusedOption = focusableOptions[optionIndex];\n      _this.state.focusedOptionId = getFocusedOptionId(focusableOptionsWithIds, focusableOptions[optionIndex]);\n    }\n    return _this;\n  }\n  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(Select, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.startListeningComposition();\n      this.startListeningToTouch();\n      if (this.props.closeMenuOnScroll && document && document.addEventListener) {\n        // Listen to all scroll events, and filter them out inside of 'onScroll'\n        document.addEventListener('scroll', this.onScroll, true);\n      }\n      if (this.props.autoFocus) {\n        this.focusInput();\n      }\n\n      // Scroll focusedOption into view if menuIsOpen is set on mount (e.g. defaultMenuIsOpen)\n      if (this.props.menuIsOpen && this.state.focusedOption && this.menuListRef && this.focusedOptionRef) {\n        (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.I)(this.menuListRef, this.focusedOptionRef);\n      }\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props6 = this.props,\n        isDisabled = _this$props6.isDisabled,\n        menuIsOpen = _this$props6.menuIsOpen;\n      var isFocused = this.state.isFocused;\n      if (\n      // ensure focus is restored correctly when the control becomes enabled\n      isFocused && !isDisabled && prevProps.isDisabled ||\n      // ensure focus is on the Input when the menu opens\n      isFocused && menuIsOpen && !prevProps.menuIsOpen) {\n        this.focusInput();\n      }\n      if (isFocused && isDisabled && !prevProps.isDisabled) {\n        // ensure select state gets blurred in case Select is programmatically disabled while focused\n        // eslint-disable-next-line react/no-did-update-set-state\n        this.setState({\n          isFocused: false\n        }, this.onMenuClose);\n      } else if (!isFocused && !isDisabled && prevProps.isDisabled && this.inputRef === document.activeElement) {\n        // ensure select state gets focused in case Select is programatically re-enabled while focused (Firefox)\n        // eslint-disable-next-line react/no-did-update-set-state\n        this.setState({\n          isFocused: true\n        });\n      }\n\n      // scroll the focused option into view if necessary\n      if (this.menuListRef && this.focusedOptionRef && this.scrollToFocusedOptionOnUpdate) {\n        (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.I)(this.menuListRef, this.focusedOptionRef);\n        this.scrollToFocusedOptionOnUpdate = false;\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.stopListeningComposition();\n      this.stopListeningToTouch();\n      document.removeEventListener('scroll', this.onScroll, true);\n    }\n\n    // ==============================\n    // Consumer Handlers\n    // ==============================\n  }, {\n    key: \"onMenuOpen\",\n    value: function onMenuOpen() {\n      this.props.onMenuOpen();\n    }\n  }, {\n    key: \"onMenuClose\",\n    value: function onMenuClose() {\n      this.onInputChange('', {\n        action: 'menu-close',\n        prevInputValue: this.props.inputValue\n      });\n      this.props.onMenuClose();\n    }\n  }, {\n    key: \"onInputChange\",\n    value: function onInputChange(newValue, actionMeta) {\n      this.props.onInputChange(newValue, actionMeta);\n    }\n\n    // ==============================\n    // Methods\n    // ==============================\n  }, {\n    key: \"focusInput\",\n    value: function focusInput() {\n      if (!this.inputRef) return;\n      this.inputRef.focus();\n    }\n  }, {\n    key: \"blurInput\",\n    value: function blurInput() {\n      if (!this.inputRef) return;\n      this.inputRef.blur();\n    }\n\n    // aliased for consumers\n  }, {\n    key: \"openMenu\",\n    value: function openMenu(focusOption) {\n      var _this2 = this;\n      var _this$state2 = this.state,\n        selectValue = _this$state2.selectValue,\n        isFocused = _this$state2.isFocused;\n      var focusableOptions = this.buildFocusableOptions();\n      var openAtIndex = focusOption === 'first' ? 0 : focusableOptions.length - 1;\n      if (!this.props.isMulti) {\n        var selectedIndex = focusableOptions.indexOf(selectValue[0]);\n        if (selectedIndex > -1) {\n          openAtIndex = selectedIndex;\n        }\n      }\n\n      // only scroll if the menu isn't already open\n      this.scrollToFocusedOptionOnUpdate = !(isFocused && this.menuListRef);\n      this.setState({\n        inputIsHiddenAfterUpdate: false,\n        focusedValue: null,\n        focusedOption: focusableOptions[openAtIndex],\n        focusedOptionId: this.getFocusedOptionId(focusableOptions[openAtIndex])\n      }, function () {\n        return _this2.onMenuOpen();\n      });\n    }\n  }, {\n    key: \"focusValue\",\n    value: function focusValue(direction) {\n      var _this$state3 = this.state,\n        selectValue = _this$state3.selectValue,\n        focusedValue = _this$state3.focusedValue;\n\n      // Only multiselects support value focusing\n      if (!this.props.isMulti) return;\n      this.setState({\n        focusedOption: null\n      });\n      var focusedIndex = selectValue.indexOf(focusedValue);\n      if (!focusedValue) {\n        focusedIndex = -1;\n      }\n      var lastIndex = selectValue.length - 1;\n      var nextFocus = -1;\n      if (!selectValue.length) return;\n      switch (direction) {\n        case 'previous':\n          if (focusedIndex === 0) {\n            // don't cycle from the start to the end\n            nextFocus = 0;\n          } else if (focusedIndex === -1) {\n            // if nothing is focused, focus the last value first\n            nextFocus = lastIndex;\n          } else {\n            nextFocus = focusedIndex - 1;\n          }\n          break;\n        case 'next':\n          if (focusedIndex > -1 && focusedIndex < lastIndex) {\n            nextFocus = focusedIndex + 1;\n          }\n          break;\n      }\n      this.setState({\n        inputIsHidden: nextFocus !== -1,\n        focusedValue: selectValue[nextFocus]\n      });\n    }\n  }, {\n    key: \"focusOption\",\n    value: function focusOption() {\n      var direction = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'first';\n      var pageSize = this.props.pageSize;\n      var focusedOption = this.state.focusedOption;\n      var options = this.getFocusableOptions();\n      if (!options.length) return;\n      var nextFocus = 0; // handles 'first'\n      var focusedIndex = options.indexOf(focusedOption);\n      if (!focusedOption) {\n        focusedIndex = -1;\n      }\n      if (direction === 'up') {\n        nextFocus = focusedIndex > 0 ? focusedIndex - 1 : options.length - 1;\n      } else if (direction === 'down') {\n        nextFocus = (focusedIndex + 1) % options.length;\n      } else if (direction === 'pageup') {\n        nextFocus = focusedIndex - pageSize;\n        if (nextFocus < 0) nextFocus = 0;\n      } else if (direction === 'pagedown') {\n        nextFocus = focusedIndex + pageSize;\n        if (nextFocus > options.length - 1) nextFocus = options.length - 1;\n      } else if (direction === 'last') {\n        nextFocus = options.length - 1;\n      }\n      this.scrollToFocusedOptionOnUpdate = true;\n      this.setState({\n        focusedOption: options[nextFocus],\n        focusedValue: null,\n        focusedOptionId: this.getFocusedOptionId(options[nextFocus])\n      });\n    }\n  }, {\n    key: \"getTheme\",\n    value:\n    // ==============================\n    // Getters\n    // ==============================\n\n    function getTheme() {\n      // Use the default theme if there are no customisations.\n      if (!this.props.theme) {\n        return defaultTheme;\n      }\n      // If the theme prop is a function, assume the function\n      // knows how to merge the passed-in default theme with\n      // its own modifications.\n      if (typeof this.props.theme === 'function') {\n        return this.props.theme(defaultTheme);\n      }\n      // Otherwise, if a plain theme object was passed in,\n      // overlay it with the default theme.\n      return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, defaultTheme), this.props.theme);\n    }\n  }, {\n    key: \"getCommonProps\",\n    value: function getCommonProps() {\n      var clearValue = this.clearValue,\n        cx = this.cx,\n        getStyles = this.getStyles,\n        getClassNames = this.getClassNames,\n        getValue = this.getValue,\n        selectOption = this.selectOption,\n        setValue = this.setValue,\n        props = this.props;\n      var isMulti = props.isMulti,\n        isRtl = props.isRtl,\n        options = props.options;\n      var hasValue = this.hasValue();\n      return {\n        clearValue: clearValue,\n        cx: cx,\n        getStyles: getStyles,\n        getClassNames: getClassNames,\n        getValue: getValue,\n        hasValue: hasValue,\n        isMulti: isMulti,\n        isRtl: isRtl,\n        options: options,\n        selectOption: selectOption,\n        selectProps: props,\n        setValue: setValue,\n        theme: this.getTheme()\n      };\n    }\n  }, {\n    key: \"hasValue\",\n    value: function hasValue() {\n      var selectValue = this.state.selectValue;\n      return selectValue.length > 0;\n    }\n  }, {\n    key: \"hasOptions\",\n    value: function hasOptions() {\n      return !!this.getFocusableOptions().length;\n    }\n  }, {\n    key: \"isClearable\",\n    value: function isClearable() {\n      var _this$props7 = this.props,\n        isClearable = _this$props7.isClearable,\n        isMulti = _this$props7.isMulti;\n\n      // single select, by default, IS NOT clearable\n      // multi select, by default, IS clearable\n      if (isClearable === undefined) return isMulti;\n      return isClearable;\n    }\n  }, {\n    key: \"isOptionDisabled\",\n    value: function isOptionDisabled(option, selectValue) {\n      return _isOptionDisabled(this.props, option, selectValue);\n    }\n  }, {\n    key: \"isOptionSelected\",\n    value: function isOptionSelected(option, selectValue) {\n      return _isOptionSelected(this.props, option, selectValue);\n    }\n  }, {\n    key: \"filterOption\",\n    value: function filterOption(option, inputValue) {\n      return _filterOption(this.props, option, inputValue);\n    }\n  }, {\n    key: \"formatOptionLabel\",\n    value: function formatOptionLabel(data, context) {\n      if (typeof this.props.formatOptionLabel === 'function') {\n        var _inputValue = this.props.inputValue;\n        var _selectValue = this.state.selectValue;\n        return this.props.formatOptionLabel(data, {\n          context: context,\n          inputValue: _inputValue,\n          selectValue: _selectValue\n        });\n      } else {\n        return this.getOptionLabel(data);\n      }\n    }\n  }, {\n    key: \"formatGroupLabel\",\n    value: function formatGroupLabel(data) {\n      return this.props.formatGroupLabel(data);\n    }\n\n    // ==============================\n    // Mouse Handlers\n    // ==============================\n  }, {\n    key: \"startListeningComposition\",\n    value:\n    // ==============================\n    // Composition Handlers\n    // ==============================\n\n    function startListeningComposition() {\n      if (document && document.addEventListener) {\n        document.addEventListener('compositionstart', this.onCompositionStart, false);\n        document.addEventListener('compositionend', this.onCompositionEnd, false);\n      }\n    }\n  }, {\n    key: \"stopListeningComposition\",\n    value: function stopListeningComposition() {\n      if (document && document.removeEventListener) {\n        document.removeEventListener('compositionstart', this.onCompositionStart);\n        document.removeEventListener('compositionend', this.onCompositionEnd);\n      }\n    }\n  }, {\n    key: \"startListeningToTouch\",\n    value:\n    // ==============================\n    // Touch Handlers\n    // ==============================\n\n    function startListeningToTouch() {\n      if (document && document.addEventListener) {\n        document.addEventListener('touchstart', this.onTouchStart, false);\n        document.addEventListener('touchmove', this.onTouchMove, false);\n        document.addEventListener('touchend', this.onTouchEnd, false);\n      }\n    }\n  }, {\n    key: \"stopListeningToTouch\",\n    value: function stopListeningToTouch() {\n      if (document && document.removeEventListener) {\n        document.removeEventListener('touchstart', this.onTouchStart);\n        document.removeEventListener('touchmove', this.onTouchMove);\n        document.removeEventListener('touchend', this.onTouchEnd);\n      }\n    }\n  }, {\n    key: \"renderInput\",\n    value:\n    // ==============================\n    // Renderers\n    // ==============================\n    function renderInput() {\n      var _this$props8 = this.props,\n        isDisabled = _this$props8.isDisabled,\n        isSearchable = _this$props8.isSearchable,\n        inputId = _this$props8.inputId,\n        inputValue = _this$props8.inputValue,\n        tabIndex = _this$props8.tabIndex,\n        form = _this$props8.form,\n        menuIsOpen = _this$props8.menuIsOpen,\n        required = _this$props8.required;\n      var _this$getComponents = this.getComponents(),\n        Input = _this$getComponents.Input;\n      var _this$state4 = this.state,\n        inputIsHidden = _this$state4.inputIsHidden,\n        ariaSelection = _this$state4.ariaSelection;\n      var commonProps = this.commonProps;\n      var id = inputId || this.getElementId('input');\n\n      // aria attributes makes the JSX \"noisy\", separated for clarity\n      var ariaAttributes = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        'aria-autocomplete': 'list',\n        'aria-expanded': menuIsOpen,\n        'aria-haspopup': true,\n        'aria-errormessage': this.props['aria-errormessage'],\n        'aria-invalid': this.props['aria-invalid'],\n        'aria-label': this.props['aria-label'],\n        'aria-labelledby': this.props['aria-labelledby'],\n        'aria-required': required,\n        role: 'combobox',\n        'aria-activedescendant': this.isAppleDevice ? undefined : this.state.focusedOptionId || ''\n      }, menuIsOpen && {\n        'aria-controls': this.getElementId('listbox')\n      }), !isSearchable && {\n        'aria-readonly': true\n      }), this.hasValue() ? (ariaSelection === null || ariaSelection === void 0 ? void 0 : ariaSelection.action) === 'initial-input-focus' && {\n        'aria-describedby': this.getElementId('live-region')\n      } : {\n        'aria-describedby': this.getElementId('placeholder')\n      });\n      if (!isSearchable) {\n        // use a dummy input to maintain focus/blur functionality\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DummyInput, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n          id: id,\n          innerRef: this.getInputRef,\n          onBlur: this.onInputBlur,\n          onChange: _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.J,\n          onFocus: this.onInputFocus,\n          disabled: isDisabled,\n          tabIndex: tabIndex,\n          inputMode: \"none\",\n          form: form,\n          value: \"\"\n        }, ariaAttributes));\n      }\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Input, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        autoCapitalize: \"none\",\n        autoComplete: \"off\",\n        autoCorrect: \"off\",\n        id: id,\n        innerRef: this.getInputRef,\n        isDisabled: isDisabled,\n        isHidden: inputIsHidden,\n        onBlur: this.onInputBlur,\n        onChange: this.handleInputChange,\n        onFocus: this.onInputFocus,\n        spellCheck: \"false\",\n        tabIndex: tabIndex,\n        form: form,\n        type: \"text\",\n        value: inputValue\n      }, ariaAttributes));\n    }\n  }, {\n    key: \"renderPlaceholderOrValue\",\n    value: function renderPlaceholderOrValue() {\n      var _this3 = this;\n      var _this$getComponents2 = this.getComponents(),\n        MultiValue = _this$getComponents2.MultiValue,\n        MultiValueContainer = _this$getComponents2.MultiValueContainer,\n        MultiValueLabel = _this$getComponents2.MultiValueLabel,\n        MultiValueRemove = _this$getComponents2.MultiValueRemove,\n        SingleValue = _this$getComponents2.SingleValue,\n        Placeholder = _this$getComponents2.Placeholder;\n      var commonProps = this.commonProps;\n      var _this$props9 = this.props,\n        controlShouldRenderValue = _this$props9.controlShouldRenderValue,\n        isDisabled = _this$props9.isDisabled,\n        isMulti = _this$props9.isMulti,\n        inputValue = _this$props9.inputValue,\n        placeholder = _this$props9.placeholder;\n      var _this$state5 = this.state,\n        selectValue = _this$state5.selectValue,\n        focusedValue = _this$state5.focusedValue,\n        isFocused = _this$state5.isFocused;\n      if (!this.hasValue() || !controlShouldRenderValue) {\n        return inputValue ? null : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Placeholder, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n          key: \"placeholder\",\n          isDisabled: isDisabled,\n          isFocused: isFocused,\n          innerProps: {\n            id: this.getElementId('placeholder')\n          }\n        }), placeholder);\n      }\n      if (isMulti) {\n        return selectValue.map(function (opt, index) {\n          var isOptionFocused = opt === focusedValue;\n          var key = \"\".concat(_this3.getOptionLabel(opt), \"-\").concat(_this3.getOptionValue(opt));\n          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(MultiValue, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n            components: {\n              Container: MultiValueContainer,\n              Label: MultiValueLabel,\n              Remove: MultiValueRemove\n            },\n            isFocused: isOptionFocused,\n            isDisabled: isDisabled,\n            key: key,\n            index: index,\n            removeProps: {\n              onClick: function onClick() {\n                return _this3.removeValue(opt);\n              },\n              onTouchEnd: function onTouchEnd() {\n                return _this3.removeValue(opt);\n              },\n              onMouseDown: function onMouseDown(e) {\n                e.preventDefault();\n              }\n            },\n            data: opt\n          }), _this3.formatOptionLabel(opt, 'value'));\n        });\n      }\n      if (inputValue) {\n        return null;\n      }\n      var singleValue = selectValue[0];\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(SingleValue, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        data: singleValue,\n        isDisabled: isDisabled\n      }), this.formatOptionLabel(singleValue, 'value'));\n    }\n  }, {\n    key: \"renderClearIndicator\",\n    value: function renderClearIndicator() {\n      var _this$getComponents3 = this.getComponents(),\n        ClearIndicator = _this$getComponents3.ClearIndicator;\n      var commonProps = this.commonProps;\n      var _this$props10 = this.props,\n        isDisabled = _this$props10.isDisabled,\n        isLoading = _this$props10.isLoading;\n      var isFocused = this.state.isFocused;\n      if (!this.isClearable() || !ClearIndicator || isDisabled || !this.hasValue() || isLoading) {\n        return null;\n      }\n      var innerProps = {\n        onMouseDown: this.onClearIndicatorMouseDown,\n        onTouchEnd: this.onClearIndicatorTouchEnd,\n        'aria-hidden': 'true'\n      };\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ClearIndicator, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        innerProps: innerProps,\n        isFocused: isFocused\n      }));\n    }\n  }, {\n    key: \"renderLoadingIndicator\",\n    value: function renderLoadingIndicator() {\n      var _this$getComponents4 = this.getComponents(),\n        LoadingIndicator = _this$getComponents4.LoadingIndicator;\n      var commonProps = this.commonProps;\n      var _this$props11 = this.props,\n        isDisabled = _this$props11.isDisabled,\n        isLoading = _this$props11.isLoading;\n      var isFocused = this.state.isFocused;\n      if (!LoadingIndicator || !isLoading) return null;\n      var innerProps = {\n        'aria-hidden': 'true'\n      };\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(LoadingIndicator, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        innerProps: innerProps,\n        isDisabled: isDisabled,\n        isFocused: isFocused\n      }));\n    }\n  }, {\n    key: \"renderIndicatorSeparator\",\n    value: function renderIndicatorSeparator() {\n      var _this$getComponents5 = this.getComponents(),\n        DropdownIndicator = _this$getComponents5.DropdownIndicator,\n        IndicatorSeparator = _this$getComponents5.IndicatorSeparator;\n\n      // separator doesn't make sense without the dropdown indicator\n      if (!DropdownIndicator || !IndicatorSeparator) return null;\n      var commonProps = this.commonProps;\n      var isDisabled = this.props.isDisabled;\n      var isFocused = this.state.isFocused;\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IndicatorSeparator, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        isDisabled: isDisabled,\n        isFocused: isFocused\n      }));\n    }\n  }, {\n    key: \"renderDropdownIndicator\",\n    value: function renderDropdownIndicator() {\n      var _this$getComponents6 = this.getComponents(),\n        DropdownIndicator = _this$getComponents6.DropdownIndicator;\n      if (!DropdownIndicator) return null;\n      var commonProps = this.commonProps;\n      var isDisabled = this.props.isDisabled;\n      var isFocused = this.state.isFocused;\n      var innerProps = {\n        onMouseDown: this.onDropdownIndicatorMouseDown,\n        onTouchEnd: this.onDropdownIndicatorTouchEnd,\n        'aria-hidden': 'true'\n      };\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DropdownIndicator, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        innerProps: innerProps,\n        isDisabled: isDisabled,\n        isFocused: isFocused\n      }));\n    }\n  }, {\n    key: \"renderMenu\",\n    value: function renderMenu() {\n      var _this4 = this;\n      var _this$getComponents7 = this.getComponents(),\n        Group = _this$getComponents7.Group,\n        GroupHeading = _this$getComponents7.GroupHeading,\n        Menu = _this$getComponents7.Menu,\n        MenuList = _this$getComponents7.MenuList,\n        MenuPortal = _this$getComponents7.MenuPortal,\n        LoadingMessage = _this$getComponents7.LoadingMessage,\n        NoOptionsMessage = _this$getComponents7.NoOptionsMessage,\n        Option = _this$getComponents7.Option;\n      var commonProps = this.commonProps;\n      var focusedOption = this.state.focusedOption;\n      var _this$props12 = this.props,\n        captureMenuScroll = _this$props12.captureMenuScroll,\n        inputValue = _this$props12.inputValue,\n        isLoading = _this$props12.isLoading,\n        loadingMessage = _this$props12.loadingMessage,\n        minMenuHeight = _this$props12.minMenuHeight,\n        maxMenuHeight = _this$props12.maxMenuHeight,\n        menuIsOpen = _this$props12.menuIsOpen,\n        menuPlacement = _this$props12.menuPlacement,\n        menuPosition = _this$props12.menuPosition,\n        menuPortalTarget = _this$props12.menuPortalTarget,\n        menuShouldBlockScroll = _this$props12.menuShouldBlockScroll,\n        menuShouldScrollIntoView = _this$props12.menuShouldScrollIntoView,\n        noOptionsMessage = _this$props12.noOptionsMessage,\n        onMenuScrollToTop = _this$props12.onMenuScrollToTop,\n        onMenuScrollToBottom = _this$props12.onMenuScrollToBottom;\n      if (!menuIsOpen) return null;\n\n      // TODO: Internal Option Type here\n      var render = function render(props, id) {\n        var type = props.type,\n          data = props.data,\n          isDisabled = props.isDisabled,\n          isSelected = props.isSelected,\n          label = props.label,\n          value = props.value;\n        var isFocused = focusedOption === data;\n        var onHover = isDisabled ? undefined : function () {\n          return _this4.onOptionHover(data);\n        };\n        var onSelect = isDisabled ? undefined : function () {\n          return _this4.selectOption(data);\n        };\n        var optionId = \"\".concat(_this4.getElementId('option'), \"-\").concat(id);\n        var innerProps = {\n          id: optionId,\n          onClick: onSelect,\n          onMouseMove: onHover,\n          onMouseOver: onHover,\n          tabIndex: -1,\n          role: 'option',\n          'aria-selected': _this4.isAppleDevice ? undefined : isSelected // is not supported on Apple devices\n        };\n\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Option, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n          innerProps: innerProps,\n          data: data,\n          isDisabled: isDisabled,\n          isSelected: isSelected,\n          key: optionId,\n          label: label,\n          type: type,\n          value: value,\n          isFocused: isFocused,\n          innerRef: isFocused ? _this4.getFocusedOptionRef : undefined\n        }), _this4.formatOptionLabel(props.data, 'menu'));\n      };\n      var menuUI;\n      if (this.hasOptions()) {\n        menuUI = this.getCategorizedOptions().map(function (item) {\n          if (item.type === 'group') {\n            var _data = item.data,\n              options = item.options,\n              groupIndex = item.index;\n            var groupId = \"\".concat(_this4.getElementId('group'), \"-\").concat(groupIndex);\n            var headingId = \"\".concat(groupId, \"-heading\");\n            return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Group, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n              key: groupId,\n              data: _data,\n              options: options,\n              Heading: GroupHeading,\n              headingProps: {\n                id: headingId,\n                data: item.data\n              },\n              label: _this4.formatGroupLabel(item.data)\n            }), item.options.map(function (option) {\n              return render(option, \"\".concat(groupIndex, \"-\").concat(option.index));\n            }));\n          } else if (item.type === 'option') {\n            return render(item, \"\".concat(item.index));\n          }\n        });\n      } else if (isLoading) {\n        var message = loadingMessage({\n          inputValue: inputValue\n        });\n        if (message === null) return null;\n        menuUI = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(LoadingMessage, commonProps, message);\n      } else {\n        var _message = noOptionsMessage({\n          inputValue: inputValue\n        });\n        if (_message === null) return null;\n        menuUI = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(NoOptionsMessage, commonProps, _message);\n      }\n      var menuPlacementProps = {\n        minMenuHeight: minMenuHeight,\n        maxMenuHeight: maxMenuHeight,\n        menuPlacement: menuPlacement,\n        menuPosition: menuPosition,\n        menuShouldScrollIntoView: menuShouldScrollIntoView\n      };\n      var menuElement = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.M, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, menuPlacementProps), function (_ref4) {\n        var ref = _ref4.ref,\n          _ref4$placerProps = _ref4.placerProps,\n          placement = _ref4$placerProps.placement,\n          maxHeight = _ref4$placerProps.maxHeight;\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Menu, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, menuPlacementProps, {\n          innerRef: ref,\n          innerProps: {\n            onMouseDown: _this4.onMenuMouseDown,\n            onMouseMove: _this4.onMenuMouseMove\n          },\n          isLoading: isLoading,\n          placement: placement\n        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ScrollManager, {\n          captureEnabled: captureMenuScroll,\n          onTopArrive: onMenuScrollToTop,\n          onBottomArrive: onMenuScrollToBottom,\n          lockEnabled: menuShouldBlockScroll\n        }, function (scrollTargetRef) {\n          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(MenuList, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n            innerRef: function innerRef(instance) {\n              _this4.getMenuListRef(instance);\n              scrollTargetRef(instance);\n            },\n            innerProps: {\n              role: 'listbox',\n              'aria-multiselectable': commonProps.isMulti,\n              id: _this4.getElementId('listbox')\n            },\n            isLoading: isLoading,\n            maxHeight: maxHeight,\n            focusedOption: focusedOption\n          }), menuUI);\n        }));\n      });\n\n      // positioning behaviour is almost identical for portalled and fixed,\n      // so we use the same component. the actual portalling logic is forked\n      // within the component based on `menuPosition`\n      return menuPortalTarget || menuPosition === 'fixed' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(MenuPortal, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        appendTo: menuPortalTarget,\n        controlElement: this.controlRef,\n        menuPlacement: menuPlacement,\n        menuPosition: menuPosition\n      }), menuElement) : menuElement;\n    }\n  }, {\n    key: \"renderFormField\",\n    value: function renderFormField() {\n      var _this5 = this;\n      var _this$props13 = this.props,\n        delimiter = _this$props13.delimiter,\n        isDisabled = _this$props13.isDisabled,\n        isMulti = _this$props13.isMulti,\n        name = _this$props13.name,\n        required = _this$props13.required;\n      var selectValue = this.state.selectValue;\n      if (required && !this.hasValue() && !isDisabled) {\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(RequiredInput$1, {\n          name: name,\n          onFocus: this.onValueInputFocus\n        });\n      }\n      if (!name || isDisabled) return;\n      if (isMulti) {\n        if (delimiter) {\n          var value = selectValue.map(function (opt) {\n            return _this5.getOptionValue(opt);\n          }).join(delimiter);\n          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"input\", {\n            name: name,\n            type: \"hidden\",\n            value: value\n          });\n        } else {\n          var input = selectValue.length > 0 ? selectValue.map(function (opt, i) {\n            return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"input\", {\n              key: \"i-\".concat(i),\n              name: name,\n              type: \"hidden\",\n              value: _this5.getOptionValue(opt)\n            });\n          }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"input\", {\n            name: name,\n            type: \"hidden\",\n            value: \"\"\n          });\n          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"div\", null, input);\n        }\n      } else {\n        var _value = selectValue[0] ? this.getOptionValue(selectValue[0]) : '';\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"input\", {\n          name: name,\n          type: \"hidden\",\n          value: _value\n        });\n      }\n    }\n  }, {\n    key: \"renderLiveRegion\",\n    value: function renderLiveRegion() {\n      var commonProps = this.commonProps;\n      var _this$state6 = this.state,\n        ariaSelection = _this$state6.ariaSelection,\n        focusedOption = _this$state6.focusedOption,\n        focusedValue = _this$state6.focusedValue,\n        isFocused = _this$state6.isFocused,\n        selectValue = _this$state6.selectValue;\n      var focusableOptions = this.getFocusableOptions();\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(LiveRegion$1, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        id: this.getElementId('live-region'),\n        ariaSelection: ariaSelection,\n        focusedOption: focusedOption,\n        focusedValue: focusedValue,\n        isFocused: isFocused,\n        selectValue: selectValue,\n        focusableOptions: focusableOptions,\n        isAppleDevice: this.isAppleDevice\n      }));\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$getComponents8 = this.getComponents(),\n        Control = _this$getComponents8.Control,\n        IndicatorsContainer = _this$getComponents8.IndicatorsContainer,\n        SelectContainer = _this$getComponents8.SelectContainer,\n        ValueContainer = _this$getComponents8.ValueContainer;\n      var _this$props14 = this.props,\n        className = _this$props14.className,\n        id = _this$props14.id,\n        isDisabled = _this$props14.isDisabled,\n        menuIsOpen = _this$props14.menuIsOpen;\n      var isFocused = this.state.isFocused;\n      var commonProps = this.commonProps = this.getCommonProps();\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(SelectContainer, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        className: className,\n        innerProps: {\n          id: id,\n          onKeyDown: this.onKeyDown\n        },\n        isDisabled: isDisabled,\n        isFocused: isFocused\n      }), this.renderLiveRegion(), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Control, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        innerRef: this.getControlRef,\n        innerProps: {\n          onMouseDown: this.onControlMouseDown,\n          onTouchEnd: this.onControlTouchEnd\n        },\n        isDisabled: isDisabled,\n        isFocused: isFocused,\n        menuIsOpen: menuIsOpen\n      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ValueContainer, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        isDisabled: isDisabled\n      }), this.renderPlaceholderOrValue(), this.renderInput()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IndicatorsContainer, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        isDisabled: isDisabled\n      }), this.renderClearIndicator(), this.renderLoadingIndicator(), this.renderIndicatorSeparator(), this.renderDropdownIndicator())), this.renderMenu(), this.renderFormField());\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, state) {\n      var prevProps = state.prevProps,\n        clearFocusValueOnUpdate = state.clearFocusValueOnUpdate,\n        inputIsHiddenAfterUpdate = state.inputIsHiddenAfterUpdate,\n        ariaSelection = state.ariaSelection,\n        isFocused = state.isFocused,\n        prevWasFocused = state.prevWasFocused,\n        instancePrefix = state.instancePrefix;\n      var options = props.options,\n        value = props.value,\n        menuIsOpen = props.menuIsOpen,\n        inputValue = props.inputValue,\n        isMulti = props.isMulti;\n      var selectValue = (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.H)(value);\n      var newMenuOptionsState = {};\n      if (prevProps && (value !== prevProps.value || options !== prevProps.options || menuIsOpen !== prevProps.menuIsOpen || inputValue !== prevProps.inputValue)) {\n        var focusableOptions = menuIsOpen ? buildFocusableOptions(props, selectValue) : [];\n        var focusableOptionsWithIds = menuIsOpen ? buildFocusableOptionsWithIds(buildCategorizedOptions(props, selectValue), \"\".concat(instancePrefix, \"-option\")) : [];\n        var focusedValue = clearFocusValueOnUpdate ? getNextFocusedValue(state, selectValue) : null;\n        var focusedOption = getNextFocusedOption(state, focusableOptions);\n        var focusedOptionId = getFocusedOptionId(focusableOptionsWithIds, focusedOption);\n        newMenuOptionsState = {\n          selectValue: selectValue,\n          focusedOption: focusedOption,\n          focusedOptionId: focusedOptionId,\n          focusableOptionsWithIds: focusableOptionsWithIds,\n          focusedValue: focusedValue,\n          clearFocusValueOnUpdate: false\n        };\n      }\n      // some updates should toggle the state of the input visibility\n      var newInputIsHiddenState = inputIsHiddenAfterUpdate != null && props !== prevProps ? {\n        inputIsHidden: inputIsHiddenAfterUpdate,\n        inputIsHiddenAfterUpdate: undefined\n      } : {};\n      var newAriaSelection = ariaSelection;\n      var hasKeptFocus = isFocused && prevWasFocused;\n      if (isFocused && !hasKeptFocus) {\n        // If `value` or `defaultValue` props are not empty then announce them\n        // when the Select is initially focused\n        newAriaSelection = {\n          value: (0,_index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_11__.D)(isMulti, selectValue, selectValue[0] || null),\n          options: selectValue,\n          action: 'initial-input-focus'\n        };\n        hasKeptFocus = !prevWasFocused;\n      }\n\n      // If the 'initial-input-focus' action has been set already\n      // then reset the ariaSelection to null\n      if ((ariaSelection === null || ariaSelection === void 0 ? void 0 : ariaSelection.action) === 'initial-input-focus') {\n        newAriaSelection = null;\n      }\n      return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, newMenuOptionsState), newInputIsHiddenState), {}, {\n        prevProps: props,\n        ariaSelection: newAriaSelection,\n        prevWasFocused: hasKeptFocus\n      });\n    }\n  }]);\n  return Select;\n}(react__WEBPACK_IMPORTED_MODULE_7__.Component);\nSelect.defaultProps = defaultProps;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc2VsZWN0L2Rpc3QvU2VsZWN0LWFhYjAyN2YzLmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwRDtBQUNXO0FBQ0c7QUFDTjtBQUNOO0FBQ007QUFDWTtBQUMvQztBQUNzRDtBQUNtdEI7QUFDOXZCO0FBQ0w7QUFDcUQ7O0FBRTFGLGdEQUFnRDs7QUFFaEQ7QUFDQSxXQUFXLE1BQXFDLEdBQUcsQ0FHbEQsQ0FBQztBQUNGO0FBQ0EsMEJBQTBCLGFBQWEsU0FBUyw4QkFBOEIsV0FBVyxVQUFVLGtCQUFrQixnQkFBZ0IsVUFBVSxtQkFBbUIsZUFBZTtBQUNqTCxtREFBbUQsY0FBYztBQUNqRTtBQUNBO0FBQ0E7QUFDQSxTQUFTLG1EQUFHLFNBQVMsOEVBQVE7QUFDN0I7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxpQkFBaUIsOENBQU87QUFDeEIsV0FBVyxvRkFBYSxDQUFDLG9GQUFhLEdBQUcsa0RBQWtEO0FBQzNGLEdBQUc7O0FBRUg7QUFDQSxxQkFBcUIsOENBQU87QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLG9GQUFhO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILG9CQUFvQiw4Q0FBTztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILG9CQUFvQiw4Q0FBTztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLHFCQUFxQiw4Q0FBTztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxHQUFHO0FBQ0gseUJBQXlCLG1EQUFHLENBQUMsMkNBQVEsUUFBUSxtREFBRztBQUNoRDtBQUNBLEdBQUcsaUJBQWlCLG1EQUFHO0FBQ3ZCO0FBQ0EsR0FBRyxnQkFBZ0IsbURBQUc7QUFDdEI7QUFDQSxHQUFHLGdCQUFnQixtREFBRztBQUN0QjtBQUNBLEdBQUc7QUFDSCxTQUFTLG1EQUFHLENBQUMsMkNBQVEsUUFBUSxtREFBRztBQUNoQztBQUNBLEdBQUcsdUNBQXVDLG1EQUFHO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0EsZ0JBQWdCLHVCQUF1QjtBQUN2QztBQUNBLGtCQUFrQiw4QkFBOEI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBLHNDQUFzQyx3REFBVTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxvRkFBYTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLDhGQUF3QjtBQUNwQztBQUNBLHNCQUFzQiwwREFBVztBQUNqQyxTQUFTLG1EQUFHLFVBQVUsOEVBQVE7QUFDOUI7QUFDQSxHQUFHO0FBQ0gsc0JBQXNCLG1EQUFHO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSyxFQUFFLE1BQXFDLEdBQUcsQ0FBRSxLQUFLLGlCQUFpQixHQUFHLE1BQXFDLEdBQUcsQ0FBRSwrQ0FBK0MsY0FBYztBQUNqTCxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsNkNBQU07QUFDdkIsY0FBYyw2Q0FBTTtBQUNwQixtQkFBbUIsNkNBQU07QUFDekIscUJBQXFCLDZDQUFNO0FBQzNCLHlCQUF5QixrREFBVztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsZ0JBQWdCLGtEQUFXO0FBQzNCO0FBQ0EsR0FBRztBQUNILHFCQUFxQixrREFBVztBQUNoQztBQUNBO0FBQ0EsR0FBRztBQUNILG9CQUFvQixrREFBVztBQUMvQjtBQUNBO0FBQ0EsR0FBRztBQUNILHVCQUF1QixrREFBVztBQUNsQztBQUNBO0FBQ0EscUJBQXFCLHNEQUFxQjtBQUMxQztBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsc0JBQXNCLGtEQUFXO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQ7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qiw2Q0FBTSxHQUFHO0FBQ2hDLHFCQUFxQiw2Q0FBTTtBQUMzQixzQkFBc0Isa0RBQVc7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsaUNBQWlDLG1CQUFtQjtBQUNwRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLEdBQUc7QUFDSCx5QkFBeUIsa0RBQVc7QUFDcEM7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGdEQUFnRDtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsTUFBcUMsR0FBRyxDQUdyRCxDQUFDO0FBQ0Y7QUFDQSwwQkFBMEIsT0FBTyxTQUFTLFFBQVEsTUFBTSxvQkFBb0I7QUFDNUUsbURBQW1ELGNBQWM7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxtREFBRyxDQUFDLDJDQUFRLHVCQUF1QixtREFBRztBQUMvQztBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBLDhDQUE4QztBQUM5QyxZQUFZLE1BQXFDLEdBQUcsQ0FHbkQsQ0FBQztBQUNGO0FBQ0EsK0JBQStCLFVBQVUsb0JBQW9CLGtCQUFrQixTQUFTLE9BQU8sUUFBUSxXQUFXLG9CQUFvQjtBQUN0SSxtREFBbUQsY0FBYztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxtREFBRztBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGtCQUFrQixzREFBaUI7QUFDbkMsYUFBYSxzREFBWTtBQUN6QixXQUFXLHNEQUFLO0FBQ2hCLHFCQUFxQixzREFBb0I7QUFDekMsU0FBUyxzREFBUTtBQUNqQixnQkFBZ0Isc0RBQWU7QUFDL0IsdUJBQXVCLHNEQUFzQjtBQUM3QyxzQkFBc0Isc0RBQXFCO0FBQzNDLFNBQVMsc0RBQVE7QUFDakIsb0JBQW9CLHNEQUFtQjtBQUN2QyxrQkFBa0Isc0RBQWlCO0FBQ25DLFFBQVEsc0RBQU87QUFDZixZQUFZLHNEQUFXO0FBQ3ZCLGNBQWMsc0RBQWE7QUFDM0IsY0FBYyxzREFBYTtBQUMzQixtQkFBbUIsc0RBQWtCO0FBQ3JDLG9CQUFvQixzREFBbUI7QUFDdkMsb0JBQW9CLHNEQUFtQjtBQUN2QyxVQUFVLHNEQUFTO0FBQ25CLGVBQWUsc0RBQWM7QUFDN0IsZUFBZSxzREFBSztBQUNwQixrQkFBa0Isc0RBQWlCO0FBQ25DO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG9GQUFhLEdBQUc7O0FBRS9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDBEQUFjO0FBQ25DLHNCQUFzQiwwREFBYztBQUNwQyxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QiwwREFBYztBQUMzQztBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLEdBQUcsU0FBUyxzREFBVTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdEQUF3RCx3RkFBa0I7QUFDMUU7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdEQUF3RCx3RkFBa0I7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSwrRUFBUztBQUNYLGVBQWUsa0ZBQVk7QUFDM0I7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUkscUZBQWU7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QiwwREFBaUI7QUFDeEM7QUFDQSxTQUFTO0FBQ1QsUUFBUTtBQUNSO0FBQ0E7QUFDQSx5QkFBeUIsMERBQWlCLFdBQVcsd0ZBQWtCO0FBQ3ZFLFVBQVU7QUFDVix5QkFBeUIsMERBQWtCO0FBQzNDO0FBQ0EsUUFBUTtBQUNSLDJCQUEyQiwwREFBa0I7QUFDN0M7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxxQkFBcUIsMERBQVk7QUFDakM7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDBEQUFZO0FBQ2pDO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDBEQUFZO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBFQUEwRSxhQUFhO0FBQ3ZGO0FBQ0E7QUFDQSxhQUFhLHNEQUFVO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSwwREFBaUI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLG9GQUFhO0FBQ3BDO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1ELDBEQUFpQjtBQUNwRTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLDBEQUFVO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLGtGQUFZO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsUUFBUSwwREFBYztBQUN0QjtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7O0FBRUE7QUFDQTtBQUNBLFFBQVEsMERBQWM7QUFDdEI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLG9GQUFhLENBQUMsb0ZBQWEsR0FBRztBQUMzQztBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLDJCQUEyQixvRkFBYSxDQUFDLG9GQUFhLENBQUMsb0ZBQWE7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxPQUFPO0FBQ1A7QUFDQSxPQUFPO0FBQ1A7QUFDQSxRQUFRO0FBQ1I7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLDRCQUE0QixnREFBbUIsYUFBYSw4RUFBUTtBQUNwRTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isc0RBQUk7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsMEJBQTBCLGdEQUFtQixRQUFRLDhFQUFRLEdBQUc7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELGdEQUFtQixjQUFjLDhFQUFRLEdBQUc7QUFDNUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsZ0RBQW1CLGFBQWEsOEVBQVEsR0FBRztBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLFdBQVc7QUFDWCxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixnREFBbUIsY0FBYyw4RUFBUSxHQUFHO0FBQ3RFO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixnREFBbUIsaUJBQWlCLDhFQUFRLEdBQUc7QUFDekU7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixnREFBbUIsbUJBQW1CLDhFQUFRLEdBQUc7QUFDM0U7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixnREFBbUIscUJBQXFCLDhFQUFRLEdBQUc7QUFDN0U7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixnREFBbUIsb0JBQW9CLDhFQUFRLEdBQUc7QUFDNUU7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDRCQUE0QixnREFBbUIsU0FBUyw4RUFBUSxHQUFHO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLGdEQUFtQixRQUFRLDhFQUFRLEdBQUc7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQSxhQUFhO0FBQ2I7QUFDQSxhQUFhO0FBQ2IsWUFBWTtBQUNaO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsUUFBUTtBQUNSO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSw4QkFBOEIsZ0RBQW1CO0FBQ2pELFFBQVE7QUFDUjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsOEJBQThCLGdEQUFtQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLGdEQUFtQixDQUFDLHNEQUFVLEVBQUUsOEVBQVEsR0FBRztBQUNoRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixnREFBbUIsT0FBTyw4RUFBUSxHQUFHO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQSxTQUFTLGdCQUFnQixnREFBbUI7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsOEJBQThCLGdEQUFtQixXQUFXLDhFQUFRLEdBQUc7QUFDdkU7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCxTQUFTO0FBQ1QsT0FBTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQSx5RUFBeUUsZ0RBQW1CLGFBQWEsOEVBQVEsR0FBRztBQUNwSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGdEQUFtQjtBQUMvQztBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCw4QkFBOEIsZ0RBQW1CO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCxVQUFVO0FBQ1Y7QUFDQSxnQ0FBZ0MsZ0RBQW1CO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFdBQVcsaUJBQWlCLGdEQUFtQjtBQUMvQztBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsOEJBQThCLGdEQUFtQjtBQUNqRDtBQUNBLFFBQVE7QUFDUjtBQUNBLDRCQUE0QixnREFBbUI7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsZ0RBQW1CLGVBQWUsOEVBQVEsR0FBRztBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsZ0RBQW1CLGtCQUFrQiw4RUFBUSxHQUFHO0FBQzFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxPQUFPLHlDQUF5QyxnREFBbUIsVUFBVSw4RUFBUSxHQUFHO0FBQ3hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLE9BQU8sZ0JBQWdCLGdEQUFtQixpQkFBaUIsOEVBQVEsR0FBRztBQUN0RTtBQUNBLE9BQU8sc0VBQXNFLGdEQUFtQixzQkFBc0IsOEVBQVEsR0FBRztBQUNqSTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLDBEQUFVO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLDBEQUFZO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsb0ZBQWEsQ0FBQyxvRkFBYSxDQUFDLG9GQUFhLEdBQUcsa0RBQWtEO0FBQzNHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSDtBQUNBLENBQUMsQ0FBQyw0Q0FBUztBQUNYOztBQUVnSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXG5vZGVfbW9kdWxlc1xccmVhY3Qtc2VsZWN0XFxkaXN0XFxTZWxlY3QtYWFiMDI3ZjMuZXNtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tICdAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzJztcbmltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gJ0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDInO1xuaW1wb3J0IF9jbGFzc0NhbGxDaGVjayBmcm9tICdAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jbGFzc0NhbGxDaGVjayc7XG5pbXBvcnQgX2NyZWF0ZUNsYXNzIGZyb20gJ0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NyZWF0ZUNsYXNzJztcbmltcG9ydCBfaW5oZXJpdHMgZnJvbSAnQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vaW5oZXJpdHMnO1xuaW1wb3J0IF9jcmVhdGVTdXBlciBmcm9tICdAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jcmVhdGVTdXBlcic7XG5pbXBvcnQgX3RvQ29uc3VtYWJsZUFycmF5IGZyb20gJ0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5JztcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZU1lbW8sIEZyYWdtZW50LCB1c2VSZWYsIHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIENvbXBvbmVudCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHIgYXMgcmVtb3ZlUHJvcHMsIHMgYXMgc3VwcG9ydHNQYXNzaXZlRXZlbnRzLCBhIGFzIGNsZWFySW5kaWNhdG9yQ1NTLCBiIGFzIGNvbnRhaW5lckNTUywgZCBhcyBjc3MkMSwgZSBhcyBkcm9wZG93bkluZGljYXRvckNTUywgZyBhcyBncm91cENTUywgZiBhcyBncm91cEhlYWRpbmdDU1MsIGkgYXMgaW5kaWNhdG9yc0NvbnRhaW5lckNTUywgaCBhcyBpbmRpY2F0b3JTZXBhcmF0b3JDU1MsIGogYXMgaW5wdXRDU1MsIGwgYXMgbG9hZGluZ0luZGljYXRvckNTUywgayBhcyBsb2FkaW5nTWVzc2FnZUNTUywgbSBhcyBtZW51Q1NTLCBuIGFzIG1lbnVMaXN0Q1NTLCBvIGFzIG1lbnVQb3J0YWxDU1MsIHAgYXMgbXVsdGlWYWx1ZUNTUywgcSBhcyBtdWx0aVZhbHVlTGFiZWxDU1MsIHQgYXMgbXVsdGlWYWx1ZVJlbW92ZUNTUywgdSBhcyBub09wdGlvbnNNZXNzYWdlQ1NTLCB2IGFzIG9wdGlvbkNTUywgdyBhcyBwbGFjZWhvbGRlckNTUywgeCBhcyBjc3MkMiwgeSBhcyB2YWx1ZUNvbnRhaW5lckNTUywgeiBhcyBpc1RvdWNoQ2FwYWJsZSwgQSBhcyBpc01vYmlsZURldmljZSwgQiBhcyBtdWx0aVZhbHVlQXNWYWx1ZSwgQyBhcyBzaW5nbGVWYWx1ZUFzVmFsdWUsIEQgYXMgdmFsdWVUZXJuYXJ5LCBFIGFzIGNsYXNzTmFtZXMsIEYgYXMgZGVmYXVsdENvbXBvbmVudHMsIEcgYXMgaXNEb2N1bWVudEVsZW1lbnQsIEggYXMgY2xlYW5WYWx1ZSwgSSBhcyBzY3JvbGxJbnRvVmlldywgSiBhcyBub29wLCBNIGFzIE1lbnVQbGFjZXIsIEsgYXMgbm90TnVsbGlzaCB9IGZyb20gJy4vaW5kZXgtNjQxZWU1YjguZXNtLmpzJztcbmltcG9ydCB7IGpzeCwgY3NzIH0gZnJvbSAnQGVtb3Rpb24vcmVhY3QnO1xuaW1wb3J0IG1lbW9pemVPbmUgZnJvbSAnbWVtb2l6ZS1vbmUnO1xuaW1wb3J0IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyBmcm9tICdAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RXaXRob3V0UHJvcGVydGllcyc7XG5cbmZ1bmN0aW9uIF9FTU9USU9OX1NUUklOR0lGSUVEX0NTU19FUlJPUl9fJDIoKSB7IHJldHVybiBcIllvdSBoYXZlIHRyaWVkIHRvIHN0cmluZ2lmeSBvYmplY3QgcmV0dXJuZWQgZnJvbSBgY3NzYCBmdW5jdGlvbi4gSXQgaXNuJ3Qgc3VwcG9zZWQgdG8gYmUgdXNlZCBkaXJlY3RseSAoZS5nLiBhcyB2YWx1ZSBvZiB0aGUgYGNsYXNzTmFtZWAgcHJvcCksIGJ1dCByYXRoZXIgaGFuZGVkIHRvIGVtb3Rpb24gc28gaXQgY2FuIGhhbmRsZSBpdCAoZS5nLiBhcyB2YWx1ZSBvZiBgY3NzYCBwcm9wKS5cIjsgfVxuXG4vLyBBc3Npc3RpdmUgdGV4dCB0byBkZXNjcmliZSB2aXN1YWwgZWxlbWVudHMuIEhpZGRlbiBmb3Igc2lnaHRlZCB1c2Vycy5cbnZhciBfcmVmID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiID8ge1xuICBuYW1lOiBcIjdwZzBjai1hMTF5VGV4dFwiLFxuICBzdHlsZXM6IFwibGFiZWw6YTExeVRleHQ7ei1pbmRleDo5OTk5O2JvcmRlcjowO2NsaXA6cmVjdCgxcHgsIDFweCwgMXB4LCAxcHgpO2hlaWdodDoxcHg7d2lkdGg6MXB4O3Bvc2l0aW9uOmFic29sdXRlO292ZXJmbG93OmhpZGRlbjtwYWRkaW5nOjA7d2hpdGUtc3BhY2U6bm93cmFwXCJcbn0gOiB7XG4gIG5hbWU6IFwiMWY0M2F2ei1hMTF5VGV4dC1BMTF5VGV4dFwiLFxuICBzdHlsZXM6IFwibGFiZWw6YTExeVRleHQ7ei1pbmRleDo5OTk5O2JvcmRlcjowO2NsaXA6cmVjdCgxcHgsIDFweCwgMXB4LCAxcHgpO2hlaWdodDoxcHg7d2lkdGg6MXB4O3Bvc2l0aW9uOmFic29sdXRlO292ZXJmbG93OmhpZGRlbjtwYWRkaW5nOjA7d2hpdGUtc3BhY2U6bm93cmFwO2xhYmVsOkExMXlUZXh0O1wiLFxuICBtYXA6IFwiLyojIHNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTg7YmFzZTY0LGV5SjJaWEp6YVc5dUlqb3pMQ0p6YjNWeVkyVnpJanBiSWtFeE1YbFVaWGgwTG5SemVDSmRMQ0p1WVcxbGN5STZXMTBzSW0xaGNIQnBibWR6SWpvaVFVRlBTU0lzSW1acGJHVWlPaUpCTVRGNVZHVjRkQzUwYzNnaUxDSnpiM1Z5WTJWelEyOXVkR1Z1ZENJNld5SXZLaW9nUUdwemVDQnFjM2dnS2k5Y2JtbHRjRzl5ZENCN0lFcFRXQ0I5SUdaeWIyMGdKM0psWVdOMEp6dGNibWx0Y0c5eWRDQjdJR3B6ZUNCOUlHWnliMjBnSjBCbGJXOTBhVzl1TDNKbFlXTjBKenRjYmx4dUx5OGdRWE56YVhOMGFYWmxJSFJsZUhRZ2RHOGdaR1Z6WTNKcFltVWdkbWx6ZFdGc0lHVnNaVzFsYm5SekxpQklhV1JrWlc0Z1ptOXlJSE5wWjJoMFpXUWdkWE5sY25NdVhHNWpiMjV6ZENCQk1URjVWR1Y0ZENBOUlDaHdjbTl3Y3pvZ1NsTllMa2x1ZEhKcGJuTnBZMFZzWlcxbGJuUnpXeWR6Y0dGdUoxMHBJRDArSUNoY2JpQWdQSE53WVc1Y2JpQWdJQ0JqYzNNOWUzdGNiaUFnSUNBZ0lHeGhZbVZzT2lBbllURXhlVlJsZUhRbkxGeHVJQ0FnSUNBZ2VrbHVaR1Y0T2lBNU9UazVMRnh1SUNBZ0lDQWdZbTl5WkdWeU9pQXdMRnh1SUNBZ0lDQWdZMnhwY0RvZ0ozSmxZM1FvTVhCNExDQXhjSGdzSURGd2VDd2dNWEI0S1Njc1hHNGdJQ0FnSUNCb1pXbG5hSFE2SURFc1hHNGdJQ0FnSUNCM2FXUjBhRG9nTVN4Y2JpQWdJQ0FnSUhCdmMybDBhVzl1T2lBbllXSnpiMngxZEdVbkxGeHVJQ0FnSUNBZ2IzWmxjbVpzYjNjNklDZG9hV1JrWlc0bkxGeHVJQ0FnSUNBZ2NHRmtaR2x1WnpvZ01DeGNiaUFnSUNBZ0lIZG9hWFJsVTNCaFkyVTZJQ2R1YjNkeVlYQW5MRnh1SUNBZ0lIMTlYRzRnSUNBZ2V5NHVMbkJ5YjNCemZWeHVJQ0F2UGx4dUtUdGNibHh1Wlhod2IzSjBJR1JsWm1GMWJIUWdRVEV4ZVZSbGVIUTdYRzRpWFgwPSAqL1wiLFxuICB0b1N0cmluZzogX0VNT1RJT05fU1RSSU5HSUZJRURfQ1NTX0VSUk9SX18kMlxufTtcbnZhciBBMTF5VGV4dCA9IGZ1bmN0aW9uIEExMXlUZXh0KHByb3BzKSB7XG4gIHJldHVybiBqc3goXCJzcGFuXCIsIF9leHRlbmRzKHtcbiAgICBjc3M6IF9yZWZcbiAgfSwgcHJvcHMpKTtcbn07XG52YXIgQTExeVRleHQkMSA9IEExMXlUZXh0O1xuXG52YXIgZGVmYXVsdEFyaWFMaXZlTWVzc2FnZXMgPSB7XG4gIGd1aWRhbmNlOiBmdW5jdGlvbiBndWlkYW5jZShwcm9wcykge1xuICAgIHZhciBpc1NlYXJjaGFibGUgPSBwcm9wcy5pc1NlYXJjaGFibGUsXG4gICAgICBpc011bHRpID0gcHJvcHMuaXNNdWx0aSxcbiAgICAgIHRhYlNlbGVjdHNWYWx1ZSA9IHByb3BzLnRhYlNlbGVjdHNWYWx1ZSxcbiAgICAgIGNvbnRleHQgPSBwcm9wcy5jb250ZXh0LFxuICAgICAgaXNJbml0aWFsRm9jdXMgPSBwcm9wcy5pc0luaXRpYWxGb2N1cztcbiAgICBzd2l0Y2ggKGNvbnRleHQpIHtcbiAgICAgIGNhc2UgJ21lbnUnOlxuICAgICAgICByZXR1cm4gXCJVc2UgVXAgYW5kIERvd24gdG8gY2hvb3NlIG9wdGlvbnMsIHByZXNzIEVudGVyIHRvIHNlbGVjdCB0aGUgY3VycmVudGx5IGZvY3VzZWQgb3B0aW9uLCBwcmVzcyBFc2NhcGUgdG8gZXhpdCB0aGUgbWVudVwiLmNvbmNhdCh0YWJTZWxlY3RzVmFsdWUgPyAnLCBwcmVzcyBUYWIgdG8gc2VsZWN0IHRoZSBvcHRpb24gYW5kIGV4aXQgdGhlIG1lbnUnIDogJycsIFwiLlwiKTtcbiAgICAgIGNhc2UgJ2lucHV0JzpcbiAgICAgICAgcmV0dXJuIGlzSW5pdGlhbEZvY3VzID8gXCJcIi5jb25jYXQocHJvcHNbJ2FyaWEtbGFiZWwnXSB8fCAnU2VsZWN0JywgXCIgaXMgZm9jdXNlZCBcIikuY29uY2F0KGlzU2VhcmNoYWJsZSA/ICcsdHlwZSB0byByZWZpbmUgbGlzdCcgOiAnJywgXCIsIHByZXNzIERvd24gdG8gb3BlbiB0aGUgbWVudSwgXCIpLmNvbmNhdChpc011bHRpID8gJyBwcmVzcyBsZWZ0IHRvIGZvY3VzIHNlbGVjdGVkIHZhbHVlcycgOiAnJykgOiAnJztcbiAgICAgIGNhc2UgJ3ZhbHVlJzpcbiAgICAgICAgcmV0dXJuICdVc2UgbGVmdCBhbmQgcmlnaHQgdG8gdG9nZ2xlIGJldHdlZW4gZm9jdXNlZCB2YWx1ZXMsIHByZXNzIEJhY2tzcGFjZSB0byByZW1vdmUgdGhlIGN1cnJlbnRseSBmb2N1c2VkIHZhbHVlJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnJztcbiAgICB9XG4gIH0sXG4gIG9uQ2hhbmdlOiBmdW5jdGlvbiBvbkNoYW5nZShwcm9wcykge1xuICAgIHZhciBhY3Rpb24gPSBwcm9wcy5hY3Rpb24sXG4gICAgICBfcHJvcHMkbGFiZWwgPSBwcm9wcy5sYWJlbCxcbiAgICAgIGxhYmVsID0gX3Byb3BzJGxhYmVsID09PSB2b2lkIDAgPyAnJyA6IF9wcm9wcyRsYWJlbCxcbiAgICAgIGxhYmVscyA9IHByb3BzLmxhYmVscyxcbiAgICAgIGlzRGlzYWJsZWQgPSBwcm9wcy5pc0Rpc2FibGVkO1xuICAgIHN3aXRjaCAoYWN0aW9uKSB7XG4gICAgICBjYXNlICdkZXNlbGVjdC1vcHRpb24nOlxuICAgICAgY2FzZSAncG9wLXZhbHVlJzpcbiAgICAgIGNhc2UgJ3JlbW92ZS12YWx1ZSc6XG4gICAgICAgIHJldHVybiBcIm9wdGlvbiBcIi5jb25jYXQobGFiZWwsIFwiLCBkZXNlbGVjdGVkLlwiKTtcbiAgICAgIGNhc2UgJ2NsZWFyJzpcbiAgICAgICAgcmV0dXJuICdBbGwgc2VsZWN0ZWQgb3B0aW9ucyBoYXZlIGJlZW4gY2xlYXJlZC4nO1xuICAgICAgY2FzZSAnaW5pdGlhbC1pbnB1dC1mb2N1cyc6XG4gICAgICAgIHJldHVybiBcIm9wdGlvblwiLmNvbmNhdChsYWJlbHMubGVuZ3RoID4gMSA/ICdzJyA6ICcnLCBcIiBcIikuY29uY2F0KGxhYmVscy5qb2luKCcsJyksIFwiLCBzZWxlY3RlZC5cIik7XG4gICAgICBjYXNlICdzZWxlY3Qtb3B0aW9uJzpcbiAgICAgICAgcmV0dXJuIGlzRGlzYWJsZWQgPyBcIm9wdGlvbiBcIi5jb25jYXQobGFiZWwsIFwiIGlzIGRpc2FibGVkLiBTZWxlY3QgYW5vdGhlciBvcHRpb24uXCIpIDogXCJvcHRpb24gXCIuY29uY2F0KGxhYmVsLCBcIiwgc2VsZWN0ZWQuXCIpO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICcnO1xuICAgIH1cbiAgfSxcbiAgb25Gb2N1czogZnVuY3Rpb24gb25Gb2N1cyhwcm9wcykge1xuICAgIHZhciBjb250ZXh0ID0gcHJvcHMuY29udGV4dCxcbiAgICAgIGZvY3VzZWQgPSBwcm9wcy5mb2N1c2VkLFxuICAgICAgb3B0aW9ucyA9IHByb3BzLm9wdGlvbnMsXG4gICAgICBfcHJvcHMkbGFiZWwyID0gcHJvcHMubGFiZWwsXG4gICAgICBsYWJlbCA9IF9wcm9wcyRsYWJlbDIgPT09IHZvaWQgMCA/ICcnIDogX3Byb3BzJGxhYmVsMixcbiAgICAgIHNlbGVjdFZhbHVlID0gcHJvcHMuc2VsZWN0VmFsdWUsXG4gICAgICBpc0Rpc2FibGVkID0gcHJvcHMuaXNEaXNhYmxlZCxcbiAgICAgIGlzU2VsZWN0ZWQgPSBwcm9wcy5pc1NlbGVjdGVkLFxuICAgICAgaXNBcHBsZURldmljZSA9IHByb3BzLmlzQXBwbGVEZXZpY2U7XG4gICAgdmFyIGdldEFycmF5SW5kZXggPSBmdW5jdGlvbiBnZXRBcnJheUluZGV4KGFyciwgaXRlbSkge1xuICAgICAgcmV0dXJuIGFyciAmJiBhcnIubGVuZ3RoID8gXCJcIi5jb25jYXQoYXJyLmluZGV4T2YoaXRlbSkgKyAxLCBcIiBvZiBcIikuY29uY2F0KGFyci5sZW5ndGgpIDogJyc7XG4gICAgfTtcbiAgICBpZiAoY29udGV4dCA9PT0gJ3ZhbHVlJyAmJiBzZWxlY3RWYWx1ZSkge1xuICAgICAgcmV0dXJuIFwidmFsdWUgXCIuY29uY2F0KGxhYmVsLCBcIiBmb2N1c2VkLCBcIikuY29uY2F0KGdldEFycmF5SW5kZXgoc2VsZWN0VmFsdWUsIGZvY3VzZWQpLCBcIi5cIik7XG4gICAgfVxuICAgIGlmIChjb250ZXh0ID09PSAnbWVudScgJiYgaXNBcHBsZURldmljZSkge1xuICAgICAgdmFyIGRpc2FibGVkID0gaXNEaXNhYmxlZCA/ICcgZGlzYWJsZWQnIDogJyc7XG4gICAgICB2YXIgc3RhdHVzID0gXCJcIi5jb25jYXQoaXNTZWxlY3RlZCA/ICcgc2VsZWN0ZWQnIDogJycpLmNvbmNhdChkaXNhYmxlZCk7XG4gICAgICByZXR1cm4gXCJcIi5jb25jYXQobGFiZWwpLmNvbmNhdChzdGF0dXMsIFwiLCBcIikuY29uY2F0KGdldEFycmF5SW5kZXgob3B0aW9ucywgZm9jdXNlZCksIFwiLlwiKTtcbiAgICB9XG4gICAgcmV0dXJuICcnO1xuICB9LFxuICBvbkZpbHRlcjogZnVuY3Rpb24gb25GaWx0ZXIocHJvcHMpIHtcbiAgICB2YXIgaW5wdXRWYWx1ZSA9IHByb3BzLmlucHV0VmFsdWUsXG4gICAgICByZXN1bHRzTWVzc2FnZSA9IHByb3BzLnJlc3VsdHNNZXNzYWdlO1xuICAgIHJldHVybiBcIlwiLmNvbmNhdChyZXN1bHRzTWVzc2FnZSkuY29uY2F0KGlucHV0VmFsdWUgPyAnIGZvciBzZWFyY2ggdGVybSAnICsgaW5wdXRWYWx1ZSA6ICcnLCBcIi5cIik7XG4gIH1cbn07XG5cbnZhciBMaXZlUmVnaW9uID0gZnVuY3Rpb24gTGl2ZVJlZ2lvbihwcm9wcykge1xuICB2YXIgYXJpYVNlbGVjdGlvbiA9IHByb3BzLmFyaWFTZWxlY3Rpb24sXG4gICAgZm9jdXNlZE9wdGlvbiA9IHByb3BzLmZvY3VzZWRPcHRpb24sXG4gICAgZm9jdXNlZFZhbHVlID0gcHJvcHMuZm9jdXNlZFZhbHVlLFxuICAgIGZvY3VzYWJsZU9wdGlvbnMgPSBwcm9wcy5mb2N1c2FibGVPcHRpb25zLFxuICAgIGlzRm9jdXNlZCA9IHByb3BzLmlzRm9jdXNlZCxcbiAgICBzZWxlY3RWYWx1ZSA9IHByb3BzLnNlbGVjdFZhbHVlLFxuICAgIHNlbGVjdFByb3BzID0gcHJvcHMuc2VsZWN0UHJvcHMsXG4gICAgaWQgPSBwcm9wcy5pZCxcbiAgICBpc0FwcGxlRGV2aWNlID0gcHJvcHMuaXNBcHBsZURldmljZTtcbiAgdmFyIGFyaWFMaXZlTWVzc2FnZXMgPSBzZWxlY3RQcm9wcy5hcmlhTGl2ZU1lc3NhZ2VzLFxuICAgIGdldE9wdGlvbkxhYmVsID0gc2VsZWN0UHJvcHMuZ2V0T3B0aW9uTGFiZWwsXG4gICAgaW5wdXRWYWx1ZSA9IHNlbGVjdFByb3BzLmlucHV0VmFsdWUsXG4gICAgaXNNdWx0aSA9IHNlbGVjdFByb3BzLmlzTXVsdGksXG4gICAgaXNPcHRpb25EaXNhYmxlZCA9IHNlbGVjdFByb3BzLmlzT3B0aW9uRGlzYWJsZWQsXG4gICAgaXNTZWFyY2hhYmxlID0gc2VsZWN0UHJvcHMuaXNTZWFyY2hhYmxlLFxuICAgIG1lbnVJc09wZW4gPSBzZWxlY3RQcm9wcy5tZW51SXNPcGVuLFxuICAgIG9wdGlvbnMgPSBzZWxlY3RQcm9wcy5vcHRpb25zLFxuICAgIHNjcmVlblJlYWRlclN0YXR1cyA9IHNlbGVjdFByb3BzLnNjcmVlblJlYWRlclN0YXR1cyxcbiAgICB0YWJTZWxlY3RzVmFsdWUgPSBzZWxlY3RQcm9wcy50YWJTZWxlY3RzVmFsdWUsXG4gICAgaXNMb2FkaW5nID0gc2VsZWN0UHJvcHMuaXNMb2FkaW5nO1xuICB2YXIgYXJpYUxhYmVsID0gc2VsZWN0UHJvcHNbJ2FyaWEtbGFiZWwnXTtcbiAgdmFyIGFyaWFMaXZlID0gc2VsZWN0UHJvcHNbJ2FyaWEtbGl2ZSddO1xuXG4gIC8vIFVwZGF0ZSBhcmlhIGxpdmUgbWVzc2FnZSBjb25maWd1cmF0aW9uIHdoZW4gcHJvcCBjaGFuZ2VzXG4gIHZhciBtZXNzYWdlcyA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGRlZmF1bHRBcmlhTGl2ZU1lc3NhZ2VzKSwgYXJpYUxpdmVNZXNzYWdlcyB8fCB7fSk7XG4gIH0sIFthcmlhTGl2ZU1lc3NhZ2VzXSk7XG5cbiAgLy8gVXBkYXRlIGFyaWEgbGl2ZSBzZWxlY3RlZCBvcHRpb24gd2hlbiBwcm9wIGNoYW5nZXNcbiAgdmFyIGFyaWFTZWxlY3RlZCA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHZhciBtZXNzYWdlID0gJyc7XG4gICAgaWYgKGFyaWFTZWxlY3Rpb24gJiYgbWVzc2FnZXMub25DaGFuZ2UpIHtcbiAgICAgIHZhciBvcHRpb24gPSBhcmlhU2VsZWN0aW9uLm9wdGlvbixcbiAgICAgICAgc2VsZWN0ZWRPcHRpb25zID0gYXJpYVNlbGVjdGlvbi5vcHRpb25zLFxuICAgICAgICByZW1vdmVkVmFsdWUgPSBhcmlhU2VsZWN0aW9uLnJlbW92ZWRWYWx1ZSxcbiAgICAgICAgcmVtb3ZlZFZhbHVlcyA9IGFyaWFTZWxlY3Rpb24ucmVtb3ZlZFZhbHVlcyxcbiAgICAgICAgdmFsdWUgPSBhcmlhU2VsZWN0aW9uLnZhbHVlO1xuICAgICAgLy8gc2VsZWN0LW9wdGlvbiB3aGVuICFpc011bHRpIGRvZXMgbm90IHJldHVybiBvcHRpb24gc28gd2UgYXNzdW1lIHNlbGVjdGVkIG9wdGlvbiBpcyB2YWx1ZVxuICAgICAgdmFyIGFzT3B0aW9uID0gZnVuY3Rpb24gYXNPcHRpb24odmFsKSB7XG4gICAgICAgIHJldHVybiAhQXJyYXkuaXNBcnJheSh2YWwpID8gdmFsIDogbnVsbDtcbiAgICAgIH07XG5cbiAgICAgIC8vIElmIHRoZXJlIGlzIGp1c3Qgb25lIGl0ZW0gZnJvbSB0aGUgYWN0aW9uIHRoZW4gZ2V0IGl0cyBsYWJlbFxuICAgICAgdmFyIHNlbGVjdGVkID0gcmVtb3ZlZFZhbHVlIHx8IG9wdGlvbiB8fCBhc09wdGlvbih2YWx1ZSk7XG4gICAgICB2YXIgbGFiZWwgPSBzZWxlY3RlZCA/IGdldE9wdGlvbkxhYmVsKHNlbGVjdGVkKSA6ICcnO1xuXG4gICAgICAvLyBJZiB0aGVyZSBhcmUgbXVsdGlwbGUgaXRlbXMgZnJvbSB0aGUgYWN0aW9uIHRoZW4gcmV0dXJuIGFuIGFycmF5IG9mIGxhYmVsc1xuICAgICAgdmFyIG11bHRpU2VsZWN0ZWQgPSBzZWxlY3RlZE9wdGlvbnMgfHwgcmVtb3ZlZFZhbHVlcyB8fCB1bmRlZmluZWQ7XG4gICAgICB2YXIgbGFiZWxzID0gbXVsdGlTZWxlY3RlZCA/IG11bHRpU2VsZWN0ZWQubWFwKGdldE9wdGlvbkxhYmVsKSA6IFtdO1xuICAgICAgdmFyIG9uQ2hhbmdlUHJvcHMgPSBfb2JqZWN0U3ByZWFkKHtcbiAgICAgICAgLy8gbXVsdGlTZWxlY3RlZCBpdGVtcyBhcmUgdXN1YWxseSBpdGVtcyB0aGF0IGhhdmUgYWxyZWFkeSBiZWVuIHNlbGVjdGVkXG4gICAgICAgIC8vIG9yIHNldCBieSB0aGUgdXNlciBhcyBhIGRlZmF1bHQgdmFsdWUgc28gd2UgYXNzdW1lIHRoZXkgYXJlIG5vdCBkaXNhYmxlZFxuICAgICAgICBpc0Rpc2FibGVkOiBzZWxlY3RlZCAmJiBpc09wdGlvbkRpc2FibGVkKHNlbGVjdGVkLCBzZWxlY3RWYWx1ZSksXG4gICAgICAgIGxhYmVsOiBsYWJlbCxcbiAgICAgICAgbGFiZWxzOiBsYWJlbHNcbiAgICAgIH0sIGFyaWFTZWxlY3Rpb24pO1xuICAgICAgbWVzc2FnZSA9IG1lc3NhZ2VzLm9uQ2hhbmdlKG9uQ2hhbmdlUHJvcHMpO1xuICAgIH1cbiAgICByZXR1cm4gbWVzc2FnZTtcbiAgfSwgW2FyaWFTZWxlY3Rpb24sIG1lc3NhZ2VzLCBpc09wdGlvbkRpc2FibGVkLCBzZWxlY3RWYWx1ZSwgZ2V0T3B0aW9uTGFiZWxdKTtcbiAgdmFyIGFyaWFGb2N1c2VkID0gdXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgdmFyIGZvY3VzTXNnID0gJyc7XG4gICAgdmFyIGZvY3VzZWQgPSBmb2N1c2VkT3B0aW9uIHx8IGZvY3VzZWRWYWx1ZTtcbiAgICB2YXIgaXNTZWxlY3RlZCA9ICEhKGZvY3VzZWRPcHRpb24gJiYgc2VsZWN0VmFsdWUgJiYgc2VsZWN0VmFsdWUuaW5jbHVkZXMoZm9jdXNlZE9wdGlvbikpO1xuICAgIGlmIChmb2N1c2VkICYmIG1lc3NhZ2VzLm9uRm9jdXMpIHtcbiAgICAgIHZhciBvbkZvY3VzUHJvcHMgPSB7XG4gICAgICAgIGZvY3VzZWQ6IGZvY3VzZWQsXG4gICAgICAgIGxhYmVsOiBnZXRPcHRpb25MYWJlbChmb2N1c2VkKSxcbiAgICAgICAgaXNEaXNhYmxlZDogaXNPcHRpb25EaXNhYmxlZChmb2N1c2VkLCBzZWxlY3RWYWx1ZSksXG4gICAgICAgIGlzU2VsZWN0ZWQ6IGlzU2VsZWN0ZWQsXG4gICAgICAgIG9wdGlvbnM6IGZvY3VzYWJsZU9wdGlvbnMsXG4gICAgICAgIGNvbnRleHQ6IGZvY3VzZWQgPT09IGZvY3VzZWRPcHRpb24gPyAnbWVudScgOiAndmFsdWUnLFxuICAgICAgICBzZWxlY3RWYWx1ZTogc2VsZWN0VmFsdWUsXG4gICAgICAgIGlzQXBwbGVEZXZpY2U6IGlzQXBwbGVEZXZpY2VcbiAgICAgIH07XG4gICAgICBmb2N1c01zZyA9IG1lc3NhZ2VzLm9uRm9jdXMob25Gb2N1c1Byb3BzKTtcbiAgICB9XG4gICAgcmV0dXJuIGZvY3VzTXNnO1xuICB9LCBbZm9jdXNlZE9wdGlvbiwgZm9jdXNlZFZhbHVlLCBnZXRPcHRpb25MYWJlbCwgaXNPcHRpb25EaXNhYmxlZCwgbWVzc2FnZXMsIGZvY3VzYWJsZU9wdGlvbnMsIHNlbGVjdFZhbHVlLCBpc0FwcGxlRGV2aWNlXSk7XG4gIHZhciBhcmlhUmVzdWx0cyA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHZhciByZXN1bHRzTXNnID0gJyc7XG4gICAgaWYgKG1lbnVJc09wZW4gJiYgb3B0aW9ucy5sZW5ndGggJiYgIWlzTG9hZGluZyAmJiBtZXNzYWdlcy5vbkZpbHRlcikge1xuICAgICAgdmFyIHJlc3VsdHNNZXNzYWdlID0gc2NyZWVuUmVhZGVyU3RhdHVzKHtcbiAgICAgICAgY291bnQ6IGZvY3VzYWJsZU9wdGlvbnMubGVuZ3RoXG4gICAgICB9KTtcbiAgICAgIHJlc3VsdHNNc2cgPSBtZXNzYWdlcy5vbkZpbHRlcih7XG4gICAgICAgIGlucHV0VmFsdWU6IGlucHV0VmFsdWUsXG4gICAgICAgIHJlc3VsdHNNZXNzYWdlOiByZXN1bHRzTWVzc2FnZVxuICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHRzTXNnO1xuICB9LCBbZm9jdXNhYmxlT3B0aW9ucywgaW5wdXRWYWx1ZSwgbWVudUlzT3BlbiwgbWVzc2FnZXMsIG9wdGlvbnMsIHNjcmVlblJlYWRlclN0YXR1cywgaXNMb2FkaW5nXSk7XG4gIHZhciBpc0luaXRpYWxGb2N1cyA9IChhcmlhU2VsZWN0aW9uID09PSBudWxsIHx8IGFyaWFTZWxlY3Rpb24gPT09IHZvaWQgMCA/IHZvaWQgMCA6IGFyaWFTZWxlY3Rpb24uYWN0aW9uKSA9PT0gJ2luaXRpYWwtaW5wdXQtZm9jdXMnO1xuICB2YXIgYXJpYUd1aWRhbmNlID0gdXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgdmFyIGd1aWRhbmNlTXNnID0gJyc7XG4gICAgaWYgKG1lc3NhZ2VzLmd1aWRhbmNlKSB7XG4gICAgICB2YXIgY29udGV4dCA9IGZvY3VzZWRWYWx1ZSA/ICd2YWx1ZScgOiBtZW51SXNPcGVuID8gJ21lbnUnIDogJ2lucHV0JztcbiAgICAgIGd1aWRhbmNlTXNnID0gbWVzc2FnZXMuZ3VpZGFuY2Uoe1xuICAgICAgICAnYXJpYS1sYWJlbCc6IGFyaWFMYWJlbCxcbiAgICAgICAgY29udGV4dDogY29udGV4dCxcbiAgICAgICAgaXNEaXNhYmxlZDogZm9jdXNlZE9wdGlvbiAmJiBpc09wdGlvbkRpc2FibGVkKGZvY3VzZWRPcHRpb24sIHNlbGVjdFZhbHVlKSxcbiAgICAgICAgaXNNdWx0aTogaXNNdWx0aSxcbiAgICAgICAgaXNTZWFyY2hhYmxlOiBpc1NlYXJjaGFibGUsXG4gICAgICAgIHRhYlNlbGVjdHNWYWx1ZTogdGFiU2VsZWN0c1ZhbHVlLFxuICAgICAgICBpc0luaXRpYWxGb2N1czogaXNJbml0aWFsRm9jdXNcbiAgICAgIH0pO1xuICAgIH1cbiAgICByZXR1cm4gZ3VpZGFuY2VNc2c7XG4gIH0sIFthcmlhTGFiZWwsIGZvY3VzZWRPcHRpb24sIGZvY3VzZWRWYWx1ZSwgaXNNdWx0aSwgaXNPcHRpb25EaXNhYmxlZCwgaXNTZWFyY2hhYmxlLCBtZW51SXNPcGVuLCBtZXNzYWdlcywgc2VsZWN0VmFsdWUsIHRhYlNlbGVjdHNWYWx1ZSwgaXNJbml0aWFsRm9jdXNdKTtcbiAgdmFyIFNjcmVlblJlYWRlclRleHQgPSBqc3goRnJhZ21lbnQsIG51bGwsIGpzeChcInNwYW5cIiwge1xuICAgIGlkOiBcImFyaWEtc2VsZWN0aW9uXCJcbiAgfSwgYXJpYVNlbGVjdGVkKSwganN4KFwic3BhblwiLCB7XG4gICAgaWQ6IFwiYXJpYS1mb2N1c2VkXCJcbiAgfSwgYXJpYUZvY3VzZWQpLCBqc3goXCJzcGFuXCIsIHtcbiAgICBpZDogXCJhcmlhLXJlc3VsdHNcIlxuICB9LCBhcmlhUmVzdWx0cyksIGpzeChcInNwYW5cIiwge1xuICAgIGlkOiBcImFyaWEtZ3VpZGFuY2VcIlxuICB9LCBhcmlhR3VpZGFuY2UpKTtcbiAgcmV0dXJuIGpzeChGcmFnbWVudCwgbnVsbCwganN4KEExMXlUZXh0JDEsIHtcbiAgICBpZDogaWRcbiAgfSwgaXNJbml0aWFsRm9jdXMgJiYgU2NyZWVuUmVhZGVyVGV4dCksIGpzeChBMTF5VGV4dCQxLCB7XG4gICAgXCJhcmlhLWxpdmVcIjogYXJpYUxpdmUsXG4gICAgXCJhcmlhLWF0b21pY1wiOiBcImZhbHNlXCIsXG4gICAgXCJhcmlhLXJlbGV2YW50XCI6IFwiYWRkaXRpb25zIHRleHRcIixcbiAgICByb2xlOiBcImxvZ1wiXG4gIH0sIGlzRm9jdXNlZCAmJiAhaXNJbml0aWFsRm9jdXMgJiYgU2NyZWVuUmVhZGVyVGV4dCkpO1xufTtcbnZhciBMaXZlUmVnaW9uJDEgPSBMaXZlUmVnaW9uO1xuXG52YXIgZGlhY3JpdGljcyA9IFt7XG4gIGJhc2U6ICdBJyxcbiAgbGV0dGVyczogXCJBXFx1MjRCNlxcdUZGMjFcXHhDMFxceEMxXFx4QzJcXHUxRUE2XFx1MUVBNFxcdTFFQUFcXHUxRUE4XFx4QzNcXHUwMTAwXFx1MDEwMlxcdTFFQjBcXHUxRUFFXFx1MUVCNFxcdTFFQjJcXHUwMjI2XFx1MDFFMFxceEM0XFx1MDFERVxcdTFFQTJcXHhDNVxcdTAxRkFcXHUwMUNEXFx1MDIwMFxcdTAyMDJcXHUxRUEwXFx1MUVBQ1xcdTFFQjZcXHUxRTAwXFx1MDEwNFxcdTAyM0FcXHUyQzZGXCJcbn0sIHtcbiAgYmFzZTogJ0FBJyxcbiAgbGV0dGVyczogXCJcXHVBNzMyXCJcbn0sIHtcbiAgYmFzZTogJ0FFJyxcbiAgbGV0dGVyczogXCJcXHhDNlxcdTAxRkNcXHUwMUUyXCJcbn0sIHtcbiAgYmFzZTogJ0FPJyxcbiAgbGV0dGVyczogXCJcXHVBNzM0XCJcbn0sIHtcbiAgYmFzZTogJ0FVJyxcbiAgbGV0dGVyczogXCJcXHVBNzM2XCJcbn0sIHtcbiAgYmFzZTogJ0FWJyxcbiAgbGV0dGVyczogXCJcXHVBNzM4XFx1QTczQVwiXG59LCB7XG4gIGJhc2U6ICdBWScsXG4gIGxldHRlcnM6IFwiXFx1QTczQ1wiXG59LCB7XG4gIGJhc2U6ICdCJyxcbiAgbGV0dGVyczogXCJCXFx1MjRCN1xcdUZGMjJcXHUxRTAyXFx1MUUwNFxcdTFFMDZcXHUwMjQzXFx1MDE4MlxcdTAxODFcIlxufSwge1xuICBiYXNlOiAnQycsXG4gIGxldHRlcnM6IFwiQ1xcdTI0QjhcXHVGRjIzXFx1MDEwNlxcdTAxMDhcXHUwMTBBXFx1MDEwQ1xceEM3XFx1MUUwOFxcdTAxODdcXHUwMjNCXFx1QTczRVwiXG59LCB7XG4gIGJhc2U6ICdEJyxcbiAgbGV0dGVyczogXCJEXFx1MjRCOVxcdUZGMjRcXHUxRTBBXFx1MDEwRVxcdTFFMENcXHUxRTEwXFx1MUUxMlxcdTFFMEVcXHUwMTEwXFx1MDE4QlxcdTAxOEFcXHUwMTg5XFx1QTc3OVwiXG59LCB7XG4gIGJhc2U6ICdEWicsXG4gIGxldHRlcnM6IFwiXFx1MDFGMVxcdTAxQzRcIlxufSwge1xuICBiYXNlOiAnRHonLFxuICBsZXR0ZXJzOiBcIlxcdTAxRjJcXHUwMUM1XCJcbn0sIHtcbiAgYmFzZTogJ0UnLFxuICBsZXR0ZXJzOiBcIkVcXHUyNEJBXFx1RkYyNVxceEM4XFx4QzlcXHhDQVxcdTFFQzBcXHUxRUJFXFx1MUVDNFxcdTFFQzJcXHUxRUJDXFx1MDExMlxcdTFFMTRcXHUxRTE2XFx1MDExNFxcdTAxMTZcXHhDQlxcdTFFQkFcXHUwMTFBXFx1MDIwNFxcdTAyMDZcXHUxRUI4XFx1MUVDNlxcdTAyMjhcXHUxRTFDXFx1MDExOFxcdTFFMThcXHUxRTFBXFx1MDE5MFxcdTAxOEVcIlxufSwge1xuICBiYXNlOiAnRicsXG4gIGxldHRlcnM6IFwiRlxcdTI0QkJcXHVGRjI2XFx1MUUxRVxcdTAxOTFcXHVBNzdCXCJcbn0sIHtcbiAgYmFzZTogJ0cnLFxuICBsZXR0ZXJzOiBcIkdcXHUyNEJDXFx1RkYyN1xcdTAxRjRcXHUwMTFDXFx1MUUyMFxcdTAxMUVcXHUwMTIwXFx1MDFFNlxcdTAxMjJcXHUwMUU0XFx1MDE5M1xcdUE3QTBcXHVBNzdEXFx1QTc3RVwiXG59LCB7XG4gIGJhc2U6ICdIJyxcbiAgbGV0dGVyczogXCJIXFx1MjRCRFxcdUZGMjhcXHUwMTI0XFx1MUUyMlxcdTFFMjZcXHUwMjFFXFx1MUUyNFxcdTFFMjhcXHUxRTJBXFx1MDEyNlxcdTJDNjdcXHUyQzc1XFx1QTc4RFwiXG59LCB7XG4gIGJhc2U6ICdJJyxcbiAgbGV0dGVyczogXCJJXFx1MjRCRVxcdUZGMjlcXHhDQ1xceENEXFx4Q0VcXHUwMTI4XFx1MDEyQVxcdTAxMkNcXHUwMTMwXFx4Q0ZcXHUxRTJFXFx1MUVDOFxcdTAxQ0ZcXHUwMjA4XFx1MDIwQVxcdTFFQ0FcXHUwMTJFXFx1MUUyQ1xcdTAxOTdcIlxufSwge1xuICBiYXNlOiAnSicsXG4gIGxldHRlcnM6IFwiSlxcdTI0QkZcXHVGRjJBXFx1MDEzNFxcdTAyNDhcIlxufSwge1xuICBiYXNlOiAnSycsXG4gIGxldHRlcnM6IFwiS1xcdTI0QzBcXHVGRjJCXFx1MUUzMFxcdTAxRThcXHUxRTMyXFx1MDEzNlxcdTFFMzRcXHUwMTk4XFx1MkM2OVxcdUE3NDBcXHVBNzQyXFx1QTc0NFxcdUE3QTJcIlxufSwge1xuICBiYXNlOiAnTCcsXG4gIGxldHRlcnM6IFwiTFxcdTI0QzFcXHVGRjJDXFx1MDEzRlxcdTAxMzlcXHUwMTNEXFx1MUUzNlxcdTFFMzhcXHUwMTNCXFx1MUUzQ1xcdTFFM0FcXHUwMTQxXFx1MDIzRFxcdTJDNjJcXHUyQzYwXFx1QTc0OFxcdUE3NDZcXHVBNzgwXCJcbn0sIHtcbiAgYmFzZTogJ0xKJyxcbiAgbGV0dGVyczogXCJcXHUwMUM3XCJcbn0sIHtcbiAgYmFzZTogJ0xqJyxcbiAgbGV0dGVyczogXCJcXHUwMUM4XCJcbn0sIHtcbiAgYmFzZTogJ00nLFxuICBsZXR0ZXJzOiBcIk1cXHUyNEMyXFx1RkYyRFxcdTFFM0VcXHUxRTQwXFx1MUU0MlxcdTJDNkVcXHUwMTlDXCJcbn0sIHtcbiAgYmFzZTogJ04nLFxuICBsZXR0ZXJzOiBcIk5cXHUyNEMzXFx1RkYyRVxcdTAxRjhcXHUwMTQzXFx4RDFcXHUxRTQ0XFx1MDE0N1xcdTFFNDZcXHUwMTQ1XFx1MUU0QVxcdTFFNDhcXHUwMjIwXFx1MDE5RFxcdUE3OTBcXHVBN0E0XCJcbn0sIHtcbiAgYmFzZTogJ05KJyxcbiAgbGV0dGVyczogXCJcXHUwMUNBXCJcbn0sIHtcbiAgYmFzZTogJ05qJyxcbiAgbGV0dGVyczogXCJcXHUwMUNCXCJcbn0sIHtcbiAgYmFzZTogJ08nLFxuICBsZXR0ZXJzOiBcIk9cXHUyNEM0XFx1RkYyRlxceEQyXFx4RDNcXHhENFxcdTFFRDJcXHUxRUQwXFx1MUVENlxcdTFFRDRcXHhENVxcdTFFNENcXHUwMjJDXFx1MUU0RVxcdTAxNENcXHUxRTUwXFx1MUU1MlxcdTAxNEVcXHUwMjJFXFx1MDIzMFxceEQ2XFx1MDIyQVxcdTFFQ0VcXHUwMTUwXFx1MDFEMVxcdTAyMENcXHUwMjBFXFx1MDFBMFxcdTFFRENcXHUxRURBXFx1MUVFMFxcdTFFREVcXHUxRUUyXFx1MUVDQ1xcdTFFRDhcXHUwMUVBXFx1MDFFQ1xceEQ4XFx1MDFGRVxcdTAxODZcXHUwMTlGXFx1QTc0QVxcdUE3NENcIlxufSwge1xuICBiYXNlOiAnT0knLFxuICBsZXR0ZXJzOiBcIlxcdTAxQTJcIlxufSwge1xuICBiYXNlOiAnT08nLFxuICBsZXR0ZXJzOiBcIlxcdUE3NEVcIlxufSwge1xuICBiYXNlOiAnT1UnLFxuICBsZXR0ZXJzOiBcIlxcdTAyMjJcIlxufSwge1xuICBiYXNlOiAnUCcsXG4gIGxldHRlcnM6IFwiUFxcdTI0QzVcXHVGRjMwXFx1MUU1NFxcdTFFNTZcXHUwMUE0XFx1MkM2M1xcdUE3NTBcXHVBNzUyXFx1QTc1NFwiXG59LCB7XG4gIGJhc2U6ICdRJyxcbiAgbGV0dGVyczogXCJRXFx1MjRDNlxcdUZGMzFcXHVBNzU2XFx1QTc1OFxcdTAyNEFcIlxufSwge1xuICBiYXNlOiAnUicsXG4gIGxldHRlcnM6IFwiUlxcdTI0QzdcXHVGRjMyXFx1MDE1NFxcdTFFNThcXHUwMTU4XFx1MDIxMFxcdTAyMTJcXHUxRTVBXFx1MUU1Q1xcdTAxNTZcXHUxRTVFXFx1MDI0Q1xcdTJDNjRcXHVBNzVBXFx1QTdBNlxcdUE3ODJcIlxufSwge1xuICBiYXNlOiAnUycsXG4gIGxldHRlcnM6IFwiU1xcdTI0QzhcXHVGRjMzXFx1MUU5RVxcdTAxNUFcXHUxRTY0XFx1MDE1Q1xcdTFFNjBcXHUwMTYwXFx1MUU2NlxcdTFFNjJcXHUxRTY4XFx1MDIxOFxcdTAxNUVcXHUyQzdFXFx1QTdBOFxcdUE3ODRcIlxufSwge1xuICBiYXNlOiAnVCcsXG4gIGxldHRlcnM6IFwiVFxcdTI0QzlcXHVGRjM0XFx1MUU2QVxcdTAxNjRcXHUxRTZDXFx1MDIxQVxcdTAxNjJcXHUxRTcwXFx1MUU2RVxcdTAxNjZcXHUwMUFDXFx1MDFBRVxcdTAyM0VcXHVBNzg2XCJcbn0sIHtcbiAgYmFzZTogJ1RaJyxcbiAgbGV0dGVyczogXCJcXHVBNzI4XCJcbn0sIHtcbiAgYmFzZTogJ1UnLFxuICBsZXR0ZXJzOiBcIlVcXHUyNENBXFx1RkYzNVxceEQ5XFx4REFcXHhEQlxcdTAxNjhcXHUxRTc4XFx1MDE2QVxcdTFFN0FcXHUwMTZDXFx4RENcXHUwMURCXFx1MDFEN1xcdTAxRDVcXHUwMUQ5XFx1MUVFNlxcdTAxNkVcXHUwMTcwXFx1MDFEM1xcdTAyMTRcXHUwMjE2XFx1MDFBRlxcdTFFRUFcXHUxRUU4XFx1MUVFRVxcdTFFRUNcXHUxRUYwXFx1MUVFNFxcdTFFNzJcXHUwMTcyXFx1MUU3NlxcdTFFNzRcXHUwMjQ0XCJcbn0sIHtcbiAgYmFzZTogJ1YnLFxuICBsZXR0ZXJzOiBcIlZcXHUyNENCXFx1RkYzNlxcdTFFN0NcXHUxRTdFXFx1MDFCMlxcdUE3NUVcXHUwMjQ1XCJcbn0sIHtcbiAgYmFzZTogJ1ZZJyxcbiAgbGV0dGVyczogXCJcXHVBNzYwXCJcbn0sIHtcbiAgYmFzZTogJ1cnLFxuICBsZXR0ZXJzOiBcIldcXHUyNENDXFx1RkYzN1xcdTFFODBcXHUxRTgyXFx1MDE3NFxcdTFFODZcXHUxRTg0XFx1MUU4OFxcdTJDNzJcIlxufSwge1xuICBiYXNlOiAnWCcsXG4gIGxldHRlcnM6IFwiWFxcdTI0Q0RcXHVGRjM4XFx1MUU4QVxcdTFFOENcIlxufSwge1xuICBiYXNlOiAnWScsXG4gIGxldHRlcnM6IFwiWVxcdTI0Q0VcXHVGRjM5XFx1MUVGMlxceEREXFx1MDE3NlxcdTFFRjhcXHUwMjMyXFx1MUU4RVxcdTAxNzhcXHUxRUY2XFx1MUVGNFxcdTAxQjNcXHUwMjRFXFx1MUVGRVwiXG59LCB7XG4gIGJhc2U6ICdaJyxcbiAgbGV0dGVyczogXCJaXFx1MjRDRlxcdUZGM0FcXHUwMTc5XFx1MUU5MFxcdTAxN0JcXHUwMTdEXFx1MUU5MlxcdTFFOTRcXHUwMUI1XFx1MDIyNFxcdTJDN0ZcXHUyQzZCXFx1QTc2MlwiXG59LCB7XG4gIGJhc2U6ICdhJyxcbiAgbGV0dGVyczogXCJhXFx1MjREMFxcdUZGNDFcXHUxRTlBXFx4RTBcXHhFMVxceEUyXFx1MUVBN1xcdTFFQTVcXHUxRUFCXFx1MUVBOVxceEUzXFx1MDEwMVxcdTAxMDNcXHUxRUIxXFx1MUVBRlxcdTFFQjVcXHUxRUIzXFx1MDIyN1xcdTAxRTFcXHhFNFxcdTAxREZcXHUxRUEzXFx4RTVcXHUwMUZCXFx1MDFDRVxcdTAyMDFcXHUwMjAzXFx1MUVBMVxcdTFFQURcXHUxRUI3XFx1MUUwMVxcdTAxMDVcXHUyQzY1XFx1MDI1MFwiXG59LCB7XG4gIGJhc2U6ICdhYScsXG4gIGxldHRlcnM6IFwiXFx1QTczM1wiXG59LCB7XG4gIGJhc2U6ICdhZScsXG4gIGxldHRlcnM6IFwiXFx4RTZcXHUwMUZEXFx1MDFFM1wiXG59LCB7XG4gIGJhc2U6ICdhbycsXG4gIGxldHRlcnM6IFwiXFx1QTczNVwiXG59LCB7XG4gIGJhc2U6ICdhdScsXG4gIGxldHRlcnM6IFwiXFx1QTczN1wiXG59LCB7XG4gIGJhc2U6ICdhdicsXG4gIGxldHRlcnM6IFwiXFx1QTczOVxcdUE3M0JcIlxufSwge1xuICBiYXNlOiAnYXknLFxuICBsZXR0ZXJzOiBcIlxcdUE3M0RcIlxufSwge1xuICBiYXNlOiAnYicsXG4gIGxldHRlcnM6IFwiYlxcdTI0RDFcXHVGRjQyXFx1MUUwM1xcdTFFMDVcXHUxRTA3XFx1MDE4MFxcdTAxODNcXHUwMjUzXCJcbn0sIHtcbiAgYmFzZTogJ2MnLFxuICBsZXR0ZXJzOiBcImNcXHUyNEQyXFx1RkY0M1xcdTAxMDdcXHUwMTA5XFx1MDEwQlxcdTAxMERcXHhFN1xcdTFFMDlcXHUwMTg4XFx1MDIzQ1xcdUE3M0ZcXHUyMTg0XCJcbn0sIHtcbiAgYmFzZTogJ2QnLFxuICBsZXR0ZXJzOiBcImRcXHUyNEQzXFx1RkY0NFxcdTFFMEJcXHUwMTBGXFx1MUUwRFxcdTFFMTFcXHUxRTEzXFx1MUUwRlxcdTAxMTFcXHUwMThDXFx1MDI1NlxcdTAyNTdcXHVBNzdBXCJcbn0sIHtcbiAgYmFzZTogJ2R6JyxcbiAgbGV0dGVyczogXCJcXHUwMUYzXFx1MDFDNlwiXG59LCB7XG4gIGJhc2U6ICdlJyxcbiAgbGV0dGVyczogXCJlXFx1MjRENFxcdUZGNDVcXHhFOFxceEU5XFx4RUFcXHUxRUMxXFx1MUVCRlxcdTFFQzVcXHUxRUMzXFx1MUVCRFxcdTAxMTNcXHUxRTE1XFx1MUUxN1xcdTAxMTVcXHUwMTE3XFx4RUJcXHUxRUJCXFx1MDExQlxcdTAyMDVcXHUwMjA3XFx1MUVCOVxcdTFFQzdcXHUwMjI5XFx1MUUxRFxcdTAxMTlcXHUxRTE5XFx1MUUxQlxcdTAyNDdcXHUwMjVCXFx1MDFERFwiXG59LCB7XG4gIGJhc2U6ICdmJyxcbiAgbGV0dGVyczogXCJmXFx1MjRENVxcdUZGNDZcXHUxRTFGXFx1MDE5MlxcdUE3N0NcIlxufSwge1xuICBiYXNlOiAnZycsXG4gIGxldHRlcnM6IFwiZ1xcdTI0RDZcXHVGRjQ3XFx1MDFGNVxcdTAxMURcXHUxRTIxXFx1MDExRlxcdTAxMjFcXHUwMUU3XFx1MDEyM1xcdTAxRTVcXHUwMjYwXFx1QTdBMVxcdTFENzlcXHVBNzdGXCJcbn0sIHtcbiAgYmFzZTogJ2gnLFxuICBsZXR0ZXJzOiBcImhcXHUyNEQ3XFx1RkY0OFxcdTAxMjVcXHUxRTIzXFx1MUUyN1xcdTAyMUZcXHUxRTI1XFx1MUUyOVxcdTFFMkJcXHUxRTk2XFx1MDEyN1xcdTJDNjhcXHUyQzc2XFx1MDI2NVwiXG59LCB7XG4gIGJhc2U6ICdodicsXG4gIGxldHRlcnM6IFwiXFx1MDE5NVwiXG59LCB7XG4gIGJhc2U6ICdpJyxcbiAgbGV0dGVyczogXCJpXFx1MjREOFxcdUZGNDlcXHhFQ1xceEVEXFx4RUVcXHUwMTI5XFx1MDEyQlxcdTAxMkRcXHhFRlxcdTFFMkZcXHUxRUM5XFx1MDFEMFxcdTAyMDlcXHUwMjBCXFx1MUVDQlxcdTAxMkZcXHUxRTJEXFx1MDI2OFxcdTAxMzFcIlxufSwge1xuICBiYXNlOiAnaicsXG4gIGxldHRlcnM6IFwialxcdTI0RDlcXHVGRjRBXFx1MDEzNVxcdTAxRjBcXHUwMjQ5XCJcbn0sIHtcbiAgYmFzZTogJ2snLFxuICBsZXR0ZXJzOiBcImtcXHUyNERBXFx1RkY0QlxcdTFFMzFcXHUwMUU5XFx1MUUzM1xcdTAxMzdcXHUxRTM1XFx1MDE5OVxcdTJDNkFcXHVBNzQxXFx1QTc0M1xcdUE3NDVcXHVBN0EzXCJcbn0sIHtcbiAgYmFzZTogJ2wnLFxuICBsZXR0ZXJzOiBcImxcXHUyNERCXFx1RkY0Q1xcdTAxNDBcXHUwMTNBXFx1MDEzRVxcdTFFMzdcXHUxRTM5XFx1MDEzQ1xcdTFFM0RcXHUxRTNCXFx1MDE3RlxcdTAxNDJcXHUwMTlBXFx1MDI2QlxcdTJDNjFcXHVBNzQ5XFx1QTc4MVxcdUE3NDdcIlxufSwge1xuICBiYXNlOiAnbGonLFxuICBsZXR0ZXJzOiBcIlxcdTAxQzlcIlxufSwge1xuICBiYXNlOiAnbScsXG4gIGxldHRlcnM6IFwibVxcdTI0RENcXHVGRjREXFx1MUUzRlxcdTFFNDFcXHUxRTQzXFx1MDI3MVxcdTAyNkZcIlxufSwge1xuICBiYXNlOiAnbicsXG4gIGxldHRlcnM6IFwiblxcdTI0RERcXHVGRjRFXFx1MDFGOVxcdTAxNDRcXHhGMVxcdTFFNDVcXHUwMTQ4XFx1MUU0N1xcdTAxNDZcXHUxRTRCXFx1MUU0OVxcdTAxOUVcXHUwMjcyXFx1MDE0OVxcdUE3OTFcXHVBN0E1XCJcbn0sIHtcbiAgYmFzZTogJ25qJyxcbiAgbGV0dGVyczogXCJcXHUwMUNDXCJcbn0sIHtcbiAgYmFzZTogJ28nLFxuICBsZXR0ZXJzOiBcIm9cXHUyNERFXFx1RkY0RlxceEYyXFx4RjNcXHhGNFxcdTFFRDNcXHUxRUQxXFx1MUVEN1xcdTFFRDVcXHhGNVxcdTFFNERcXHUwMjJEXFx1MUU0RlxcdTAxNERcXHUxRTUxXFx1MUU1M1xcdTAxNEZcXHUwMjJGXFx1MDIzMVxceEY2XFx1MDIyQlxcdTFFQ0ZcXHUwMTUxXFx1MDFEMlxcdTAyMERcXHUwMjBGXFx1MDFBMVxcdTFFRERcXHUxRURCXFx1MUVFMVxcdTFFREZcXHUxRUUzXFx1MUVDRFxcdTFFRDlcXHUwMUVCXFx1MDFFRFxceEY4XFx1MDFGRlxcdTAyNTRcXHVBNzRCXFx1QTc0RFxcdTAyNzVcIlxufSwge1xuICBiYXNlOiAnb2knLFxuICBsZXR0ZXJzOiBcIlxcdTAxQTNcIlxufSwge1xuICBiYXNlOiAnb3UnLFxuICBsZXR0ZXJzOiBcIlxcdTAyMjNcIlxufSwge1xuICBiYXNlOiAnb28nLFxuICBsZXR0ZXJzOiBcIlxcdUE3NEZcIlxufSwge1xuICBiYXNlOiAncCcsXG4gIGxldHRlcnM6IFwicFxcdTI0REZcXHVGRjUwXFx1MUU1NVxcdTFFNTdcXHUwMUE1XFx1MUQ3RFxcdUE3NTFcXHVBNzUzXFx1QTc1NVwiXG59LCB7XG4gIGJhc2U6ICdxJyxcbiAgbGV0dGVyczogXCJxXFx1MjRFMFxcdUZGNTFcXHUwMjRCXFx1QTc1N1xcdUE3NTlcIlxufSwge1xuICBiYXNlOiAncicsXG4gIGxldHRlcnM6IFwiclxcdTI0RTFcXHVGRjUyXFx1MDE1NVxcdTFFNTlcXHUwMTU5XFx1MDIxMVxcdTAyMTNcXHUxRTVCXFx1MUU1RFxcdTAxNTdcXHUxRTVGXFx1MDI0RFxcdTAyN0RcXHVBNzVCXFx1QTdBN1xcdUE3ODNcIlxufSwge1xuICBiYXNlOiAncycsXG4gIGxldHRlcnM6IFwic1xcdTI0RTJcXHVGRjUzXFx4REZcXHUwMTVCXFx1MUU2NVxcdTAxNURcXHUxRTYxXFx1MDE2MVxcdTFFNjdcXHUxRTYzXFx1MUU2OVxcdTAyMTlcXHUwMTVGXFx1MDIzRlxcdUE3QTlcXHVBNzg1XFx1MUU5QlwiXG59LCB7XG4gIGJhc2U6ICd0JyxcbiAgbGV0dGVyczogXCJ0XFx1MjRFM1xcdUZGNTRcXHUxRTZCXFx1MUU5N1xcdTAxNjVcXHUxRTZEXFx1MDIxQlxcdTAxNjNcXHUxRTcxXFx1MUU2RlxcdTAxNjdcXHUwMUFEXFx1MDI4OFxcdTJDNjZcXHVBNzg3XCJcbn0sIHtcbiAgYmFzZTogJ3R6JyxcbiAgbGV0dGVyczogXCJcXHVBNzI5XCJcbn0sIHtcbiAgYmFzZTogJ3UnLFxuICBsZXR0ZXJzOiBcInVcXHUyNEU0XFx1RkY1NVxceEY5XFx4RkFcXHhGQlxcdTAxNjlcXHUxRTc5XFx1MDE2QlxcdTFFN0JcXHUwMTZEXFx4RkNcXHUwMURDXFx1MDFEOFxcdTAxRDZcXHUwMURBXFx1MUVFN1xcdTAxNkZcXHUwMTcxXFx1MDFENFxcdTAyMTVcXHUwMjE3XFx1MDFCMFxcdTFFRUJcXHUxRUU5XFx1MUVFRlxcdTFFRURcXHUxRUYxXFx1MUVFNVxcdTFFNzNcXHUwMTczXFx1MUU3N1xcdTFFNzVcXHUwMjg5XCJcbn0sIHtcbiAgYmFzZTogJ3YnLFxuICBsZXR0ZXJzOiBcInZcXHUyNEU1XFx1RkY1NlxcdTFFN0RcXHUxRTdGXFx1MDI4QlxcdUE3NUZcXHUwMjhDXCJcbn0sIHtcbiAgYmFzZTogJ3Z5JyxcbiAgbGV0dGVyczogXCJcXHVBNzYxXCJcbn0sIHtcbiAgYmFzZTogJ3cnLFxuICBsZXR0ZXJzOiBcIndcXHUyNEU2XFx1RkY1N1xcdTFFODFcXHUxRTgzXFx1MDE3NVxcdTFFODdcXHUxRTg1XFx1MUU5OFxcdTFFODlcXHUyQzczXCJcbn0sIHtcbiAgYmFzZTogJ3gnLFxuICBsZXR0ZXJzOiBcInhcXHUyNEU3XFx1RkY1OFxcdTFFOEJcXHUxRThEXCJcbn0sIHtcbiAgYmFzZTogJ3knLFxuICBsZXR0ZXJzOiBcInlcXHUyNEU4XFx1RkY1OVxcdTFFRjNcXHhGRFxcdTAxNzdcXHUxRUY5XFx1MDIzM1xcdTFFOEZcXHhGRlxcdTFFRjdcXHUxRTk5XFx1MUVGNVxcdTAxQjRcXHUwMjRGXFx1MUVGRlwiXG59LCB7XG4gIGJhc2U6ICd6JyxcbiAgbGV0dGVyczogXCJ6XFx1MjRFOVxcdUZGNUFcXHUwMTdBXFx1MUU5MVxcdTAxN0NcXHUwMTdFXFx1MUU5M1xcdTFFOTVcXHUwMUI2XFx1MDIyNVxcdTAyNDBcXHUyQzZDXFx1QTc2M1wiXG59XTtcbnZhciBhbnlEaWFjcml0aWMgPSBuZXcgUmVnRXhwKCdbJyArIGRpYWNyaXRpY3MubWFwKGZ1bmN0aW9uIChkKSB7XG4gIHJldHVybiBkLmxldHRlcnM7XG59KS5qb2luKCcnKSArICddJywgJ2cnKTtcbnZhciBkaWFjcml0aWNUb0Jhc2UgPSB7fTtcbmZvciAodmFyIGkgPSAwOyBpIDwgZGlhY3JpdGljcy5sZW5ndGg7IGkrKykge1xuICB2YXIgZGlhY3JpdGljID0gZGlhY3JpdGljc1tpXTtcbiAgZm9yICh2YXIgaiA9IDA7IGogPCBkaWFjcml0aWMubGV0dGVycy5sZW5ndGg7IGorKykge1xuICAgIGRpYWNyaXRpY1RvQmFzZVtkaWFjcml0aWMubGV0dGVyc1tqXV0gPSBkaWFjcml0aWMuYmFzZTtcbiAgfVxufVxudmFyIHN0cmlwRGlhY3JpdGljcyA9IGZ1bmN0aW9uIHN0cmlwRGlhY3JpdGljcyhzdHIpIHtcbiAgcmV0dXJuIHN0ci5yZXBsYWNlKGFueURpYWNyaXRpYywgZnVuY3Rpb24gKG1hdGNoKSB7XG4gICAgcmV0dXJuIGRpYWNyaXRpY1RvQmFzZVttYXRjaF07XG4gIH0pO1xufTtcblxudmFyIG1lbW9pemVkU3RyaXBEaWFjcml0aWNzRm9ySW5wdXQgPSBtZW1vaXplT25lKHN0cmlwRGlhY3JpdGljcyk7XG52YXIgdHJpbVN0cmluZyA9IGZ1bmN0aW9uIHRyaW1TdHJpbmcoc3RyKSB7XG4gIHJldHVybiBzdHIucmVwbGFjZSgvXlxccyt8XFxzKyQvZywgJycpO1xufTtcbnZhciBkZWZhdWx0U3RyaW5naWZ5ID0gZnVuY3Rpb24gZGVmYXVsdFN0cmluZ2lmeShvcHRpb24pIHtcbiAgcmV0dXJuIFwiXCIuY29uY2F0KG9wdGlvbi5sYWJlbCwgXCIgXCIpLmNvbmNhdChvcHRpb24udmFsdWUpO1xufTtcbnZhciBjcmVhdGVGaWx0ZXIgPSBmdW5jdGlvbiBjcmVhdGVGaWx0ZXIoY29uZmlnKSB7XG4gIHJldHVybiBmdW5jdGlvbiAob3B0aW9uLCByYXdJbnB1dCkge1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby11bmRlcnNjb3JlLWRhbmdsZVxuICAgIGlmIChvcHRpb24uZGF0YS5fX2lzTmV3X18pIHJldHVybiB0cnVlO1xuICAgIHZhciBfaWdub3JlQ2FzZSRpZ25vcmVBY2MgPSBfb2JqZWN0U3ByZWFkKHtcbiAgICAgICAgaWdub3JlQ2FzZTogdHJ1ZSxcbiAgICAgICAgaWdub3JlQWNjZW50czogdHJ1ZSxcbiAgICAgICAgc3RyaW5naWZ5OiBkZWZhdWx0U3RyaW5naWZ5LFxuICAgICAgICB0cmltOiB0cnVlLFxuICAgICAgICBtYXRjaEZyb206ICdhbnknXG4gICAgICB9LCBjb25maWcpLFxuICAgICAgaWdub3JlQ2FzZSA9IF9pZ25vcmVDYXNlJGlnbm9yZUFjYy5pZ25vcmVDYXNlLFxuICAgICAgaWdub3JlQWNjZW50cyA9IF9pZ25vcmVDYXNlJGlnbm9yZUFjYy5pZ25vcmVBY2NlbnRzLFxuICAgICAgc3RyaW5naWZ5ID0gX2lnbm9yZUNhc2UkaWdub3JlQWNjLnN0cmluZ2lmeSxcbiAgICAgIHRyaW0gPSBfaWdub3JlQ2FzZSRpZ25vcmVBY2MudHJpbSxcbiAgICAgIG1hdGNoRnJvbSA9IF9pZ25vcmVDYXNlJGlnbm9yZUFjYy5tYXRjaEZyb207XG4gICAgdmFyIGlucHV0ID0gdHJpbSA/IHRyaW1TdHJpbmcocmF3SW5wdXQpIDogcmF3SW5wdXQ7XG4gICAgdmFyIGNhbmRpZGF0ZSA9IHRyaW0gPyB0cmltU3RyaW5nKHN0cmluZ2lmeShvcHRpb24pKSA6IHN0cmluZ2lmeShvcHRpb24pO1xuICAgIGlmIChpZ25vcmVDYXNlKSB7XG4gICAgICBpbnB1dCA9IGlucHV0LnRvTG93ZXJDYXNlKCk7XG4gICAgICBjYW5kaWRhdGUgPSBjYW5kaWRhdGUudG9Mb3dlckNhc2UoKTtcbiAgICB9XG4gICAgaWYgKGlnbm9yZUFjY2VudHMpIHtcbiAgICAgIGlucHV0ID0gbWVtb2l6ZWRTdHJpcERpYWNyaXRpY3NGb3JJbnB1dChpbnB1dCk7XG4gICAgICBjYW5kaWRhdGUgPSBzdHJpcERpYWNyaXRpY3MoY2FuZGlkYXRlKTtcbiAgICB9XG4gICAgcmV0dXJuIG1hdGNoRnJvbSA9PT0gJ3N0YXJ0JyA/IGNhbmRpZGF0ZS5zdWJzdHIoMCwgaW5wdXQubGVuZ3RoKSA9PT0gaW5wdXQgOiBjYW5kaWRhdGUuaW5kZXhPZihpbnB1dCkgPiAtMTtcbiAgfTtcbn07XG5cbnZhciBfZXhjbHVkZWQgPSBbXCJpbm5lclJlZlwiXTtcbmZ1bmN0aW9uIER1bW15SW5wdXQoX3JlZikge1xuICB2YXIgaW5uZXJSZWYgPSBfcmVmLmlubmVyUmVmLFxuICAgIHByb3BzID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzKF9yZWYsIF9leGNsdWRlZCk7XG4gIC8vIFJlbW92ZSBhbmltYXRpb24gcHJvcHMgbm90IG1lYW50IGZvciBIVE1MIGVsZW1lbnRzXG4gIHZhciBmaWx0ZXJlZFByb3BzID0gcmVtb3ZlUHJvcHMocHJvcHMsICdvbkV4aXRlZCcsICdpbicsICdlbnRlcicsICdleGl0JywgJ2FwcGVhcicpO1xuICByZXR1cm4ganN4KFwiaW5wdXRcIiwgX2V4dGVuZHMoe1xuICAgIHJlZjogaW5uZXJSZWZcbiAgfSwgZmlsdGVyZWRQcm9wcywge1xuICAgIGNzczogLyojX19QVVJFX18qL2Nzcyh7XG4gICAgICBsYWJlbDogJ2R1bW15SW5wdXQnLFxuICAgICAgLy8gZ2V0IHJpZCBvZiBhbnkgZGVmYXVsdCBzdHlsZXNcbiAgICAgIGJhY2tncm91bmQ6IDAsXG4gICAgICBib3JkZXI6IDAsXG4gICAgICAvLyBpbXBvcnRhbnQhIHRoaXMgaGlkZXMgdGhlIGZsYXNoaW5nIGN1cnNvclxuICAgICAgY2FyZXRDb2xvcjogJ3RyYW5zcGFyZW50JyxcbiAgICAgIGZvbnRTaXplOiAnaW5oZXJpdCcsXG4gICAgICBncmlkQXJlYTogJzEgLyAxIC8gMiAvIDMnLFxuICAgICAgb3V0bGluZTogMCxcbiAgICAgIHBhZGRpbmc6IDAsXG4gICAgICAvLyBpbXBvcnRhbnQhIHdpdGhvdXQgYHdpZHRoYCBicm93c2VycyB3b24ndCBhbGxvdyBmb2N1c1xuICAgICAgd2lkdGg6IDEsXG4gICAgICAvLyByZW1vdmUgY3Vyc29yIG9uIGRlc2t0b3BcbiAgICAgIGNvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgLy8gcmVtb3ZlIGN1cnNvciBvbiBtb2JpbGUgd2hpbHN0IG1haW50YWluaW5nIFwic2Nyb2xsIGludG8gdmlld1wiIGJlaGF2aW91clxuICAgICAgbGVmdDogLTEwMCxcbiAgICAgIG9wYWNpdHk6IDAsXG4gICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJyxcbiAgICAgIHRyYW5zZm9ybTogJ3NjYWxlKC4wMSknXG4gICAgfSwgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiID8gXCJcIiA6IFwiO2xhYmVsOkR1bW15SW5wdXQ7XCIsIHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIiA/IFwiXCIgOiBcIi8qIyBzb3VyY2VNYXBwaW5nVVJMPWRhdGE6YXBwbGljYXRpb24vanNvbjtjaGFyc2V0PXV0Zi04O2Jhc2U2NCxleUoyWlhKemFXOXVJam96TENKemIzVnlZMlZ6SWpwYklrUjFiVzE1U1c1d2RYUXVkSE40SWwwc0ltNWhiV1Z6SWpwYlhTd2liV0Z3Y0dsdVozTWlPaUpCUVhsQ1RTSXNJbVpwYkdVaU9pSkVkVzF0ZVVsdWNIVjBMblJ6ZUNJc0luTnZkWEpqWlhORGIyNTBaVzUwSWpwYklpOHFLaUJBYW5ONElHcHplQ0FxTDF4dWFXMXdiM0owSUhzZ1NsTllMQ0JTWldZZ2ZTQm1jbTl0SUNkeVpXRmpkQ2M3WEc1cGJYQnZjblFnZXlCcWMzZ2dmU0JtY205dElDZEFaVzF2ZEdsdmJpOXlaV0ZqZENjN1hHNXBiWEJ2Y25RZ2V5QnlaVzF2ZG1WUWNtOXdjeUI5SUdaeWIyMGdKeTR1TDNWMGFXeHpKenRjYmx4dVpYaHdiM0owSUdSbFptRjFiSFFnWm5WdVkzUnBiMjRnUkhWdGJYbEpibkIxZENoN1hHNGdJR2x1Ym1WeVVtVm1MRnh1SUNBdUxpNXdjbTl3YzF4dWZUb2dTbE5ZTGtsdWRISnBibk5wWTBWc1pXMWxiblJ6V3lkcGJuQjFkQ2RkSUNZZ2UxeHVJQ0J5WldGa2IyNXNlU0JwYm01bGNsSmxaam9nVW1WbVBFaFVUVXhKYm5CMWRFVnNaVzFsYm5RK08xeHVmU2tnZTF4dUlDQXZMeUJTWlcxdmRtVWdZVzVwYldGMGFXOXVJSEJ5YjNCeklHNXZkQ0J0WldGdWRDQm1iM0lnU0ZSTlRDQmxiR1Z0Wlc1MGMxeHVJQ0JqYjI1emRDQm1hV3gwWlhKbFpGQnliM0J6SUQwZ2NtVnRiM1psVUhKdmNITW9YRzRnSUNBZ2NISnZjSE1zWEc0Z0lDQWdKMjl1UlhocGRHVmtKeXhjYmlBZ0lDQW5hVzRuTEZ4dUlDQWdJQ2RsYm5SbGNpY3NYRzRnSUNBZ0oyVjRhWFFuTEZ4dUlDQWdJQ2RoY0hCbFlYSW5YRzRnSUNrN1hHNWNiaUFnY21WMGRYSnVJQ2hjYmlBZ0lDQThhVzV3ZFhSY2JpQWdJQ0FnSUhKbFpqMTdhVzV1WlhKU1pXWjlYRzRnSUNBZ0lDQjdMaTR1Wm1sc2RHVnlaV1JRY205d2MzMWNiaUFnSUNBZ0lHTnpjejE3ZTF4dUlDQWdJQ0FnSUNCc1lXSmxiRG9nSjJSMWJXMTVTVzV3ZFhRbkxGeHVJQ0FnSUNBZ0lDQXZMeUJuWlhRZ2NtbGtJRzltSUdGdWVTQmtaV1poZFd4MElITjBlV3hsYzF4dUlDQWdJQ0FnSUNCaVlXTnJaM0p2ZFc1a09pQXdMRnh1SUNBZ0lDQWdJQ0JpYjNKa1pYSTZJREFzWEc0Z0lDQWdJQ0FnSUM4dklHbHRjRzl5ZEdGdWRDRWdkR2hwY3lCb2FXUmxjeUIwYUdVZ1pteGhjMmhwYm1jZ1kzVnljMjl5WEc0Z0lDQWdJQ0FnSUdOaGNtVjBRMjlzYjNJNklDZDBjbUZ1YzNCaGNtVnVkQ2NzWEc0Z0lDQWdJQ0FnSUdadmJuUlRhWHBsT2lBbmFXNW9aWEpwZENjc1hHNGdJQ0FnSUNBZ0lHZHlhV1JCY21WaE9pQW5NU0F2SURFZ0x5QXlJQzhnTXljc1hHNGdJQ0FnSUNBZ0lHOTFkR3hwYm1VNklEQXNYRzRnSUNBZ0lDQWdJSEJoWkdScGJtYzZJREFzWEc0Z0lDQWdJQ0FnSUM4dklHbHRjRzl5ZEdGdWRDRWdkMmwwYUc5MWRDQmdkMmxrZEdoZ0lHSnliM2R6WlhKeklIZHZiaWQwSUdGc2JHOTNJR1p2WTNWelhHNGdJQ0FnSUNBZ0lIZHBaSFJvT2lBeExGeHVYRzRnSUNBZ0lDQWdJQzh2SUhKbGJXOTJaU0JqZFhKemIzSWdiMjRnWkdWemEzUnZjRnh1SUNBZ0lDQWdJQ0JqYjJ4dmNqb2dKM1J5WVc1emNHRnlaVzUwSnl4Y2JseHVJQ0FnSUNBZ0lDQXZMeUJ5WlcxdmRtVWdZM1Z5YzI5eUlHOXVJRzF2WW1sc1pTQjNhR2xzYzNRZ2JXRnBiblJoYVc1cGJtY2dYQ0p6WTNKdmJHd2dhVzUwYnlCMmFXVjNYQ0lnWW1Wb1lYWnBiM1Z5WEc0Z0lDQWdJQ0FnSUd4bFpuUTZJQzB4TURBc1hHNGdJQ0FnSUNBZ0lHOXdZV05wZEhrNklEQXNYRzRnSUNBZ0lDQWdJSEJ2YzJsMGFXOXVPaUFuY21Wc1lYUnBkbVVuTEZ4dUlDQWdJQ0FnSUNCMGNtRnVjMlp2Y20wNklDZHpZMkZzWlNndU1ERXBKeXhjYmlBZ0lDQWdJSDE5WEc0Z0lDQWdMejVjYmlBZ0tUdGNibjFjYmlKZGZRPT0gKi9cIilcbiAgfSkpO1xufVxuXG52YXIgY2FuY2VsU2Nyb2xsID0gZnVuY3Rpb24gY2FuY2VsU2Nyb2xsKGV2ZW50KSB7XG4gIGlmIChldmVudC5jYW5jZWxhYmxlKSBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICBldmVudC5zdG9wUHJvcGFnYXRpb24oKTtcbn07XG5mdW5jdGlvbiB1c2VTY3JvbGxDYXB0dXJlKF9yZWYpIHtcbiAgdmFyIGlzRW5hYmxlZCA9IF9yZWYuaXNFbmFibGVkLFxuICAgIG9uQm90dG9tQXJyaXZlID0gX3JlZi5vbkJvdHRvbUFycml2ZSxcbiAgICBvbkJvdHRvbUxlYXZlID0gX3JlZi5vbkJvdHRvbUxlYXZlLFxuICAgIG9uVG9wQXJyaXZlID0gX3JlZi5vblRvcEFycml2ZSxcbiAgICBvblRvcExlYXZlID0gX3JlZi5vblRvcExlYXZlO1xuICB2YXIgaXNCb3R0b20gPSB1c2VSZWYoZmFsc2UpO1xuICB2YXIgaXNUb3AgPSB1c2VSZWYoZmFsc2UpO1xuICB2YXIgdG91Y2hTdGFydCA9IHVzZVJlZigwKTtcbiAgdmFyIHNjcm9sbFRhcmdldCA9IHVzZVJlZihudWxsKTtcbiAgdmFyIGhhbmRsZUV2ZW50RGVsdGEgPSB1c2VDYWxsYmFjayhmdW5jdGlvbiAoZXZlbnQsIGRlbHRhKSB7XG4gICAgaWYgKHNjcm9sbFRhcmdldC5jdXJyZW50ID09PSBudWxsKSByZXR1cm47XG4gICAgdmFyIF9zY3JvbGxUYXJnZXQkY3VycmVudCA9IHNjcm9sbFRhcmdldC5jdXJyZW50LFxuICAgICAgc2Nyb2xsVG9wID0gX3Njcm9sbFRhcmdldCRjdXJyZW50LnNjcm9sbFRvcCxcbiAgICAgIHNjcm9sbEhlaWdodCA9IF9zY3JvbGxUYXJnZXQkY3VycmVudC5zY3JvbGxIZWlnaHQsXG4gICAgICBjbGllbnRIZWlnaHQgPSBfc2Nyb2xsVGFyZ2V0JGN1cnJlbnQuY2xpZW50SGVpZ2h0O1xuICAgIHZhciB0YXJnZXQgPSBzY3JvbGxUYXJnZXQuY3VycmVudDtcbiAgICB2YXIgaXNEZWx0YVBvc2l0aXZlID0gZGVsdGEgPiAwO1xuICAgIHZhciBhdmFpbGFibGVTY3JvbGwgPSBzY3JvbGxIZWlnaHQgLSBjbGllbnRIZWlnaHQgLSBzY3JvbGxUb3A7XG4gICAgdmFyIHNob3VsZENhbmNlbFNjcm9sbCA9IGZhbHNlO1xuXG4gICAgLy8gcmVzZXQgYm90dG9tL3RvcCBmbGFnc1xuICAgIGlmIChhdmFpbGFibGVTY3JvbGwgPiBkZWx0YSAmJiBpc0JvdHRvbS5jdXJyZW50KSB7XG4gICAgICBpZiAob25Cb3R0b21MZWF2ZSkgb25Cb3R0b21MZWF2ZShldmVudCk7XG4gICAgICBpc0JvdHRvbS5jdXJyZW50ID0gZmFsc2U7XG4gICAgfVxuICAgIGlmIChpc0RlbHRhUG9zaXRpdmUgJiYgaXNUb3AuY3VycmVudCkge1xuICAgICAgaWYgKG9uVG9wTGVhdmUpIG9uVG9wTGVhdmUoZXZlbnQpO1xuICAgICAgaXNUb3AuY3VycmVudCA9IGZhbHNlO1xuICAgIH1cblxuICAgIC8vIGJvdHRvbSBsaW1pdFxuICAgIGlmIChpc0RlbHRhUG9zaXRpdmUgJiYgZGVsdGEgPiBhdmFpbGFibGVTY3JvbGwpIHtcbiAgICAgIGlmIChvbkJvdHRvbUFycml2ZSAmJiAhaXNCb3R0b20uY3VycmVudCkge1xuICAgICAgICBvbkJvdHRvbUFycml2ZShldmVudCk7XG4gICAgICB9XG4gICAgICB0YXJnZXQuc2Nyb2xsVG9wID0gc2Nyb2xsSGVpZ2h0O1xuICAgICAgc2hvdWxkQ2FuY2VsU2Nyb2xsID0gdHJ1ZTtcbiAgICAgIGlzQm90dG9tLmN1cnJlbnQgPSB0cnVlO1xuXG4gICAgICAvLyB0b3AgbGltaXRcbiAgICB9IGVsc2UgaWYgKCFpc0RlbHRhUG9zaXRpdmUgJiYgLWRlbHRhID4gc2Nyb2xsVG9wKSB7XG4gICAgICBpZiAob25Ub3BBcnJpdmUgJiYgIWlzVG9wLmN1cnJlbnQpIHtcbiAgICAgICAgb25Ub3BBcnJpdmUoZXZlbnQpO1xuICAgICAgfVxuICAgICAgdGFyZ2V0LnNjcm9sbFRvcCA9IDA7XG4gICAgICBzaG91bGRDYW5jZWxTY3JvbGwgPSB0cnVlO1xuICAgICAgaXNUb3AuY3VycmVudCA9IHRydWU7XG4gICAgfVxuXG4gICAgLy8gY2FuY2VsIHNjcm9sbFxuICAgIGlmIChzaG91bGRDYW5jZWxTY3JvbGwpIHtcbiAgICAgIGNhbmNlbFNjcm9sbChldmVudCk7XG4gICAgfVxuICB9LCBbb25Cb3R0b21BcnJpdmUsIG9uQm90dG9tTGVhdmUsIG9uVG9wQXJyaXZlLCBvblRvcExlYXZlXSk7XG4gIHZhciBvbldoZWVsID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKGV2ZW50KSB7XG4gICAgaGFuZGxlRXZlbnREZWx0YShldmVudCwgZXZlbnQuZGVsdGFZKTtcbiAgfSwgW2hhbmRsZUV2ZW50RGVsdGFdKTtcbiAgdmFyIG9uVG91Y2hTdGFydCA9IHVzZUNhbGxiYWNrKGZ1bmN0aW9uIChldmVudCkge1xuICAgIC8vIHNldCB0b3VjaCBzdGFydCBzbyB3ZSBjYW4gY2FsY3VsYXRlIHRvdWNobW92ZSBkZWx0YVxuICAgIHRvdWNoU3RhcnQuY3VycmVudCA9IGV2ZW50LmNoYW5nZWRUb3VjaGVzWzBdLmNsaWVudFk7XG4gIH0sIFtdKTtcbiAgdmFyIG9uVG91Y2hNb3ZlID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKGV2ZW50KSB7XG4gICAgdmFyIGRlbHRhWSA9IHRvdWNoU3RhcnQuY3VycmVudCAtIGV2ZW50LmNoYW5nZWRUb3VjaGVzWzBdLmNsaWVudFk7XG4gICAgaGFuZGxlRXZlbnREZWx0YShldmVudCwgZGVsdGFZKTtcbiAgfSwgW2hhbmRsZUV2ZW50RGVsdGFdKTtcbiAgdmFyIHN0YXJ0TGlzdGVuaW5nID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKGVsKSB7XG4gICAgLy8gYmFpbCBlYXJseSBpZiBubyBlbGVtZW50IGlzIGF2YWlsYWJsZSB0byBhdHRhY2ggdG9cbiAgICBpZiAoIWVsKSByZXR1cm47XG4gICAgdmFyIG5vdFBhc3NpdmUgPSBzdXBwb3J0c1Bhc3NpdmVFdmVudHMgPyB7XG4gICAgICBwYXNzaXZlOiBmYWxzZVxuICAgIH0gOiBmYWxzZTtcbiAgICBlbC5hZGRFdmVudExpc3RlbmVyKCd3aGVlbCcsIG9uV2hlZWwsIG5vdFBhc3NpdmUpO1xuICAgIGVsLmFkZEV2ZW50TGlzdGVuZXIoJ3RvdWNoc3RhcnQnLCBvblRvdWNoU3RhcnQsIG5vdFBhc3NpdmUpO1xuICAgIGVsLmFkZEV2ZW50TGlzdGVuZXIoJ3RvdWNobW92ZScsIG9uVG91Y2hNb3ZlLCBub3RQYXNzaXZlKTtcbiAgfSwgW29uVG91Y2hNb3ZlLCBvblRvdWNoU3RhcnQsIG9uV2hlZWxdKTtcbiAgdmFyIHN0b3BMaXN0ZW5pbmcgPSB1c2VDYWxsYmFjayhmdW5jdGlvbiAoZWwpIHtcbiAgICAvLyBiYWlsIGVhcmx5IGlmIG5vIGVsZW1lbnQgaXMgYXZhaWxhYmxlIHRvIGRldGFjaCBmcm9tXG4gICAgaWYgKCFlbCkgcmV0dXJuO1xuICAgIGVsLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3doZWVsJywgb25XaGVlbCwgZmFsc2UpO1xuICAgIGVsLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3RvdWNoc3RhcnQnLCBvblRvdWNoU3RhcnQsIGZhbHNlKTtcbiAgICBlbC5yZW1vdmVFdmVudExpc3RlbmVyKCd0b3VjaG1vdmUnLCBvblRvdWNoTW92ZSwgZmFsc2UpO1xuICB9LCBbb25Ub3VjaE1vdmUsIG9uVG91Y2hTdGFydCwgb25XaGVlbF0pO1xuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmICghaXNFbmFibGVkKSByZXR1cm47XG4gICAgdmFyIGVsZW1lbnQgPSBzY3JvbGxUYXJnZXQuY3VycmVudDtcbiAgICBzdGFydExpc3RlbmluZyhlbGVtZW50KTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgc3RvcExpc3RlbmluZyhlbGVtZW50KTtcbiAgICB9O1xuICB9LCBbaXNFbmFibGVkLCBzdGFydExpc3RlbmluZywgc3RvcExpc3RlbmluZ10pO1xuICByZXR1cm4gZnVuY3Rpb24gKGVsZW1lbnQpIHtcbiAgICBzY3JvbGxUYXJnZXQuY3VycmVudCA9IGVsZW1lbnQ7XG4gIH07XG59XG5cbnZhciBTVFlMRV9LRVlTID0gWydib3hTaXppbmcnLCAnaGVpZ2h0JywgJ292ZXJmbG93JywgJ3BhZGRpbmdSaWdodCcsICdwb3NpdGlvbiddO1xudmFyIExPQ0tfU1RZTEVTID0ge1xuICBib3hTaXppbmc6ICdib3JkZXItYm94JyxcbiAgLy8gYWNjb3VudCBmb3IgcG9zc2libGUgZGVjbGFyYXRpb24gYHdpZHRoOiAxMDAlO2Agb24gYm9keVxuICBvdmVyZmxvdzogJ2hpZGRlbicsXG4gIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxuICBoZWlnaHQ6ICcxMDAlJ1xufTtcbmZ1bmN0aW9uIHByZXZlbnRUb3VjaE1vdmUoZSkge1xuICBpZiAoZS5jYW5jZWxhYmxlKSBlLnByZXZlbnREZWZhdWx0KCk7XG59XG5mdW5jdGlvbiBhbGxvd1RvdWNoTW92ZShlKSB7XG4gIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG59XG5mdW5jdGlvbiBwcmV2ZW50SW5lcnRpYVNjcm9sbCgpIHtcbiAgdmFyIHRvcCA9IHRoaXMuc2Nyb2xsVG9wO1xuICB2YXIgdG90YWxTY3JvbGwgPSB0aGlzLnNjcm9sbEhlaWdodDtcbiAgdmFyIGN1cnJlbnRTY3JvbGwgPSB0b3AgKyB0aGlzLm9mZnNldEhlaWdodDtcbiAgaWYgKHRvcCA9PT0gMCkge1xuICAgIHRoaXMuc2Nyb2xsVG9wID0gMTtcbiAgfSBlbHNlIGlmIChjdXJyZW50U2Nyb2xsID09PSB0b3RhbFNjcm9sbCkge1xuICAgIHRoaXMuc2Nyb2xsVG9wID0gdG9wIC0gMTtcbiAgfVxufVxuXG4vLyBgb250b3VjaHN0YXJ0YCBjaGVjayB3b3JrcyBvbiBtb3N0IGJyb3dzZXJzXG4vLyBgbWF4VG91Y2hQb2ludHNgIHdvcmtzIG9uIElFMTAvMTEgYW5kIFN1cmZhY2VcbmZ1bmN0aW9uIGlzVG91Y2hEZXZpY2UoKSB7XG4gIHJldHVybiAnb250b3VjaHN0YXJ0JyBpbiB3aW5kb3cgfHwgbmF2aWdhdG9yLm1heFRvdWNoUG9pbnRzO1xufVxudmFyIGNhblVzZURPTSA9ICEhKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5kb2N1bWVudCAmJiB3aW5kb3cuZG9jdW1lbnQuY3JlYXRlRWxlbWVudCk7XG52YXIgYWN0aXZlU2Nyb2xsTG9ja3MgPSAwO1xudmFyIGxpc3RlbmVyT3B0aW9ucyA9IHtcbiAgY2FwdHVyZTogZmFsc2UsXG4gIHBhc3NpdmU6IGZhbHNlXG59O1xuZnVuY3Rpb24gdXNlU2Nyb2xsTG9jayhfcmVmKSB7XG4gIHZhciBpc0VuYWJsZWQgPSBfcmVmLmlzRW5hYmxlZCxcbiAgICBfcmVmJGFjY291bnRGb3JTY3JvbGwgPSBfcmVmLmFjY291bnRGb3JTY3JvbGxiYXJzLFxuICAgIGFjY291bnRGb3JTY3JvbGxiYXJzID0gX3JlZiRhY2NvdW50Rm9yU2Nyb2xsID09PSB2b2lkIDAgPyB0cnVlIDogX3JlZiRhY2NvdW50Rm9yU2Nyb2xsO1xuICB2YXIgb3JpZ2luYWxTdHlsZXMgPSB1c2VSZWYoe30pO1xuICB2YXIgc2Nyb2xsVGFyZ2V0ID0gdXNlUmVmKG51bGwpO1xuICB2YXIgYWRkU2Nyb2xsTG9jayA9IHVzZUNhbGxiYWNrKGZ1bmN0aW9uICh0b3VjaFNjcm9sbFRhcmdldCkge1xuICAgIGlmICghY2FuVXNlRE9NKSByZXR1cm47XG4gICAgdmFyIHRhcmdldCA9IGRvY3VtZW50LmJvZHk7XG4gICAgdmFyIHRhcmdldFN0eWxlID0gdGFyZ2V0ICYmIHRhcmdldC5zdHlsZTtcbiAgICBpZiAoYWNjb3VudEZvclNjcm9sbGJhcnMpIHtcbiAgICAgIC8vIHN0b3JlIGFueSBzdHlsZXMgYWxyZWFkeSBhcHBsaWVkIHRvIHRoZSBib2R5XG4gICAgICBTVFlMRV9LRVlTLmZvckVhY2goZnVuY3Rpb24gKGtleSkge1xuICAgICAgICB2YXIgdmFsID0gdGFyZ2V0U3R5bGUgJiYgdGFyZ2V0U3R5bGVba2V5XTtcbiAgICAgICAgb3JpZ2luYWxTdHlsZXMuY3VycmVudFtrZXldID0gdmFsO1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gYXBwbHkgdGhlIGxvY2sgc3R5bGVzIGFuZCBwYWRkaW5nIGlmIHRoaXMgaXMgdGhlIGZpcnN0IHNjcm9sbCBsb2NrXG4gICAgaWYgKGFjY291bnRGb3JTY3JvbGxiYXJzICYmIGFjdGl2ZVNjcm9sbExvY2tzIDwgMSkge1xuICAgICAgdmFyIGN1cnJlbnRQYWRkaW5nID0gcGFyc2VJbnQob3JpZ2luYWxTdHlsZXMuY3VycmVudC5wYWRkaW5nUmlnaHQsIDEwKSB8fCAwO1xuICAgICAgdmFyIGNsaWVudFdpZHRoID0gZG9jdW1lbnQuYm9keSA/IGRvY3VtZW50LmJvZHkuY2xpZW50V2lkdGggOiAwO1xuICAgICAgdmFyIGFkanVzdGVkUGFkZGluZyA9IHdpbmRvdy5pbm5lcldpZHRoIC0gY2xpZW50V2lkdGggKyBjdXJyZW50UGFkZGluZyB8fCAwO1xuICAgICAgT2JqZWN0LmtleXMoTE9DS19TVFlMRVMpLmZvckVhY2goZnVuY3Rpb24gKGtleSkge1xuICAgICAgICB2YXIgdmFsID0gTE9DS19TVFlMRVNba2V5XTtcbiAgICAgICAgaWYgKHRhcmdldFN0eWxlKSB7XG4gICAgICAgICAgdGFyZ2V0U3R5bGVba2V5XSA9IHZhbDtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICBpZiAodGFyZ2V0U3R5bGUpIHtcbiAgICAgICAgdGFyZ2V0U3R5bGUucGFkZGluZ1JpZ2h0ID0gXCJcIi5jb25jYXQoYWRqdXN0ZWRQYWRkaW5nLCBcInB4XCIpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIGFjY291bnQgZm9yIHRvdWNoIGRldmljZXNcbiAgICBpZiAodGFyZ2V0ICYmIGlzVG91Y2hEZXZpY2UoKSkge1xuICAgICAgLy8gTW9iaWxlIFNhZmFyaSBpZ25vcmVzIHsgb3ZlcmZsb3c6IGhpZGRlbiB9IGRlY2xhcmF0aW9uIG9uIHRoZSBib2R5LlxuICAgICAgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIoJ3RvdWNobW92ZScsIHByZXZlbnRUb3VjaE1vdmUsIGxpc3RlbmVyT3B0aW9ucyk7XG5cbiAgICAgIC8vIEFsbG93IHNjcm9sbCBvbiBwcm92aWRlZCB0YXJnZXRcbiAgICAgIGlmICh0b3VjaFNjcm9sbFRhcmdldCkge1xuICAgICAgICB0b3VjaFNjcm9sbFRhcmdldC5hZGRFdmVudExpc3RlbmVyKCd0b3VjaHN0YXJ0JywgcHJldmVudEluZXJ0aWFTY3JvbGwsIGxpc3RlbmVyT3B0aW9ucyk7XG4gICAgICAgIHRvdWNoU2Nyb2xsVGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIoJ3RvdWNobW92ZScsIGFsbG93VG91Y2hNb3ZlLCBsaXN0ZW5lck9wdGlvbnMpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIGluY3JlbWVudCBhY3RpdmUgc2Nyb2xsIGxvY2tzXG4gICAgYWN0aXZlU2Nyb2xsTG9ja3MgKz0gMTtcbiAgfSwgW2FjY291bnRGb3JTY3JvbGxiYXJzXSk7XG4gIHZhciByZW1vdmVTY3JvbGxMb2NrID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKHRvdWNoU2Nyb2xsVGFyZ2V0KSB7XG4gICAgaWYgKCFjYW5Vc2VET00pIHJldHVybjtcbiAgICB2YXIgdGFyZ2V0ID0gZG9jdW1lbnQuYm9keTtcbiAgICB2YXIgdGFyZ2V0U3R5bGUgPSB0YXJnZXQgJiYgdGFyZ2V0LnN0eWxlO1xuXG4gICAgLy8gc2FmZWx5IGRlY3JlbWVudCBhY3RpdmUgc2Nyb2xsIGxvY2tzXG4gICAgYWN0aXZlU2Nyb2xsTG9ja3MgPSBNYXRoLm1heChhY3RpdmVTY3JvbGxMb2NrcyAtIDEsIDApO1xuXG4gICAgLy8gcmVhcHBseSBvcmlnaW5hbCBib2R5IHN0eWxlcywgaWYgYW55XG4gICAgaWYgKGFjY291bnRGb3JTY3JvbGxiYXJzICYmIGFjdGl2ZVNjcm9sbExvY2tzIDwgMSkge1xuICAgICAgU1RZTEVfS0VZUy5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHtcbiAgICAgICAgdmFyIHZhbCA9IG9yaWdpbmFsU3R5bGVzLmN1cnJlbnRba2V5XTtcbiAgICAgICAgaWYgKHRhcmdldFN0eWxlKSB7XG4gICAgICAgICAgdGFyZ2V0U3R5bGVba2V5XSA9IHZhbDtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gcmVtb3ZlIHRvdWNoIGxpc3RlbmVyc1xuICAgIGlmICh0YXJnZXQgJiYgaXNUb3VjaERldmljZSgpKSB7XG4gICAgICB0YXJnZXQucmVtb3ZlRXZlbnRMaXN0ZW5lcigndG91Y2htb3ZlJywgcHJldmVudFRvdWNoTW92ZSwgbGlzdGVuZXJPcHRpb25zKTtcbiAgICAgIGlmICh0b3VjaFNjcm9sbFRhcmdldCkge1xuICAgICAgICB0b3VjaFNjcm9sbFRhcmdldC5yZW1vdmVFdmVudExpc3RlbmVyKCd0b3VjaHN0YXJ0JywgcHJldmVudEluZXJ0aWFTY3JvbGwsIGxpc3RlbmVyT3B0aW9ucyk7XG4gICAgICAgIHRvdWNoU2Nyb2xsVGFyZ2V0LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3RvdWNobW92ZScsIGFsbG93VG91Y2hNb3ZlLCBsaXN0ZW5lck9wdGlvbnMpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW2FjY291bnRGb3JTY3JvbGxiYXJzXSk7XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKCFpc0VuYWJsZWQpIHJldHVybjtcbiAgICB2YXIgZWxlbWVudCA9IHNjcm9sbFRhcmdldC5jdXJyZW50O1xuICAgIGFkZFNjcm9sbExvY2soZWxlbWVudCk7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIHJlbW92ZVNjcm9sbExvY2soZWxlbWVudCk7XG4gICAgfTtcbiAgfSwgW2lzRW5hYmxlZCwgYWRkU2Nyb2xsTG9jaywgcmVtb3ZlU2Nyb2xsTG9ja10pO1xuICByZXR1cm4gZnVuY3Rpb24gKGVsZW1lbnQpIHtcbiAgICBzY3JvbGxUYXJnZXQuY3VycmVudCA9IGVsZW1lbnQ7XG4gIH07XG59XG5cbmZ1bmN0aW9uIF9FTU9USU9OX1NUUklOR0lGSUVEX0NTU19FUlJPUl9fJDEoKSB7IHJldHVybiBcIllvdSBoYXZlIHRyaWVkIHRvIHN0cmluZ2lmeSBvYmplY3QgcmV0dXJuZWQgZnJvbSBgY3NzYCBmdW5jdGlvbi4gSXQgaXNuJ3Qgc3VwcG9zZWQgdG8gYmUgdXNlZCBkaXJlY3RseSAoZS5nLiBhcyB2YWx1ZSBvZiB0aGUgYGNsYXNzTmFtZWAgcHJvcCksIGJ1dCByYXRoZXIgaGFuZGVkIHRvIGVtb3Rpb24gc28gaXQgY2FuIGhhbmRsZSBpdCAoZS5nLiBhcyB2YWx1ZSBvZiBgY3NzYCBwcm9wKS5cIjsgfVxudmFyIGJsdXJTZWxlY3RJbnB1dCA9IGZ1bmN0aW9uIGJsdXJTZWxlY3RJbnB1dChldmVudCkge1xuICB2YXIgZWxlbWVudCA9IGV2ZW50LnRhcmdldDtcbiAgcmV0dXJuIGVsZW1lbnQub3duZXJEb2N1bWVudC5hY3RpdmVFbGVtZW50ICYmIGVsZW1lbnQub3duZXJEb2N1bWVudC5hY3RpdmVFbGVtZW50LmJsdXIoKTtcbn07XG52YXIgX3JlZjIkMSA9IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIiA/IHtcbiAgbmFtZTogXCIxa2ZkYjBlXCIsXG4gIHN0eWxlczogXCJwb3NpdGlvbjpmaXhlZDtsZWZ0OjA7Ym90dG9tOjA7cmlnaHQ6MDt0b3A6MFwiXG59IDoge1xuICBuYW1lOiBcImJwOGN1YS1TY3JvbGxNYW5hZ2VyXCIsXG4gIHN0eWxlczogXCJwb3NpdGlvbjpmaXhlZDtsZWZ0OjA7Ym90dG9tOjA7cmlnaHQ6MDt0b3A6MDtsYWJlbDpTY3JvbGxNYW5hZ2VyO1wiLFxuICBtYXA6IFwiLyojIHNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTg7YmFzZTY0LGV5SjJaWEp6YVc5dUlqb3pMQ0p6YjNWeVkyVnpJanBiSWxOamNtOXNiRTFoYm1GblpYSXVkSE40SWwwc0ltNWhiV1Z6SWpwYlhTd2liV0Z3Y0dsdVozTWlPaUpCUVc5RVZTSXNJbVpwYkdVaU9pSlRZM0p2Ykd4TllXNWhaMlZ5TG5SemVDSXNJbk52ZFhKalpYTkRiMjUwWlc1MElqcGJJaThxS2lCQWFuTjRJR3B6ZUNBcUwxeHVhVzF3YjNKMElIc2dhbk40SUgwZ1puSnZiU0FuUUdWdGIzUnBiMjR2Y21WaFkzUW5PMXh1YVcxd2IzSjBJSHNnUm5KaFoyMWxiblFzSUZKbFlXTjBSV3hsYldWdWRDd2dVbVZtUTJGc2JHSmhZMnNzSUUxdmRYTmxSWFpsYm5RZ2ZTQm1jbTl0SUNkeVpXRmpkQ2M3WEc1cGJYQnZjblFnZFhObFUyTnliMnhzUTJGd2RIVnlaU0JtY205dElDY3VMM1Z6WlZOamNtOXNiRU5oY0hSMWNtVW5PMXh1YVcxd2IzSjBJSFZ6WlZOamNtOXNiRXh2WTJzZ1puSnZiU0FuTGk5MWMyVlRZM0p2Ykd4TWIyTnJKenRjYmx4dWFXNTBaWEptWVdObElGQnliM0J6SUh0Y2JpQWdjbVZoWkc5dWJIa2dZMmhwYkdSeVpXNDZJQ2h5WldZNklGSmxaa05oYkd4aVlXTnJQRWhVVFV4RmJHVnRaVzUwUGlrZ1BUNGdVbVZoWTNSRmJHVnRaVzUwTzF4dUlDQnlaV0ZrYjI1c2VTQnNiMk5yUlc1aFlteGxaRG9nWW05dmJHVmhianRjYmlBZ2NtVmhaRzl1YkhrZ1kyRndkSFZ5WlVWdVlXSnNaV1E2SUdKdmIyeGxZVzQ3WEc0Z0lISmxZV1J2Ym14NUlHOXVRbTkwZEc5dFFYSnlhWFpsUHpvZ0tHVjJaVzUwT2lCWGFHVmxiRVYyWlc1MElId2dWRzkxWTJoRmRtVnVkQ2tnUFQ0Z2RtOXBaRHRjYmlBZ2NtVmhaRzl1YkhrZ2IyNUNiM1IwYjIxTVpXRjJaVDg2SUNobGRtVnVkRG9nVjJobFpXeEZkbVZ1ZENCOElGUnZkV05vUlhabGJuUXBJRDArSUhadmFXUTdYRzRnSUhKbFlXUnZibXg1SUc5dVZHOXdRWEp5YVhabFB6b2dLR1YyWlc1ME9pQlhhR1ZsYkVWMlpXNTBJSHdnVkc5MVkyaEZkbVZ1ZENrZ1BUNGdkbTlwWkR0Y2JpQWdjbVZoWkc5dWJIa2diMjVVYjNCTVpXRjJaVDg2SUNobGRtVnVkRG9nVjJobFpXeEZkbVZ1ZENCOElGUnZkV05vUlhabGJuUXBJRDArSUhadmFXUTdYRzU5WEc1Y2JtTnZibk4wSUdKc2RYSlRaV3hsWTNSSmJuQjFkQ0E5SUNobGRtVnVkRG9nVFc5MWMyVkZkbVZ1ZER4SVZFMU1SR2wyUld4bGJXVnVkRDRwSUQwK0lIdGNiaUFnWTI5dWMzUWdaV3hsYldWdWRDQTlJR1YyWlc1MExuUmhjbWRsZENCaGN5QklWRTFNUkdsMlJXeGxiV1Z1ZER0Y2JpQWdjbVYwZFhKdUlDaGNiaUFnSUNCbGJHVnRaVzUwTG05M2JtVnlSRzlqZFcxbGJuUXVZV04wYVhabFJXeGxiV1Z1ZENBbUpseHVJQ0FnSUNobGJHVnRaVzUwTG05M2JtVnlSRzlqZFcxbGJuUXVZV04wYVhabFJXeGxiV1Z1ZENCaGN5QklWRTFNUld4bGJXVnVkQ2t1WW14MWNpZ3BYRzRnSUNrN1hHNTlPMXh1WEc1bGVIQnZjblFnWkdWbVlYVnNkQ0JtZFc1amRHbHZiaUJUWTNKdmJHeE5ZVzVoWjJWeUtIdGNiaUFnWTJocGJHUnlaVzRzWEc0Z0lHeHZZMnRGYm1GaWJHVmtMRnh1SUNCallYQjBkWEpsUlc1aFlteGxaQ0E5SUhSeWRXVXNYRzRnSUc5dVFtOTBkRzl0UVhKeWFYWmxMRnh1SUNCdmJrSnZkSFJ2YlV4bFlYWmxMRnh1SUNCdmJsUnZjRUZ5Y21sMlpTeGNiaUFnYjI1VWIzQk1aV0YyWlN4Y2JuMDZJRkJ5YjNCektTQjdYRzRnSUdOdmJuTjBJSE5sZEZOamNtOXNiRU5oY0hSMWNtVlVZWEpuWlhRZ1BTQjFjMlZUWTNKdmJHeERZWEIwZFhKbEtIdGNiaUFnSUNCcGMwVnVZV0pzWldRNklHTmhjSFIxY21WRmJtRmliR1ZrTEZ4dUlDQWdJRzl1UW05MGRHOXRRWEp5YVhabExGeHVJQ0FnSUc5dVFtOTBkRzl0VEdWaGRtVXNYRzRnSUNBZ2IyNVViM0JCY25KcGRtVXNYRzRnSUNBZ2IyNVViM0JNWldGMlpTeGNiaUFnZlNrN1hHNGdJR052Ym5OMElITmxkRk5qY205c2JFeHZZMnRVWVhKblpYUWdQU0IxYzJWVFkzSnZiR3hNYjJOcktIc2dhWE5GYm1GaWJHVmtPaUJzYjJOclJXNWhZbXhsWkNCOUtUdGNibHh1SUNCamIyNXpkQ0IwWVhKblpYUlNaV1k2SUZKbFprTmhiR3hpWVdOclBFaFVUVXhGYkdWdFpXNTBQaUE5SUNobGJHVnRaVzUwS1NBOVBpQjdYRzRnSUNBZ2MyVjBVMk55YjJ4c1EyRndkSFZ5WlZSaGNtZGxkQ2hsYkdWdFpXNTBLVHRjYmlBZ0lDQnpaWFJUWTNKdmJHeE1iMk5yVkdGeVoyVjBLR1ZzWlcxbGJuUXBPMXh1SUNCOU8xeHVYRzRnSUhKbGRIVnliaUFvWEc0Z0lDQWdQRVp5WVdkdFpXNTBQbHh1SUNBZ0lDQWdlMnh2WTJ0RmJtRmliR1ZrSUNZbUlDaGNiaUFnSUNBZ0lDQWdQR1JwZGx4dUlDQWdJQ0FnSUNBZ0lHOXVRMnhwWTJzOWUySnNkWEpUWld4bFkzUkpibkIxZEgxY2JpQWdJQ0FnSUNBZ0lDQmpjM005ZTNzZ2NHOXphWFJwYjI0NklDZG1hWGhsWkNjc0lHeGxablE2SURBc0lHSnZkSFJ2YlRvZ01Dd2djbWxuYUhRNklEQXNJSFJ2Y0RvZ01DQjlmVnh1SUNBZ0lDQWdJQ0F2UGx4dUlDQWdJQ0FnS1gxY2JpQWdJQ0FnSUh0amFHbHNaSEpsYmloMFlYSm5aWFJTWldZcGZWeHVJQ0FnSUR3dlJuSmhaMjFsYm5RK1hHNGdJQ2s3WEc1OVhHNGlYWDA9ICovXCIsXG4gIHRvU3RyaW5nOiBfRU1PVElPTl9TVFJJTkdJRklFRF9DU1NfRVJST1JfXyQxXG59O1xuZnVuY3Rpb24gU2Nyb2xsTWFuYWdlcihfcmVmKSB7XG4gIHZhciBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sXG4gICAgbG9ja0VuYWJsZWQgPSBfcmVmLmxvY2tFbmFibGVkLFxuICAgIF9yZWYkY2FwdHVyZUVuYWJsZWQgPSBfcmVmLmNhcHR1cmVFbmFibGVkLFxuICAgIGNhcHR1cmVFbmFibGVkID0gX3JlZiRjYXB0dXJlRW5hYmxlZCA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9yZWYkY2FwdHVyZUVuYWJsZWQsXG4gICAgb25Cb3R0b21BcnJpdmUgPSBfcmVmLm9uQm90dG9tQXJyaXZlLFxuICAgIG9uQm90dG9tTGVhdmUgPSBfcmVmLm9uQm90dG9tTGVhdmUsXG4gICAgb25Ub3BBcnJpdmUgPSBfcmVmLm9uVG9wQXJyaXZlLFxuICAgIG9uVG9wTGVhdmUgPSBfcmVmLm9uVG9wTGVhdmU7XG4gIHZhciBzZXRTY3JvbGxDYXB0dXJlVGFyZ2V0ID0gdXNlU2Nyb2xsQ2FwdHVyZSh7XG4gICAgaXNFbmFibGVkOiBjYXB0dXJlRW5hYmxlZCxcbiAgICBvbkJvdHRvbUFycml2ZTogb25Cb3R0b21BcnJpdmUsXG4gICAgb25Cb3R0b21MZWF2ZTogb25Cb3R0b21MZWF2ZSxcbiAgICBvblRvcEFycml2ZTogb25Ub3BBcnJpdmUsXG4gICAgb25Ub3BMZWF2ZTogb25Ub3BMZWF2ZVxuICB9KTtcbiAgdmFyIHNldFNjcm9sbExvY2tUYXJnZXQgPSB1c2VTY3JvbGxMb2NrKHtcbiAgICBpc0VuYWJsZWQ6IGxvY2tFbmFibGVkXG4gIH0pO1xuICB2YXIgdGFyZ2V0UmVmID0gZnVuY3Rpb24gdGFyZ2V0UmVmKGVsZW1lbnQpIHtcbiAgICBzZXRTY3JvbGxDYXB0dXJlVGFyZ2V0KGVsZW1lbnQpO1xuICAgIHNldFNjcm9sbExvY2tUYXJnZXQoZWxlbWVudCk7XG4gIH07XG4gIHJldHVybiBqc3goRnJhZ21lbnQsIG51bGwsIGxvY2tFbmFibGVkICYmIGpzeChcImRpdlwiLCB7XG4gICAgb25DbGljazogYmx1clNlbGVjdElucHV0LFxuICAgIGNzczogX3JlZjIkMVxuICB9KSwgY2hpbGRyZW4odGFyZ2V0UmVmKSk7XG59XG5cbmZ1bmN0aW9uIF9FTU9USU9OX1NUUklOR0lGSUVEX0NTU19FUlJPUl9fKCkgeyByZXR1cm4gXCJZb3UgaGF2ZSB0cmllZCB0byBzdHJpbmdpZnkgb2JqZWN0IHJldHVybmVkIGZyb20gYGNzc2AgZnVuY3Rpb24uIEl0IGlzbid0IHN1cHBvc2VkIHRvIGJlIHVzZWQgZGlyZWN0bHkgKGUuZy4gYXMgdmFsdWUgb2YgdGhlIGBjbGFzc05hbWVgIHByb3ApLCBidXQgcmF0aGVyIGhhbmRlZCB0byBlbW90aW9uIHNvIGl0IGNhbiBoYW5kbGUgaXQgKGUuZy4gYXMgdmFsdWUgb2YgYGNzc2AgcHJvcCkuXCI7IH1cbnZhciBfcmVmMiA9IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIiA/IHtcbiAgbmFtZTogXCIxYTBybzRuLXJlcXVpcmVkSW5wdXRcIixcbiAgc3R5bGVzOiBcImxhYmVsOnJlcXVpcmVkSW5wdXQ7b3BhY2l0eTowO3BvaW50ZXItZXZlbnRzOm5vbmU7cG9zaXRpb246YWJzb2x1dGU7Ym90dG9tOjA7bGVmdDowO3JpZ2h0OjA7d2lkdGg6MTAwJVwiXG59IDoge1xuICBuYW1lOiBcIjVra3hiMi1yZXF1aXJlZElucHV0LVJlcXVpcmVkSW5wdXRcIixcbiAgc3R5bGVzOiBcImxhYmVsOnJlcXVpcmVkSW5wdXQ7b3BhY2l0eTowO3BvaW50ZXItZXZlbnRzOm5vbmU7cG9zaXRpb246YWJzb2x1dGU7Ym90dG9tOjA7bGVmdDowO3JpZ2h0OjA7d2lkdGg6MTAwJTtsYWJlbDpSZXF1aXJlZElucHV0O1wiLFxuICBtYXA6IFwiLyojIHNvdXJjZU1hcHBpbmdVUkw9ZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTg7YmFzZTY0LGV5SjJaWEp6YVc5dUlqb3pMQ0p6YjNWeVkyVnpJanBiSWxKbGNYVnBjbVZrU1c1d2RYUXVkSE40SWwwc0ltNWhiV1Z6SWpwYlhTd2liV0Z3Y0dsdVozTWlPaUpCUVdOSklpd2labWxzWlNJNklsSmxjWFZwY21Wa1NXNXdkWFF1ZEhONElpd2ljMjkxY21ObGMwTnZiblJsYm5RaU9sc2lMeW9xSUVCcWMzZ2dhbk40SUNvdlhHNXBiWEJ2Y25RZ2V5QkdiMk4xYzBWMlpXNTBTR0Z1Wkd4bGNpd2dSblZ1WTNScGIyNURiMjF3YjI1bGJuUWdmU0JtY205dElDZHlaV0ZqZENjN1hHNXBiWEJ2Y25RZ2V5QnFjM2dnZlNCbWNtOXRJQ2RBWlcxdmRHbHZiaTl5WldGamRDYzdYRzVjYm1OdmJuTjBJRkpsY1hWcGNtVmtTVzV3ZFhRNklFWjFibU4wYVc5dVEyOXRjRzl1Wlc1MFBIdGNiaUFnY21WaFpHOXViSGtnYm1GdFpUODZJSE4wY21sdVp6dGNiaUFnY21WaFpHOXViSGtnYjI1R2IyTjFjem9nUm05amRYTkZkbVZ1ZEVoaGJtUnNaWEk4U0ZSTlRFbHVjSFYwUld4bGJXVnVkRDQ3WEc1OVBpQTlJQ2g3SUc1aGJXVXNJRzl1Um05amRYTWdmU2tnUFQ0Z0tGeHVJQ0E4YVc1d2RYUmNiaUFnSUNCeVpYRjFhWEpsWkZ4dUlDQWdJRzVoYldVOWUyNWhiV1Y5WEc0Z0lDQWdkR0ZpU1c1a1pYZzlleTB4ZlZ4dUlDQWdJR0Z5YVdFdGFHbGtaR1Z1UFZ3aWRISjFaVndpWEc0Z0lDQWdiMjVHYjJOMWN6MTdiMjVHYjJOMWMzMWNiaUFnSUNCamMzTTllM3RjYmlBZ0lDQWdJR3hoWW1Wc09pQW5jbVZ4ZFdseVpXUkpibkIxZENjc1hHNGdJQ0FnSUNCdmNHRmphWFI1T2lBd0xGeHVJQ0FnSUNBZ2NHOXBiblJsY2tWMlpXNTBjem9nSjI1dmJtVW5MRnh1SUNBZ0lDQWdjRzl6YVhScGIyNDZJQ2RoWW5OdmJIVjBaU2NzWEc0Z0lDQWdJQ0JpYjNSMGIyMDZJREFzWEc0Z0lDQWdJQ0JzWldaME9pQXdMRnh1SUNBZ0lDQWdjbWxuYUhRNklEQXNYRzRnSUNBZ0lDQjNhV1IwYURvZ0p6RXdNQ1VuTEZ4dUlDQWdJSDE5WEc0Z0lDQWdMeThnVUhKbGRtVnVkQ0JnVTNkcGRHTm9hVzVuSUdaeWIyMGdkVzVqYjI1MGNtOXNiR1ZrSUhSdklHTnZiblJ5YjJ4c1pXUmdJR1Z5Y205eVhHNGdJQ0FnZG1Gc2RXVTlYQ0pjSWx4dUlDQWdJRzl1UTJoaGJtZGxQWHNvS1NBOVBpQjdmWDFjYmlBZ0x6NWNiaWs3WEc1Y2JtVjRjRzl5ZENCa1pXWmhkV3gwSUZKbGNYVnBjbVZrU1c1d2RYUTdYRzRpWFgwPSAqL1wiLFxuICB0b1N0cmluZzogX0VNT1RJT05fU1RSSU5HSUZJRURfQ1NTX0VSUk9SX19cbn07XG52YXIgUmVxdWlyZWRJbnB1dCA9IGZ1bmN0aW9uIFJlcXVpcmVkSW5wdXQoX3JlZikge1xuICB2YXIgbmFtZSA9IF9yZWYubmFtZSxcbiAgICBvbkZvY3VzID0gX3JlZi5vbkZvY3VzO1xuICByZXR1cm4ganN4KFwiaW5wdXRcIiwge1xuICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIG5hbWU6IG5hbWUsXG4gICAgdGFiSW5kZXg6IC0xLFxuICAgIFwiYXJpYS1oaWRkZW5cIjogXCJ0cnVlXCIsXG4gICAgb25Gb2N1czogb25Gb2N1cyxcbiAgICBjc3M6IF9yZWYyXG4gICAgLy8gUHJldmVudCBgU3dpdGNoaW5nIGZyb20gdW5jb250cm9sbGVkIHRvIGNvbnRyb2xsZWRgIGVycm9yXG4gICAgLFxuICAgIHZhbHVlOiBcIlwiLFxuICAgIG9uQ2hhbmdlOiBmdW5jdGlvbiBvbkNoYW5nZSgpIHt9XG4gIH0pO1xufTtcbnZhciBSZXF1aXJlZElucHV0JDEgPSBSZXF1aXJlZElucHV0O1xuXG4vLy8gPHJlZmVyZW5jZSB0eXBlcz1cInVzZXItYWdlbnQtZGF0YS10eXBlc1wiIC8+XG5cbmZ1bmN0aW9uIHRlc3RQbGF0Zm9ybShyZSkge1xuICB2YXIgX3dpbmRvdyRuYXZpZ2F0b3IkdXNlO1xuICByZXR1cm4gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93Lm5hdmlnYXRvciAhPSBudWxsID8gcmUudGVzdCgoKF93aW5kb3ckbmF2aWdhdG9yJHVzZSA9IHdpbmRvdy5uYXZpZ2F0b3JbJ3VzZXJBZ2VudERhdGEnXSkgPT09IG51bGwgfHwgX3dpbmRvdyRuYXZpZ2F0b3IkdXNlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfd2luZG93JG5hdmlnYXRvciR1c2UucGxhdGZvcm0pIHx8IHdpbmRvdy5uYXZpZ2F0b3IucGxhdGZvcm0pIDogZmFsc2U7XG59XG5mdW5jdGlvbiBpc0lQaG9uZSgpIHtcbiAgcmV0dXJuIHRlc3RQbGF0Zm9ybSgvXmlQaG9uZS9pKTtcbn1cbmZ1bmN0aW9uIGlzTWFjKCkge1xuICByZXR1cm4gdGVzdFBsYXRmb3JtKC9eTWFjL2kpO1xufVxuZnVuY3Rpb24gaXNJUGFkKCkge1xuICByZXR1cm4gdGVzdFBsYXRmb3JtKC9eaVBhZC9pKSB8fFxuICAvLyBpUGFkT1MgMTMgbGllcyBhbmQgc2F5cyBpdCdzIGEgTWFjLCBidXQgd2UgY2FuIGRpc3Rpbmd1aXNoIGJ5IGRldGVjdGluZyB0b3VjaCBzdXBwb3J0LlxuICBpc01hYygpICYmIG5hdmlnYXRvci5tYXhUb3VjaFBvaW50cyA+IDE7XG59XG5mdW5jdGlvbiBpc0lPUygpIHtcbiAgcmV0dXJuIGlzSVBob25lKCkgfHwgaXNJUGFkKCk7XG59XG5mdW5jdGlvbiBpc0FwcGxlRGV2aWNlKCkge1xuICByZXR1cm4gaXNNYWMoKSB8fCBpc0lPUygpO1xufVxuXG52YXIgZm9ybWF0R3JvdXBMYWJlbCA9IGZ1bmN0aW9uIGZvcm1hdEdyb3VwTGFiZWwoZ3JvdXApIHtcbiAgcmV0dXJuIGdyb3VwLmxhYmVsO1xufTtcbnZhciBnZXRPcHRpb25MYWJlbCQxID0gZnVuY3Rpb24gZ2V0T3B0aW9uTGFiZWwob3B0aW9uKSB7XG4gIHJldHVybiBvcHRpb24ubGFiZWw7XG59O1xudmFyIGdldE9wdGlvblZhbHVlJDEgPSBmdW5jdGlvbiBnZXRPcHRpb25WYWx1ZShvcHRpb24pIHtcbiAgcmV0dXJuIG9wdGlvbi52YWx1ZTtcbn07XG52YXIgaXNPcHRpb25EaXNhYmxlZCA9IGZ1bmN0aW9uIGlzT3B0aW9uRGlzYWJsZWQob3B0aW9uKSB7XG4gIHJldHVybiAhIW9wdGlvbi5pc0Rpc2FibGVkO1xufTtcblxudmFyIGRlZmF1bHRTdHlsZXMgPSB7XG4gIGNsZWFySW5kaWNhdG9yOiBjbGVhckluZGljYXRvckNTUyxcbiAgY29udGFpbmVyOiBjb250YWluZXJDU1MsXG4gIGNvbnRyb2w6IGNzcyQxLFxuICBkcm9wZG93bkluZGljYXRvcjogZHJvcGRvd25JbmRpY2F0b3JDU1MsXG4gIGdyb3VwOiBncm91cENTUyxcbiAgZ3JvdXBIZWFkaW5nOiBncm91cEhlYWRpbmdDU1MsXG4gIGluZGljYXRvcnNDb250YWluZXI6IGluZGljYXRvcnNDb250YWluZXJDU1MsXG4gIGluZGljYXRvclNlcGFyYXRvcjogaW5kaWNhdG9yU2VwYXJhdG9yQ1NTLFxuICBpbnB1dDogaW5wdXRDU1MsXG4gIGxvYWRpbmdJbmRpY2F0b3I6IGxvYWRpbmdJbmRpY2F0b3JDU1MsXG4gIGxvYWRpbmdNZXNzYWdlOiBsb2FkaW5nTWVzc2FnZUNTUyxcbiAgbWVudTogbWVudUNTUyxcbiAgbWVudUxpc3Q6IG1lbnVMaXN0Q1NTLFxuICBtZW51UG9ydGFsOiBtZW51UG9ydGFsQ1NTLFxuICBtdWx0aVZhbHVlOiBtdWx0aVZhbHVlQ1NTLFxuICBtdWx0aVZhbHVlTGFiZWw6IG11bHRpVmFsdWVMYWJlbENTUyxcbiAgbXVsdGlWYWx1ZVJlbW92ZTogbXVsdGlWYWx1ZVJlbW92ZUNTUyxcbiAgbm9PcHRpb25zTWVzc2FnZTogbm9PcHRpb25zTWVzc2FnZUNTUyxcbiAgb3B0aW9uOiBvcHRpb25DU1MsXG4gIHBsYWNlaG9sZGVyOiBwbGFjZWhvbGRlckNTUyxcbiAgc2luZ2xlVmFsdWU6IGNzcyQyLFxuICB2YWx1ZUNvbnRhaW5lcjogdmFsdWVDb250YWluZXJDU1Ncbn07XG4vLyBNZXJnZSBVdGlsaXR5XG4vLyBBbGxvd3MgY29uc3VtZXJzIHRvIGV4dGVuZCBhIGJhc2UgU2VsZWN0IHdpdGggYWRkaXRpb25hbCBzdHlsZXNcblxuZnVuY3Rpb24gbWVyZ2VTdHlsZXMoc291cmNlKSB7XG4gIHZhciB0YXJnZXQgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IHt9O1xuICAvLyBpbml0aWFsaXplIHdpdGggc291cmNlIHN0eWxlc1xuICB2YXIgc3R5bGVzID0gX29iamVjdFNwcmVhZCh7fSwgc291cmNlKTtcblxuICAvLyBtYXNzYWdlIGluIHRhcmdldCBzdHlsZXNcbiAgT2JqZWN0LmtleXModGFyZ2V0KS5mb3JFYWNoKGZ1bmN0aW9uIChrZXlBc1N0cmluZykge1xuICAgIHZhciBrZXkgPSBrZXlBc1N0cmluZztcbiAgICBpZiAoc291cmNlW2tleV0pIHtcbiAgICAgIHN0eWxlc1trZXldID0gZnVuY3Rpb24gKHJzQ3NzLCBwcm9wcykge1xuICAgICAgICByZXR1cm4gdGFyZ2V0W2tleV0oc291cmNlW2tleV0ocnNDc3MsIHByb3BzKSwgcHJvcHMpO1xuICAgICAgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgc3R5bGVzW2tleV0gPSB0YXJnZXRba2V5XTtcbiAgICB9XG4gIH0pO1xuICByZXR1cm4gc3R5bGVzO1xufVxuXG52YXIgY29sb3JzID0ge1xuICBwcmltYXJ5OiAnIzI2ODRGRicsXG4gIHByaW1hcnk3NTogJyM0QzlBRkYnLFxuICBwcmltYXJ5NTA6ICcjQjJENEZGJyxcbiAgcHJpbWFyeTI1OiAnI0RFRUJGRicsXG4gIGRhbmdlcjogJyNERTM1MEInLFxuICBkYW5nZXJMaWdodDogJyNGRkJEQUQnLFxuICBuZXV0cmFsMDogJ2hzbCgwLCAwJSwgMTAwJSknLFxuICBuZXV0cmFsNTogJ2hzbCgwLCAwJSwgOTUlKScsXG4gIG5ldXRyYWwxMDogJ2hzbCgwLCAwJSwgOTAlKScsXG4gIG5ldXRyYWwyMDogJ2hzbCgwLCAwJSwgODAlKScsXG4gIG5ldXRyYWwzMDogJ2hzbCgwLCAwJSwgNzAlKScsXG4gIG5ldXRyYWw0MDogJ2hzbCgwLCAwJSwgNjAlKScsXG4gIG5ldXRyYWw1MDogJ2hzbCgwLCAwJSwgNTAlKScsXG4gIG5ldXRyYWw2MDogJ2hzbCgwLCAwJSwgNDAlKScsXG4gIG5ldXRyYWw3MDogJ2hzbCgwLCAwJSwgMzAlKScsXG4gIG5ldXRyYWw4MDogJ2hzbCgwLCAwJSwgMjAlKScsXG4gIG5ldXRyYWw5MDogJ2hzbCgwLCAwJSwgMTAlKSdcbn07XG52YXIgYm9yZGVyUmFkaXVzID0gNDtcbi8vIFVzZWQgdG8gY2FsY3VsYXRlIGNvbnNpc3RlbnQgbWFyZ2luL3BhZGRpbmcgb24gZWxlbWVudHNcbnZhciBiYXNlVW5pdCA9IDQ7XG4vLyBUaGUgbWluaW11bSBoZWlnaHQgb2YgdGhlIGNvbnRyb2xcbnZhciBjb250cm9sSGVpZ2h0ID0gMzg7XG4vLyBUaGUgYW1vdW50IG9mIHNwYWNlIGJldHdlZW4gdGhlIGNvbnRyb2wgYW5kIG1lbnUgKi9cbnZhciBtZW51R3V0dGVyID0gYmFzZVVuaXQgKiAyO1xudmFyIHNwYWNpbmcgPSB7XG4gIGJhc2VVbml0OiBiYXNlVW5pdCxcbiAgY29udHJvbEhlaWdodDogY29udHJvbEhlaWdodCxcbiAgbWVudUd1dHRlcjogbWVudUd1dHRlclxufTtcbnZhciBkZWZhdWx0VGhlbWUgPSB7XG4gIGJvcmRlclJhZGl1czogYm9yZGVyUmFkaXVzLFxuICBjb2xvcnM6IGNvbG9ycyxcbiAgc3BhY2luZzogc3BhY2luZ1xufTtcblxudmFyIGRlZmF1bHRQcm9wcyA9IHtcbiAgJ2FyaWEtbGl2ZSc6ICdwb2xpdGUnLFxuICBiYWNrc3BhY2VSZW1vdmVzVmFsdWU6IHRydWUsXG4gIGJsdXJJbnB1dE9uU2VsZWN0OiBpc1RvdWNoQ2FwYWJsZSgpLFxuICBjYXB0dXJlTWVudVNjcm9sbDogIWlzVG91Y2hDYXBhYmxlKCksXG4gIGNsYXNzTmFtZXM6IHt9LFxuICBjbG9zZU1lbnVPblNlbGVjdDogdHJ1ZSxcbiAgY2xvc2VNZW51T25TY3JvbGw6IGZhbHNlLFxuICBjb21wb25lbnRzOiB7fSxcbiAgY29udHJvbFNob3VsZFJlbmRlclZhbHVlOiB0cnVlLFxuICBlc2NhcGVDbGVhcnNWYWx1ZTogZmFsc2UsXG4gIGZpbHRlck9wdGlvbjogY3JlYXRlRmlsdGVyKCksXG4gIGZvcm1hdEdyb3VwTGFiZWw6IGZvcm1hdEdyb3VwTGFiZWwsXG4gIGdldE9wdGlvbkxhYmVsOiBnZXRPcHRpb25MYWJlbCQxLFxuICBnZXRPcHRpb25WYWx1ZTogZ2V0T3B0aW9uVmFsdWUkMSxcbiAgaXNEaXNhYmxlZDogZmFsc2UsXG4gIGlzTG9hZGluZzogZmFsc2UsXG4gIGlzTXVsdGk6IGZhbHNlLFxuICBpc1J0bDogZmFsc2UsXG4gIGlzU2VhcmNoYWJsZTogdHJ1ZSxcbiAgaXNPcHRpb25EaXNhYmxlZDogaXNPcHRpb25EaXNhYmxlZCxcbiAgbG9hZGluZ01lc3NhZ2U6IGZ1bmN0aW9uIGxvYWRpbmdNZXNzYWdlKCkge1xuICAgIHJldHVybiAnTG9hZGluZy4uLic7XG4gIH0sXG4gIG1heE1lbnVIZWlnaHQ6IDMwMCxcbiAgbWluTWVudUhlaWdodDogMTQwLFxuICBtZW51SXNPcGVuOiBmYWxzZSxcbiAgbWVudVBsYWNlbWVudDogJ2JvdHRvbScsXG4gIG1lbnVQb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgbWVudVNob3VsZEJsb2NrU2Nyb2xsOiBmYWxzZSxcbiAgbWVudVNob3VsZFNjcm9sbEludG9WaWV3OiAhaXNNb2JpbGVEZXZpY2UoKSxcbiAgbm9PcHRpb25zTWVzc2FnZTogZnVuY3Rpb24gbm9PcHRpb25zTWVzc2FnZSgpIHtcbiAgICByZXR1cm4gJ05vIG9wdGlvbnMnO1xuICB9LFxuICBvcGVuTWVudU9uRm9jdXM6IGZhbHNlLFxuICBvcGVuTWVudU9uQ2xpY2s6IHRydWUsXG4gIG9wdGlvbnM6IFtdLFxuICBwYWdlU2l6ZTogNSxcbiAgcGxhY2Vob2xkZXI6ICdTZWxlY3QuLi4nLFxuICBzY3JlZW5SZWFkZXJTdGF0dXM6IGZ1bmN0aW9uIHNjcmVlblJlYWRlclN0YXR1cyhfcmVmKSB7XG4gICAgdmFyIGNvdW50ID0gX3JlZi5jb3VudDtcbiAgICByZXR1cm4gXCJcIi5jb25jYXQoY291bnQsIFwiIHJlc3VsdFwiKS5jb25jYXQoY291bnQgIT09IDEgPyAncycgOiAnJywgXCIgYXZhaWxhYmxlXCIpO1xuICB9LFxuICBzdHlsZXM6IHt9LFxuICB0YWJJbmRleDogMCxcbiAgdGFiU2VsZWN0c1ZhbHVlOiB0cnVlLFxuICB1bnN0eWxlZDogZmFsc2Vcbn07XG5mdW5jdGlvbiB0b0NhdGVnb3JpemVkT3B0aW9uKHByb3BzLCBvcHRpb24sIHNlbGVjdFZhbHVlLCBpbmRleCkge1xuICB2YXIgaXNEaXNhYmxlZCA9IF9pc09wdGlvbkRpc2FibGVkKHByb3BzLCBvcHRpb24sIHNlbGVjdFZhbHVlKTtcbiAgdmFyIGlzU2VsZWN0ZWQgPSBfaXNPcHRpb25TZWxlY3RlZChwcm9wcywgb3B0aW9uLCBzZWxlY3RWYWx1ZSk7XG4gIHZhciBsYWJlbCA9IGdldE9wdGlvbkxhYmVsKHByb3BzLCBvcHRpb24pO1xuICB2YXIgdmFsdWUgPSBnZXRPcHRpb25WYWx1ZShwcm9wcywgb3B0aW9uKTtcbiAgcmV0dXJuIHtcbiAgICB0eXBlOiAnb3B0aW9uJyxcbiAgICBkYXRhOiBvcHRpb24sXG4gICAgaXNEaXNhYmxlZDogaXNEaXNhYmxlZCxcbiAgICBpc1NlbGVjdGVkOiBpc1NlbGVjdGVkLFxuICAgIGxhYmVsOiBsYWJlbCxcbiAgICB2YWx1ZTogdmFsdWUsXG4gICAgaW5kZXg6IGluZGV4XG4gIH07XG59XG5mdW5jdGlvbiBidWlsZENhdGVnb3JpemVkT3B0aW9ucyhwcm9wcywgc2VsZWN0VmFsdWUpIHtcbiAgcmV0dXJuIHByb3BzLm9wdGlvbnMubWFwKGZ1bmN0aW9uIChncm91cE9yT3B0aW9uLCBncm91cE9yT3B0aW9uSW5kZXgpIHtcbiAgICBpZiAoJ29wdGlvbnMnIGluIGdyb3VwT3JPcHRpb24pIHtcbiAgICAgIHZhciBjYXRlZ29yaXplZE9wdGlvbnMgPSBncm91cE9yT3B0aW9uLm9wdGlvbnMubWFwKGZ1bmN0aW9uIChvcHRpb24sIG9wdGlvbkluZGV4KSB7XG4gICAgICAgIHJldHVybiB0b0NhdGVnb3JpemVkT3B0aW9uKHByb3BzLCBvcHRpb24sIHNlbGVjdFZhbHVlLCBvcHRpb25JbmRleCk7XG4gICAgICB9KS5maWx0ZXIoZnVuY3Rpb24gKGNhdGVnb3JpemVkT3B0aW9uKSB7XG4gICAgICAgIHJldHVybiBpc0ZvY3VzYWJsZShwcm9wcywgY2F0ZWdvcml6ZWRPcHRpb24pO1xuICAgICAgfSk7XG4gICAgICByZXR1cm4gY2F0ZWdvcml6ZWRPcHRpb25zLmxlbmd0aCA+IDAgPyB7XG4gICAgICAgIHR5cGU6ICdncm91cCcsXG4gICAgICAgIGRhdGE6IGdyb3VwT3JPcHRpb24sXG4gICAgICAgIG9wdGlvbnM6IGNhdGVnb3JpemVkT3B0aW9ucyxcbiAgICAgICAgaW5kZXg6IGdyb3VwT3JPcHRpb25JbmRleFxuICAgICAgfSA6IHVuZGVmaW5lZDtcbiAgICB9XG4gICAgdmFyIGNhdGVnb3JpemVkT3B0aW9uID0gdG9DYXRlZ29yaXplZE9wdGlvbihwcm9wcywgZ3JvdXBPck9wdGlvbiwgc2VsZWN0VmFsdWUsIGdyb3VwT3JPcHRpb25JbmRleCk7XG4gICAgcmV0dXJuIGlzRm9jdXNhYmxlKHByb3BzLCBjYXRlZ29yaXplZE9wdGlvbikgPyBjYXRlZ29yaXplZE9wdGlvbiA6IHVuZGVmaW5lZDtcbiAgfSkuZmlsdGVyKG5vdE51bGxpc2gpO1xufVxuZnVuY3Rpb24gYnVpbGRGb2N1c2FibGVPcHRpb25zRnJvbUNhdGVnb3JpemVkT3B0aW9ucyhjYXRlZ29yaXplZE9wdGlvbnMpIHtcbiAgcmV0dXJuIGNhdGVnb3JpemVkT3B0aW9ucy5yZWR1Y2UoZnVuY3Rpb24gKG9wdGlvbnNBY2N1bXVsYXRvciwgY2F0ZWdvcml6ZWRPcHRpb24pIHtcbiAgICBpZiAoY2F0ZWdvcml6ZWRPcHRpb24udHlwZSA9PT0gJ2dyb3VwJykge1xuICAgICAgb3B0aW9uc0FjY3VtdWxhdG9yLnB1c2guYXBwbHkob3B0aW9uc0FjY3VtdWxhdG9yLCBfdG9Db25zdW1hYmxlQXJyYXkoY2F0ZWdvcml6ZWRPcHRpb24ub3B0aW9ucy5tYXAoZnVuY3Rpb24gKG9wdGlvbikge1xuICAgICAgICByZXR1cm4gb3B0aW9uLmRhdGE7XG4gICAgICB9KSkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBvcHRpb25zQWNjdW11bGF0b3IucHVzaChjYXRlZ29yaXplZE9wdGlvbi5kYXRhKTtcbiAgICB9XG4gICAgcmV0dXJuIG9wdGlvbnNBY2N1bXVsYXRvcjtcbiAgfSwgW10pO1xufVxuZnVuY3Rpb24gYnVpbGRGb2N1c2FibGVPcHRpb25zV2l0aElkcyhjYXRlZ29yaXplZE9wdGlvbnMsIG9wdGlvbklkKSB7XG4gIHJldHVybiBjYXRlZ29yaXplZE9wdGlvbnMucmVkdWNlKGZ1bmN0aW9uIChvcHRpb25zQWNjdW11bGF0b3IsIGNhdGVnb3JpemVkT3B0aW9uKSB7XG4gICAgaWYgKGNhdGVnb3JpemVkT3B0aW9uLnR5cGUgPT09ICdncm91cCcpIHtcbiAgICAgIG9wdGlvbnNBY2N1bXVsYXRvci5wdXNoLmFwcGx5KG9wdGlvbnNBY2N1bXVsYXRvciwgX3RvQ29uc3VtYWJsZUFycmF5KGNhdGVnb3JpemVkT3B0aW9uLm9wdGlvbnMubWFwKGZ1bmN0aW9uIChvcHRpb24pIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBkYXRhOiBvcHRpb24uZGF0YSxcbiAgICAgICAgICBpZDogXCJcIi5jb25jYXQob3B0aW9uSWQsIFwiLVwiKS5jb25jYXQoY2F0ZWdvcml6ZWRPcHRpb24uaW5kZXgsIFwiLVwiKS5jb25jYXQob3B0aW9uLmluZGV4KVxuICAgICAgICB9O1xuICAgICAgfSkpKTtcbiAgICB9IGVsc2Uge1xuICAgICAgb3B0aW9uc0FjY3VtdWxhdG9yLnB1c2goe1xuICAgICAgICBkYXRhOiBjYXRlZ29yaXplZE9wdGlvbi5kYXRhLFxuICAgICAgICBpZDogXCJcIi5jb25jYXQob3B0aW9uSWQsIFwiLVwiKS5jb25jYXQoY2F0ZWdvcml6ZWRPcHRpb24uaW5kZXgpXG4gICAgICB9KTtcbiAgICB9XG4gICAgcmV0dXJuIG9wdGlvbnNBY2N1bXVsYXRvcjtcbiAgfSwgW10pO1xufVxuZnVuY3Rpb24gYnVpbGRGb2N1c2FibGVPcHRpb25zKHByb3BzLCBzZWxlY3RWYWx1ZSkge1xuICByZXR1cm4gYnVpbGRGb2N1c2FibGVPcHRpb25zRnJvbUNhdGVnb3JpemVkT3B0aW9ucyhidWlsZENhdGVnb3JpemVkT3B0aW9ucyhwcm9wcywgc2VsZWN0VmFsdWUpKTtcbn1cbmZ1bmN0aW9uIGlzRm9jdXNhYmxlKHByb3BzLCBjYXRlZ29yaXplZE9wdGlvbikge1xuICB2YXIgX3Byb3BzJGlucHV0VmFsdWUgPSBwcm9wcy5pbnB1dFZhbHVlLFxuICAgIGlucHV0VmFsdWUgPSBfcHJvcHMkaW5wdXRWYWx1ZSA9PT0gdm9pZCAwID8gJycgOiBfcHJvcHMkaW5wdXRWYWx1ZTtcbiAgdmFyIGRhdGEgPSBjYXRlZ29yaXplZE9wdGlvbi5kYXRhLFxuICAgIGlzU2VsZWN0ZWQgPSBjYXRlZ29yaXplZE9wdGlvbi5pc1NlbGVjdGVkLFxuICAgIGxhYmVsID0gY2F0ZWdvcml6ZWRPcHRpb24ubGFiZWwsXG4gICAgdmFsdWUgPSBjYXRlZ29yaXplZE9wdGlvbi52YWx1ZTtcbiAgcmV0dXJuICghc2hvdWxkSGlkZVNlbGVjdGVkT3B0aW9ucyhwcm9wcykgfHwgIWlzU2VsZWN0ZWQpICYmIF9maWx0ZXJPcHRpb24ocHJvcHMsIHtcbiAgICBsYWJlbDogbGFiZWwsXG4gICAgdmFsdWU6IHZhbHVlLFxuICAgIGRhdGE6IGRhdGFcbiAgfSwgaW5wdXRWYWx1ZSk7XG59XG5mdW5jdGlvbiBnZXROZXh0Rm9jdXNlZFZhbHVlKHN0YXRlLCBuZXh0U2VsZWN0VmFsdWUpIHtcbiAgdmFyIGZvY3VzZWRWYWx1ZSA9IHN0YXRlLmZvY3VzZWRWYWx1ZSxcbiAgICBsYXN0U2VsZWN0VmFsdWUgPSBzdGF0ZS5zZWxlY3RWYWx1ZTtcbiAgdmFyIGxhc3RGb2N1c2VkSW5kZXggPSBsYXN0U2VsZWN0VmFsdWUuaW5kZXhPZihmb2N1c2VkVmFsdWUpO1xuICBpZiAobGFzdEZvY3VzZWRJbmRleCA+IC0xKSB7XG4gICAgdmFyIG5leHRGb2N1c2VkSW5kZXggPSBuZXh0U2VsZWN0VmFsdWUuaW5kZXhPZihmb2N1c2VkVmFsdWUpO1xuICAgIGlmIChuZXh0Rm9jdXNlZEluZGV4ID4gLTEpIHtcbiAgICAgIC8vIHRoZSBmb2N1c2VkIHZhbHVlIGlzIHN0aWxsIGluIHRoZSBzZWxlY3RWYWx1ZSwgcmV0dXJuIGl0XG4gICAgICByZXR1cm4gZm9jdXNlZFZhbHVlO1xuICAgIH0gZWxzZSBpZiAobGFzdEZvY3VzZWRJbmRleCA8IG5leHRTZWxlY3RWYWx1ZS5sZW5ndGgpIHtcbiAgICAgIC8vIHRoZSBmb2N1c2VkVmFsdWUgaXMgbm90IHByZXNlbnQgaW4gdGhlIG5leHQgc2VsZWN0VmFsdWUgYXJyYXkgYnlcbiAgICAgIC8vIHJlZmVyZW5jZSwgc28gcmV0dXJuIHRoZSBuZXcgdmFsdWUgYXQgdGhlIHNhbWUgaW5kZXhcbiAgICAgIHJldHVybiBuZXh0U2VsZWN0VmFsdWVbbGFzdEZvY3VzZWRJbmRleF07XG4gICAgfVxuICB9XG4gIHJldHVybiBudWxsO1xufVxuZnVuY3Rpb24gZ2V0TmV4dEZvY3VzZWRPcHRpb24oc3RhdGUsIG9wdGlvbnMpIHtcbiAgdmFyIGxhc3RGb2N1c2VkT3B0aW9uID0gc3RhdGUuZm9jdXNlZE9wdGlvbjtcbiAgcmV0dXJuIGxhc3RGb2N1c2VkT3B0aW9uICYmIG9wdGlvbnMuaW5kZXhPZihsYXN0Rm9jdXNlZE9wdGlvbikgPiAtMSA/IGxhc3RGb2N1c2VkT3B0aW9uIDogb3B0aW9uc1swXTtcbn1cbnZhciBnZXRGb2N1c2VkT3B0aW9uSWQgPSBmdW5jdGlvbiBnZXRGb2N1c2VkT3B0aW9uSWQoZm9jdXNhYmxlT3B0aW9uc1dpdGhJZHMsIGZvY3VzZWRPcHRpb24pIHtcbiAgdmFyIF9mb2N1c2FibGVPcHRpb25zV2l0aDtcbiAgdmFyIGZvY3VzZWRPcHRpb25JZCA9IChfZm9jdXNhYmxlT3B0aW9uc1dpdGggPSBmb2N1c2FibGVPcHRpb25zV2l0aElkcy5maW5kKGZ1bmN0aW9uIChvcHRpb24pIHtcbiAgICByZXR1cm4gb3B0aW9uLmRhdGEgPT09IGZvY3VzZWRPcHRpb247XG4gIH0pKSA9PT0gbnVsbCB8fCBfZm9jdXNhYmxlT3B0aW9uc1dpdGggPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9mb2N1c2FibGVPcHRpb25zV2l0aC5pZDtcbiAgcmV0dXJuIGZvY3VzZWRPcHRpb25JZCB8fCBudWxsO1xufTtcbnZhciBnZXRPcHRpb25MYWJlbCA9IGZ1bmN0aW9uIGdldE9wdGlvbkxhYmVsKHByb3BzLCBkYXRhKSB7XG4gIHJldHVybiBwcm9wcy5nZXRPcHRpb25MYWJlbChkYXRhKTtcbn07XG52YXIgZ2V0T3B0aW9uVmFsdWUgPSBmdW5jdGlvbiBnZXRPcHRpb25WYWx1ZShwcm9wcywgZGF0YSkge1xuICByZXR1cm4gcHJvcHMuZ2V0T3B0aW9uVmFsdWUoZGF0YSk7XG59O1xuZnVuY3Rpb24gX2lzT3B0aW9uRGlzYWJsZWQocHJvcHMsIG9wdGlvbiwgc2VsZWN0VmFsdWUpIHtcbiAgcmV0dXJuIHR5cGVvZiBwcm9wcy5pc09wdGlvbkRpc2FibGVkID09PSAnZnVuY3Rpb24nID8gcHJvcHMuaXNPcHRpb25EaXNhYmxlZChvcHRpb24sIHNlbGVjdFZhbHVlKSA6IGZhbHNlO1xufVxuZnVuY3Rpb24gX2lzT3B0aW9uU2VsZWN0ZWQocHJvcHMsIG9wdGlvbiwgc2VsZWN0VmFsdWUpIHtcbiAgaWYgKHNlbGVjdFZhbHVlLmluZGV4T2Yob3B0aW9uKSA+IC0xKSByZXR1cm4gdHJ1ZTtcbiAgaWYgKHR5cGVvZiBwcm9wcy5pc09wdGlvblNlbGVjdGVkID09PSAnZnVuY3Rpb24nKSB7XG4gICAgcmV0dXJuIHByb3BzLmlzT3B0aW9uU2VsZWN0ZWQob3B0aW9uLCBzZWxlY3RWYWx1ZSk7XG4gIH1cbiAgdmFyIGNhbmRpZGF0ZSA9IGdldE9wdGlvblZhbHVlKHByb3BzLCBvcHRpb24pO1xuICByZXR1cm4gc2VsZWN0VmFsdWUuc29tZShmdW5jdGlvbiAoaSkge1xuICAgIHJldHVybiBnZXRPcHRpb25WYWx1ZShwcm9wcywgaSkgPT09IGNhbmRpZGF0ZTtcbiAgfSk7XG59XG5mdW5jdGlvbiBfZmlsdGVyT3B0aW9uKHByb3BzLCBvcHRpb24sIGlucHV0VmFsdWUpIHtcbiAgcmV0dXJuIHByb3BzLmZpbHRlck9wdGlvbiA/IHByb3BzLmZpbHRlck9wdGlvbihvcHRpb24sIGlucHV0VmFsdWUpIDogdHJ1ZTtcbn1cbnZhciBzaG91bGRIaWRlU2VsZWN0ZWRPcHRpb25zID0gZnVuY3Rpb24gc2hvdWxkSGlkZVNlbGVjdGVkT3B0aW9ucyhwcm9wcykge1xuICB2YXIgaGlkZVNlbGVjdGVkT3B0aW9ucyA9IHByb3BzLmhpZGVTZWxlY3RlZE9wdGlvbnMsXG4gICAgaXNNdWx0aSA9IHByb3BzLmlzTXVsdGk7XG4gIGlmIChoaWRlU2VsZWN0ZWRPcHRpb25zID09PSB1bmRlZmluZWQpIHJldHVybiBpc011bHRpO1xuICByZXR1cm4gaGlkZVNlbGVjdGVkT3B0aW9ucztcbn07XG52YXIgaW5zdGFuY2VJZCA9IDE7XG52YXIgU2VsZWN0ID0gLyojX19QVVJFX18qL2Z1bmN0aW9uIChfQ29tcG9uZW50KSB7XG4gIF9pbmhlcml0cyhTZWxlY3QsIF9Db21wb25lbnQpO1xuICB2YXIgX3N1cGVyID0gX2NyZWF0ZVN1cGVyKFNlbGVjdCk7XG4gIC8vIE1pc2MuIEluc3RhbmNlIFByb3BlcnRpZXNcbiAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiAgLy8gVE9ET1xuXG4gIC8vIFJlZnNcbiAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiAgLy8gTGlmZWN5Y2xlXG4gIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gIGZ1bmN0aW9uIFNlbGVjdChfcHJvcHMpIHtcbiAgICB2YXIgX3RoaXM7XG4gICAgX2NsYXNzQ2FsbENoZWNrKHRoaXMsIFNlbGVjdCk7XG4gICAgX3RoaXMgPSBfc3VwZXIuY2FsbCh0aGlzLCBfcHJvcHMpO1xuICAgIF90aGlzLnN0YXRlID0ge1xuICAgICAgYXJpYVNlbGVjdGlvbjogbnVsbCxcbiAgICAgIGZvY3VzZWRPcHRpb246IG51bGwsXG4gICAgICBmb2N1c2VkT3B0aW9uSWQ6IG51bGwsXG4gICAgICBmb2N1c2FibGVPcHRpb25zV2l0aElkczogW10sXG4gICAgICBmb2N1c2VkVmFsdWU6IG51bGwsXG4gICAgICBpbnB1dElzSGlkZGVuOiBmYWxzZSxcbiAgICAgIGlzRm9jdXNlZDogZmFsc2UsXG4gICAgICBzZWxlY3RWYWx1ZTogW10sXG4gICAgICBjbGVhckZvY3VzVmFsdWVPblVwZGF0ZTogZmFsc2UsXG4gICAgICBwcmV2V2FzRm9jdXNlZDogZmFsc2UsXG4gICAgICBpbnB1dElzSGlkZGVuQWZ0ZXJVcGRhdGU6IHVuZGVmaW5lZCxcbiAgICAgIHByZXZQcm9wczogdW5kZWZpbmVkLFxuICAgICAgaW5zdGFuY2VQcmVmaXg6ICcnXG4gICAgfTtcbiAgICBfdGhpcy5ibG9ja09wdGlvbkhvdmVyID0gZmFsc2U7XG4gICAgX3RoaXMuaXNDb21wb3NpbmcgPSBmYWxzZTtcbiAgICBfdGhpcy5jb21tb25Qcm9wcyA9IHZvaWQgMDtcbiAgICBfdGhpcy5pbml0aWFsVG91Y2hYID0gMDtcbiAgICBfdGhpcy5pbml0aWFsVG91Y2hZID0gMDtcbiAgICBfdGhpcy5vcGVuQWZ0ZXJGb2N1cyA9IGZhbHNlO1xuICAgIF90aGlzLnNjcm9sbFRvRm9jdXNlZE9wdGlvbk9uVXBkYXRlID0gZmFsc2U7XG4gICAgX3RoaXMudXNlcklzRHJhZ2dpbmcgPSB2b2lkIDA7XG4gICAgX3RoaXMuaXNBcHBsZURldmljZSA9IGlzQXBwbGVEZXZpY2UoKTtcbiAgICBfdGhpcy5jb250cm9sUmVmID0gbnVsbDtcbiAgICBfdGhpcy5nZXRDb250cm9sUmVmID0gZnVuY3Rpb24gKHJlZikge1xuICAgICAgX3RoaXMuY29udHJvbFJlZiA9IHJlZjtcbiAgICB9O1xuICAgIF90aGlzLmZvY3VzZWRPcHRpb25SZWYgPSBudWxsO1xuICAgIF90aGlzLmdldEZvY3VzZWRPcHRpb25SZWYgPSBmdW5jdGlvbiAocmVmKSB7XG4gICAgICBfdGhpcy5mb2N1c2VkT3B0aW9uUmVmID0gcmVmO1xuICAgIH07XG4gICAgX3RoaXMubWVudUxpc3RSZWYgPSBudWxsO1xuICAgIF90aGlzLmdldE1lbnVMaXN0UmVmID0gZnVuY3Rpb24gKHJlZikge1xuICAgICAgX3RoaXMubWVudUxpc3RSZWYgPSByZWY7XG4gICAgfTtcbiAgICBfdGhpcy5pbnB1dFJlZiA9IG51bGw7XG4gICAgX3RoaXMuZ2V0SW5wdXRSZWYgPSBmdW5jdGlvbiAocmVmKSB7XG4gICAgICBfdGhpcy5pbnB1dFJlZiA9IHJlZjtcbiAgICB9O1xuICAgIF90aGlzLmZvY3VzID0gX3RoaXMuZm9jdXNJbnB1dDtcbiAgICBfdGhpcy5ibHVyID0gX3RoaXMuYmx1cklucHV0O1xuICAgIF90aGlzLm9uQ2hhbmdlID0gZnVuY3Rpb24gKG5ld1ZhbHVlLCBhY3Rpb25NZXRhKSB7XG4gICAgICB2YXIgX3RoaXMkcHJvcHMgPSBfdGhpcy5wcm9wcyxcbiAgICAgICAgb25DaGFuZ2UgPSBfdGhpcyRwcm9wcy5vbkNoYW5nZSxcbiAgICAgICAgbmFtZSA9IF90aGlzJHByb3BzLm5hbWU7XG4gICAgICBhY3Rpb25NZXRhLm5hbWUgPSBuYW1lO1xuICAgICAgX3RoaXMuYXJpYU9uQ2hhbmdlKG5ld1ZhbHVlLCBhY3Rpb25NZXRhKTtcbiAgICAgIG9uQ2hhbmdlKG5ld1ZhbHVlLCBhY3Rpb25NZXRhKTtcbiAgICB9O1xuICAgIF90aGlzLnNldFZhbHVlID0gZnVuY3Rpb24gKG5ld1ZhbHVlLCBhY3Rpb24sIG9wdGlvbikge1xuICAgICAgdmFyIF90aGlzJHByb3BzMiA9IF90aGlzLnByb3BzLFxuICAgICAgICBjbG9zZU1lbnVPblNlbGVjdCA9IF90aGlzJHByb3BzMi5jbG9zZU1lbnVPblNlbGVjdCxcbiAgICAgICAgaXNNdWx0aSA9IF90aGlzJHByb3BzMi5pc011bHRpLFxuICAgICAgICBpbnB1dFZhbHVlID0gX3RoaXMkcHJvcHMyLmlucHV0VmFsdWU7XG4gICAgICBfdGhpcy5vbklucHV0Q2hhbmdlKCcnLCB7XG4gICAgICAgIGFjdGlvbjogJ3NldC12YWx1ZScsXG4gICAgICAgIHByZXZJbnB1dFZhbHVlOiBpbnB1dFZhbHVlXG4gICAgICB9KTtcbiAgICAgIGlmIChjbG9zZU1lbnVPblNlbGVjdCkge1xuICAgICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgICAgaW5wdXRJc0hpZGRlbkFmdGVyVXBkYXRlOiAhaXNNdWx0aVxuICAgICAgICB9KTtcbiAgICAgICAgX3RoaXMub25NZW51Q2xvc2UoKTtcbiAgICAgIH1cbiAgICAgIC8vIHdoZW4gdGhlIHNlbGVjdCB2YWx1ZSBzaG91bGQgY2hhbmdlLCB3ZSBzaG91bGQgcmVzZXQgZm9jdXNlZFZhbHVlXG4gICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIGNsZWFyRm9jdXNWYWx1ZU9uVXBkYXRlOiB0cnVlXG4gICAgICB9KTtcbiAgICAgIF90aGlzLm9uQ2hhbmdlKG5ld1ZhbHVlLCB7XG4gICAgICAgIGFjdGlvbjogYWN0aW9uLFxuICAgICAgICBvcHRpb246IG9wdGlvblxuICAgICAgfSk7XG4gICAgfTtcbiAgICBfdGhpcy5zZWxlY3RPcHRpb24gPSBmdW5jdGlvbiAobmV3VmFsdWUpIHtcbiAgICAgIHZhciBfdGhpcyRwcm9wczMgPSBfdGhpcy5wcm9wcyxcbiAgICAgICAgYmx1cklucHV0T25TZWxlY3QgPSBfdGhpcyRwcm9wczMuYmx1cklucHV0T25TZWxlY3QsXG4gICAgICAgIGlzTXVsdGkgPSBfdGhpcyRwcm9wczMuaXNNdWx0aSxcbiAgICAgICAgbmFtZSA9IF90aGlzJHByb3BzMy5uYW1lO1xuICAgICAgdmFyIHNlbGVjdFZhbHVlID0gX3RoaXMuc3RhdGUuc2VsZWN0VmFsdWU7XG4gICAgICB2YXIgZGVzZWxlY3RlZCA9IGlzTXVsdGkgJiYgX3RoaXMuaXNPcHRpb25TZWxlY3RlZChuZXdWYWx1ZSwgc2VsZWN0VmFsdWUpO1xuICAgICAgdmFyIGlzRGlzYWJsZWQgPSBfdGhpcy5pc09wdGlvbkRpc2FibGVkKG5ld1ZhbHVlLCBzZWxlY3RWYWx1ZSk7XG4gICAgICBpZiAoZGVzZWxlY3RlZCkge1xuICAgICAgICB2YXIgY2FuZGlkYXRlID0gX3RoaXMuZ2V0T3B0aW9uVmFsdWUobmV3VmFsdWUpO1xuICAgICAgICBfdGhpcy5zZXRWYWx1ZShtdWx0aVZhbHVlQXNWYWx1ZShzZWxlY3RWYWx1ZS5maWx0ZXIoZnVuY3Rpb24gKGkpIHtcbiAgICAgICAgICByZXR1cm4gX3RoaXMuZ2V0T3B0aW9uVmFsdWUoaSkgIT09IGNhbmRpZGF0ZTtcbiAgICAgICAgfSkpLCAnZGVzZWxlY3Qtb3B0aW9uJywgbmV3VmFsdWUpO1xuICAgICAgfSBlbHNlIGlmICghaXNEaXNhYmxlZCkge1xuICAgICAgICAvLyBTZWxlY3Qgb3B0aW9uIGlmIG9wdGlvbiBpcyBub3QgZGlzYWJsZWRcbiAgICAgICAgaWYgKGlzTXVsdGkpIHtcbiAgICAgICAgICBfdGhpcy5zZXRWYWx1ZShtdWx0aVZhbHVlQXNWYWx1ZShbXS5jb25jYXQoX3RvQ29uc3VtYWJsZUFycmF5KHNlbGVjdFZhbHVlKSwgW25ld1ZhbHVlXSkpLCAnc2VsZWN0LW9wdGlvbicsIG5ld1ZhbHVlKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBfdGhpcy5zZXRWYWx1ZShzaW5nbGVWYWx1ZUFzVmFsdWUobmV3VmFsdWUpLCAnc2VsZWN0LW9wdGlvbicpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBfdGhpcy5hcmlhT25DaGFuZ2Uoc2luZ2xlVmFsdWVBc1ZhbHVlKG5ld1ZhbHVlKSwge1xuICAgICAgICAgIGFjdGlvbjogJ3NlbGVjdC1vcHRpb24nLFxuICAgICAgICAgIG9wdGlvbjogbmV3VmFsdWUsXG4gICAgICAgICAgbmFtZTogbmFtZVxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgaWYgKGJsdXJJbnB1dE9uU2VsZWN0KSB7XG4gICAgICAgIF90aGlzLmJsdXJJbnB1dCgpO1xuICAgICAgfVxuICAgIH07XG4gICAgX3RoaXMucmVtb3ZlVmFsdWUgPSBmdW5jdGlvbiAocmVtb3ZlZFZhbHVlKSB7XG4gICAgICB2YXIgaXNNdWx0aSA9IF90aGlzLnByb3BzLmlzTXVsdGk7XG4gICAgICB2YXIgc2VsZWN0VmFsdWUgPSBfdGhpcy5zdGF0ZS5zZWxlY3RWYWx1ZTtcbiAgICAgIHZhciBjYW5kaWRhdGUgPSBfdGhpcy5nZXRPcHRpb25WYWx1ZShyZW1vdmVkVmFsdWUpO1xuICAgICAgdmFyIG5ld1ZhbHVlQXJyYXkgPSBzZWxlY3RWYWx1ZS5maWx0ZXIoZnVuY3Rpb24gKGkpIHtcbiAgICAgICAgcmV0dXJuIF90aGlzLmdldE9wdGlvblZhbHVlKGkpICE9PSBjYW5kaWRhdGU7XG4gICAgICB9KTtcbiAgICAgIHZhciBuZXdWYWx1ZSA9IHZhbHVlVGVybmFyeShpc011bHRpLCBuZXdWYWx1ZUFycmF5LCBuZXdWYWx1ZUFycmF5WzBdIHx8IG51bGwpO1xuICAgICAgX3RoaXMub25DaGFuZ2UobmV3VmFsdWUsIHtcbiAgICAgICAgYWN0aW9uOiAncmVtb3ZlLXZhbHVlJyxcbiAgICAgICAgcmVtb3ZlZFZhbHVlOiByZW1vdmVkVmFsdWVcbiAgICAgIH0pO1xuICAgICAgX3RoaXMuZm9jdXNJbnB1dCgpO1xuICAgIH07XG4gICAgX3RoaXMuY2xlYXJWYWx1ZSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIHZhciBzZWxlY3RWYWx1ZSA9IF90aGlzLnN0YXRlLnNlbGVjdFZhbHVlO1xuICAgICAgX3RoaXMub25DaGFuZ2UodmFsdWVUZXJuYXJ5KF90aGlzLnByb3BzLmlzTXVsdGksIFtdLCBudWxsKSwge1xuICAgICAgICBhY3Rpb246ICdjbGVhcicsXG4gICAgICAgIHJlbW92ZWRWYWx1ZXM6IHNlbGVjdFZhbHVlXG4gICAgICB9KTtcbiAgICB9O1xuICAgIF90aGlzLnBvcFZhbHVlID0gZnVuY3Rpb24gKCkge1xuICAgICAgdmFyIGlzTXVsdGkgPSBfdGhpcy5wcm9wcy5pc011bHRpO1xuICAgICAgdmFyIHNlbGVjdFZhbHVlID0gX3RoaXMuc3RhdGUuc2VsZWN0VmFsdWU7XG4gICAgICB2YXIgbGFzdFNlbGVjdGVkVmFsdWUgPSBzZWxlY3RWYWx1ZVtzZWxlY3RWYWx1ZS5sZW5ndGggLSAxXTtcbiAgICAgIHZhciBuZXdWYWx1ZUFycmF5ID0gc2VsZWN0VmFsdWUuc2xpY2UoMCwgc2VsZWN0VmFsdWUubGVuZ3RoIC0gMSk7XG4gICAgICB2YXIgbmV3VmFsdWUgPSB2YWx1ZVRlcm5hcnkoaXNNdWx0aSwgbmV3VmFsdWVBcnJheSwgbmV3VmFsdWVBcnJheVswXSB8fCBudWxsKTtcbiAgICAgIGlmIChsYXN0U2VsZWN0ZWRWYWx1ZSkge1xuICAgICAgICBfdGhpcy5vbkNoYW5nZShuZXdWYWx1ZSwge1xuICAgICAgICAgIGFjdGlvbjogJ3BvcC12YWx1ZScsXG4gICAgICAgICAgcmVtb3ZlZFZhbHVlOiBsYXN0U2VsZWN0ZWRWYWx1ZVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9O1xuICAgIF90aGlzLmdldEZvY3VzZWRPcHRpb25JZCA9IGZ1bmN0aW9uIChmb2N1c2VkT3B0aW9uKSB7XG4gICAgICByZXR1cm4gZ2V0Rm9jdXNlZE9wdGlvbklkKF90aGlzLnN0YXRlLmZvY3VzYWJsZU9wdGlvbnNXaXRoSWRzLCBmb2N1c2VkT3B0aW9uKTtcbiAgICB9O1xuICAgIF90aGlzLmdldEZvY3VzYWJsZU9wdGlvbnNXaXRoSWRzID0gZnVuY3Rpb24gKCkge1xuICAgICAgcmV0dXJuIGJ1aWxkRm9jdXNhYmxlT3B0aW9uc1dpdGhJZHMoYnVpbGRDYXRlZ29yaXplZE9wdGlvbnMoX3RoaXMucHJvcHMsIF90aGlzLnN0YXRlLnNlbGVjdFZhbHVlKSwgX3RoaXMuZ2V0RWxlbWVudElkKCdvcHRpb24nKSk7XG4gICAgfTtcbiAgICBfdGhpcy5nZXRWYWx1ZSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiBfdGhpcy5zdGF0ZS5zZWxlY3RWYWx1ZTtcbiAgICB9O1xuICAgIF90aGlzLmN4ID0gZnVuY3Rpb24gKCkge1xuICAgICAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFyZ3MgPSBuZXcgQXJyYXkoX2xlbiksIF9rZXkgPSAwOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgICAgIGFyZ3NbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gICAgICB9XG4gICAgICByZXR1cm4gY2xhc3NOYW1lcy5hcHBseSh2b2lkIDAsIFtfdGhpcy5wcm9wcy5jbGFzc05hbWVQcmVmaXhdLmNvbmNhdChhcmdzKSk7XG4gICAgfTtcbiAgICBfdGhpcy5nZXRPcHRpb25MYWJlbCA9IGZ1bmN0aW9uIChkYXRhKSB7XG4gICAgICByZXR1cm4gZ2V0T3B0aW9uTGFiZWwoX3RoaXMucHJvcHMsIGRhdGEpO1xuICAgIH07XG4gICAgX3RoaXMuZ2V0T3B0aW9uVmFsdWUgPSBmdW5jdGlvbiAoZGF0YSkge1xuICAgICAgcmV0dXJuIGdldE9wdGlvblZhbHVlKF90aGlzLnByb3BzLCBkYXRhKTtcbiAgICB9O1xuICAgIF90aGlzLmdldFN0eWxlcyA9IGZ1bmN0aW9uIChrZXksIHByb3BzKSB7XG4gICAgICB2YXIgdW5zdHlsZWQgPSBfdGhpcy5wcm9wcy51bnN0eWxlZDtcbiAgICAgIHZhciBiYXNlID0gZGVmYXVsdFN0eWxlc1trZXldKHByb3BzLCB1bnN0eWxlZCk7XG4gICAgICBiYXNlLmJveFNpemluZyA9ICdib3JkZXItYm94JztcbiAgICAgIHZhciBjdXN0b20gPSBfdGhpcy5wcm9wcy5zdHlsZXNba2V5XTtcbiAgICAgIHJldHVybiBjdXN0b20gPyBjdXN0b20oYmFzZSwgcHJvcHMpIDogYmFzZTtcbiAgICB9O1xuICAgIF90aGlzLmdldENsYXNzTmFtZXMgPSBmdW5jdGlvbiAoa2V5LCBwcm9wcykge1xuICAgICAgdmFyIF90aGlzJHByb3BzJGNsYXNzTmFtZSwgX3RoaXMkcHJvcHMkY2xhc3NOYW1lMjtcbiAgICAgIHJldHVybiAoX3RoaXMkcHJvcHMkY2xhc3NOYW1lID0gKF90aGlzJHByb3BzJGNsYXNzTmFtZTIgPSBfdGhpcy5wcm9wcy5jbGFzc05hbWVzKVtrZXldKSA9PT0gbnVsbCB8fCBfdGhpcyRwcm9wcyRjbGFzc05hbWUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzJHByb3BzJGNsYXNzTmFtZS5jYWxsKF90aGlzJHByb3BzJGNsYXNzTmFtZTIsIHByb3BzKTtcbiAgICB9O1xuICAgIF90aGlzLmdldEVsZW1lbnRJZCA9IGZ1bmN0aW9uIChlbGVtZW50KSB7XG4gICAgICByZXR1cm4gXCJcIi5jb25jYXQoX3RoaXMuc3RhdGUuaW5zdGFuY2VQcmVmaXgsIFwiLVwiKS5jb25jYXQoZWxlbWVudCk7XG4gICAgfTtcbiAgICBfdGhpcy5nZXRDb21wb25lbnRzID0gZnVuY3Rpb24gKCkge1xuICAgICAgcmV0dXJuIGRlZmF1bHRDb21wb25lbnRzKF90aGlzLnByb3BzKTtcbiAgICB9O1xuICAgIF90aGlzLmJ1aWxkQ2F0ZWdvcml6ZWRPcHRpb25zID0gZnVuY3Rpb24gKCkge1xuICAgICAgcmV0dXJuIGJ1aWxkQ2F0ZWdvcml6ZWRPcHRpb25zKF90aGlzLnByb3BzLCBfdGhpcy5zdGF0ZS5zZWxlY3RWYWx1ZSk7XG4gICAgfTtcbiAgICBfdGhpcy5nZXRDYXRlZ29yaXplZE9wdGlvbnMgPSBmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gX3RoaXMucHJvcHMubWVudUlzT3BlbiA/IF90aGlzLmJ1aWxkQ2F0ZWdvcml6ZWRPcHRpb25zKCkgOiBbXTtcbiAgICB9O1xuICAgIF90aGlzLmJ1aWxkRm9jdXNhYmxlT3B0aW9ucyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiBidWlsZEZvY3VzYWJsZU9wdGlvbnNGcm9tQ2F0ZWdvcml6ZWRPcHRpb25zKF90aGlzLmJ1aWxkQ2F0ZWdvcml6ZWRPcHRpb25zKCkpO1xuICAgIH07XG4gICAgX3RoaXMuZ2V0Rm9jdXNhYmxlT3B0aW9ucyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiBfdGhpcy5wcm9wcy5tZW51SXNPcGVuID8gX3RoaXMuYnVpbGRGb2N1c2FibGVPcHRpb25zKCkgOiBbXTtcbiAgICB9O1xuICAgIF90aGlzLmFyaWFPbkNoYW5nZSA9IGZ1bmN0aW9uICh2YWx1ZSwgYWN0aW9uTWV0YSkge1xuICAgICAgX3RoaXMuc2V0U3RhdGUoe1xuICAgICAgICBhcmlhU2VsZWN0aW9uOiBfb2JqZWN0U3ByZWFkKHtcbiAgICAgICAgICB2YWx1ZTogdmFsdWVcbiAgICAgICAgfSwgYWN0aW9uTWV0YSlcbiAgICAgIH0pO1xuICAgIH07XG4gICAgX3RoaXMub25NZW51TW91c2VEb3duID0gZnVuY3Rpb24gKGV2ZW50KSB7XG4gICAgICBpZiAoZXZlbnQuYnV0dG9uICE9PSAwKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgIF90aGlzLmZvY3VzSW5wdXQoKTtcbiAgICB9O1xuICAgIF90aGlzLm9uTWVudU1vdXNlTW92ZSA9IGZ1bmN0aW9uIChldmVudCkge1xuICAgICAgX3RoaXMuYmxvY2tPcHRpb25Ib3ZlciA9IGZhbHNlO1xuICAgIH07XG4gICAgX3RoaXMub25Db250cm9sTW91c2VEb3duID0gZnVuY3Rpb24gKGV2ZW50KSB7XG4gICAgICAvLyBFdmVudCBjYXB0dXJlZCBieSBkcm9wZG93biBpbmRpY2F0b3JcbiAgICAgIGlmIChldmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHZhciBvcGVuTWVudU9uQ2xpY2sgPSBfdGhpcy5wcm9wcy5vcGVuTWVudU9uQ2xpY2s7XG4gICAgICBpZiAoIV90aGlzLnN0YXRlLmlzRm9jdXNlZCkge1xuICAgICAgICBpZiAob3Blbk1lbnVPbkNsaWNrKSB7XG4gICAgICAgICAgX3RoaXMub3BlbkFmdGVyRm9jdXMgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIF90aGlzLmZvY3VzSW5wdXQoKTtcbiAgICAgIH0gZWxzZSBpZiAoIV90aGlzLnByb3BzLm1lbnVJc09wZW4pIHtcbiAgICAgICAgaWYgKG9wZW5NZW51T25DbGljaykge1xuICAgICAgICAgIF90aGlzLm9wZW5NZW51KCdmaXJzdCcpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpZiAoZXZlbnQudGFyZ2V0LnRhZ05hbWUgIT09ICdJTlBVVCcgJiYgZXZlbnQudGFyZ2V0LnRhZ05hbWUgIT09ICdURVhUQVJFQScpIHtcbiAgICAgICAgICBfdGhpcy5vbk1lbnVDbG9zZSgpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBpZiAoZXZlbnQudGFyZ2V0LnRhZ05hbWUgIT09ICdJTlBVVCcgJiYgZXZlbnQudGFyZ2V0LnRhZ05hbWUgIT09ICdURVhUQVJFQScpIHtcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIF90aGlzLm9uRHJvcGRvd25JbmRpY2F0b3JNb3VzZURvd24gPSBmdW5jdGlvbiAoZXZlbnQpIHtcbiAgICAgIC8vIGlnbm9yZSBtb3VzZSBldmVudHMgdGhhdCB3ZXJlbid0IHRyaWdnZXJlZCBieSB0aGUgcHJpbWFyeSBidXR0b25cbiAgICAgIGlmIChldmVudCAmJiBldmVudC50eXBlID09PSAnbW91c2Vkb3duJyAmJiBldmVudC5idXR0b24gIT09IDApIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgaWYgKF90aGlzLnByb3BzLmlzRGlzYWJsZWQpIHJldHVybjtcbiAgICAgIHZhciBfdGhpcyRwcm9wczQgPSBfdGhpcy5wcm9wcyxcbiAgICAgICAgaXNNdWx0aSA9IF90aGlzJHByb3BzNC5pc011bHRpLFxuICAgICAgICBtZW51SXNPcGVuID0gX3RoaXMkcHJvcHM0Lm1lbnVJc09wZW47XG4gICAgICBfdGhpcy5mb2N1c0lucHV0KCk7XG4gICAgICBpZiAobWVudUlzT3Blbikge1xuICAgICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgICAgaW5wdXRJc0hpZGRlbkFmdGVyVXBkYXRlOiAhaXNNdWx0aVxuICAgICAgICB9KTtcbiAgICAgICAgX3RoaXMub25NZW51Q2xvc2UoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIF90aGlzLm9wZW5NZW51KCdmaXJzdCcpO1xuICAgICAgfVxuICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICB9O1xuICAgIF90aGlzLm9uQ2xlYXJJbmRpY2F0b3JNb3VzZURvd24gPSBmdW5jdGlvbiAoZXZlbnQpIHtcbiAgICAgIC8vIGlnbm9yZSBtb3VzZSBldmVudHMgdGhhdCB3ZXJlbid0IHRyaWdnZXJlZCBieSB0aGUgcHJpbWFyeSBidXR0b25cbiAgICAgIGlmIChldmVudCAmJiBldmVudC50eXBlID09PSAnbW91c2Vkb3duJyAmJiBldmVudC5idXR0b24gIT09IDApIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgX3RoaXMuY2xlYXJWYWx1ZSgpO1xuICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgIF90aGlzLm9wZW5BZnRlckZvY3VzID0gZmFsc2U7XG4gICAgICBpZiAoZXZlbnQudHlwZSA9PT0gJ3RvdWNoZW5kJykge1xuICAgICAgICBfdGhpcy5mb2N1c0lucHV0KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICByZXR1cm4gX3RoaXMuZm9jdXNJbnB1dCgpO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9O1xuICAgIF90aGlzLm9uU2Nyb2xsID0gZnVuY3Rpb24gKGV2ZW50KSB7XG4gICAgICBpZiAodHlwZW9mIF90aGlzLnByb3BzLmNsb3NlTWVudU9uU2Nyb2xsID09PSAnYm9vbGVhbicpIHtcbiAgICAgICAgaWYgKGV2ZW50LnRhcmdldCBpbnN0YW5jZW9mIEhUTUxFbGVtZW50ICYmIGlzRG9jdW1lbnRFbGVtZW50KGV2ZW50LnRhcmdldCkpIHtcbiAgICAgICAgICBfdGhpcy5wcm9wcy5vbk1lbnVDbG9zZSgpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKHR5cGVvZiBfdGhpcy5wcm9wcy5jbG9zZU1lbnVPblNjcm9sbCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICBpZiAoX3RoaXMucHJvcHMuY2xvc2VNZW51T25TY3JvbGwoZXZlbnQpKSB7XG4gICAgICAgICAgX3RoaXMucHJvcHMub25NZW51Q2xvc2UoKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH07XG4gICAgX3RoaXMub25Db21wb3NpdGlvblN0YXJ0ID0gZnVuY3Rpb24gKCkge1xuICAgICAgX3RoaXMuaXNDb21wb3NpbmcgPSB0cnVlO1xuICAgIH07XG4gICAgX3RoaXMub25Db21wb3NpdGlvbkVuZCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIF90aGlzLmlzQ29tcG9zaW5nID0gZmFsc2U7XG4gICAgfTtcbiAgICBfdGhpcy5vblRvdWNoU3RhcnQgPSBmdW5jdGlvbiAoX3JlZjIpIHtcbiAgICAgIHZhciB0b3VjaGVzID0gX3JlZjIudG91Y2hlcztcbiAgICAgIHZhciB0b3VjaCA9IHRvdWNoZXMgJiYgdG91Y2hlcy5pdGVtKDApO1xuICAgICAgaWYgKCF0b3VjaCkge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBfdGhpcy5pbml0aWFsVG91Y2hYID0gdG91Y2guY2xpZW50WDtcbiAgICAgIF90aGlzLmluaXRpYWxUb3VjaFkgPSB0b3VjaC5jbGllbnRZO1xuICAgICAgX3RoaXMudXNlcklzRHJhZ2dpbmcgPSBmYWxzZTtcbiAgICB9O1xuICAgIF90aGlzLm9uVG91Y2hNb3ZlID0gZnVuY3Rpb24gKF9yZWYzKSB7XG4gICAgICB2YXIgdG91Y2hlcyA9IF9yZWYzLnRvdWNoZXM7XG4gICAgICB2YXIgdG91Y2ggPSB0b3VjaGVzICYmIHRvdWNoZXMuaXRlbSgwKTtcbiAgICAgIGlmICghdG91Y2gpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgdmFyIGRlbHRhWCA9IE1hdGguYWJzKHRvdWNoLmNsaWVudFggLSBfdGhpcy5pbml0aWFsVG91Y2hYKTtcbiAgICAgIHZhciBkZWx0YVkgPSBNYXRoLmFicyh0b3VjaC5jbGllbnRZIC0gX3RoaXMuaW5pdGlhbFRvdWNoWSk7XG4gICAgICB2YXIgbW92ZVRocmVzaG9sZCA9IDU7XG4gICAgICBfdGhpcy51c2VySXNEcmFnZ2luZyA9IGRlbHRhWCA+IG1vdmVUaHJlc2hvbGQgfHwgZGVsdGFZID4gbW92ZVRocmVzaG9sZDtcbiAgICB9O1xuICAgIF90aGlzLm9uVG91Y2hFbmQgPSBmdW5jdGlvbiAoZXZlbnQpIHtcbiAgICAgIGlmIChfdGhpcy51c2VySXNEcmFnZ2luZykgcmV0dXJuO1xuXG4gICAgICAvLyBjbG9zZSB0aGUgbWVudSBpZiB0aGUgdXNlciB0YXBzIG91dHNpZGVcbiAgICAgIC8vIHdlJ3JlIGNoZWNraW5nIG9uIGV2ZW50LnRhcmdldCBoZXJlIGluc3RlYWQgb2YgZXZlbnQuY3VycmVudFRhcmdldCwgYmVjYXVzZSB3ZSB3YW50IHRvIGFzc2VydCBpbmZvcm1hdGlvblxuICAgICAgLy8gb24gZXZlbnRzIG9uIGNoaWxkIGVsZW1lbnRzLCBub3QgdGhlIGRvY3VtZW50ICh3aGljaCB3ZSd2ZSBhdHRhY2hlZCB0aGlzIGhhbmRsZXIgdG8pLlxuICAgICAgaWYgKF90aGlzLmNvbnRyb2xSZWYgJiYgIV90aGlzLmNvbnRyb2xSZWYuY29udGFpbnMoZXZlbnQudGFyZ2V0KSAmJiBfdGhpcy5tZW51TGlzdFJlZiAmJiAhX3RoaXMubWVudUxpc3RSZWYuY29udGFpbnMoZXZlbnQudGFyZ2V0KSkge1xuICAgICAgICBfdGhpcy5ibHVySW5wdXQoKTtcbiAgICAgIH1cblxuICAgICAgLy8gcmVzZXQgbW92ZSB2YXJzXG4gICAgICBfdGhpcy5pbml0aWFsVG91Y2hYID0gMDtcbiAgICAgIF90aGlzLmluaXRpYWxUb3VjaFkgPSAwO1xuICAgIH07XG4gICAgX3RoaXMub25Db250cm9sVG91Y2hFbmQgPSBmdW5jdGlvbiAoZXZlbnQpIHtcbiAgICAgIGlmIChfdGhpcy51c2VySXNEcmFnZ2luZykgcmV0dXJuO1xuICAgICAgX3RoaXMub25Db250cm9sTW91c2VEb3duKGV2ZW50KTtcbiAgICB9O1xuICAgIF90aGlzLm9uQ2xlYXJJbmRpY2F0b3JUb3VjaEVuZCA9IGZ1bmN0aW9uIChldmVudCkge1xuICAgICAgaWYgKF90aGlzLnVzZXJJc0RyYWdnaW5nKSByZXR1cm47XG4gICAgICBfdGhpcy5vbkNsZWFySW5kaWNhdG9yTW91c2VEb3duKGV2ZW50KTtcbiAgICB9O1xuICAgIF90aGlzLm9uRHJvcGRvd25JbmRpY2F0b3JUb3VjaEVuZCA9IGZ1bmN0aW9uIChldmVudCkge1xuICAgICAgaWYgKF90aGlzLnVzZXJJc0RyYWdnaW5nKSByZXR1cm47XG4gICAgICBfdGhpcy5vbkRyb3Bkb3duSW5kaWNhdG9yTW91c2VEb3duKGV2ZW50KTtcbiAgICB9O1xuICAgIF90aGlzLmhhbmRsZUlucHV0Q2hhbmdlID0gZnVuY3Rpb24gKGV2ZW50KSB7XG4gICAgICB2YXIgcHJldklucHV0VmFsdWUgPSBfdGhpcy5wcm9wcy5pbnB1dFZhbHVlO1xuICAgICAgdmFyIGlucHV0VmFsdWUgPSBldmVudC5jdXJyZW50VGFyZ2V0LnZhbHVlO1xuICAgICAgX3RoaXMuc2V0U3RhdGUoe1xuICAgICAgICBpbnB1dElzSGlkZGVuQWZ0ZXJVcGRhdGU6IGZhbHNlXG4gICAgICB9KTtcbiAgICAgIF90aGlzLm9uSW5wdXRDaGFuZ2UoaW5wdXRWYWx1ZSwge1xuICAgICAgICBhY3Rpb246ICdpbnB1dC1jaGFuZ2UnLFxuICAgICAgICBwcmV2SW5wdXRWYWx1ZTogcHJldklucHV0VmFsdWVcbiAgICAgIH0pO1xuICAgICAgaWYgKCFfdGhpcy5wcm9wcy5tZW51SXNPcGVuKSB7XG4gICAgICAgIF90aGlzLm9uTWVudU9wZW4oKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIF90aGlzLm9uSW5wdXRGb2N1cyA9IGZ1bmN0aW9uIChldmVudCkge1xuICAgICAgaWYgKF90aGlzLnByb3BzLm9uRm9jdXMpIHtcbiAgICAgICAgX3RoaXMucHJvcHMub25Gb2N1cyhldmVudCk7XG4gICAgICB9XG4gICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIGlucHV0SXNIaWRkZW5BZnRlclVwZGF0ZTogZmFsc2UsXG4gICAgICAgIGlzRm9jdXNlZDogdHJ1ZVxuICAgICAgfSk7XG4gICAgICBpZiAoX3RoaXMub3BlbkFmdGVyRm9jdXMgfHwgX3RoaXMucHJvcHMub3Blbk1lbnVPbkZvY3VzKSB7XG4gICAgICAgIF90aGlzLm9wZW5NZW51KCdmaXJzdCcpO1xuICAgICAgfVxuICAgICAgX3RoaXMub3BlbkFmdGVyRm9jdXMgPSBmYWxzZTtcbiAgICB9O1xuICAgIF90aGlzLm9uSW5wdXRCbHVyID0gZnVuY3Rpb24gKGV2ZW50KSB7XG4gICAgICB2YXIgcHJldklucHV0VmFsdWUgPSBfdGhpcy5wcm9wcy5pbnB1dFZhbHVlO1xuICAgICAgaWYgKF90aGlzLm1lbnVMaXN0UmVmICYmIF90aGlzLm1lbnVMaXN0UmVmLmNvbnRhaW5zKGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQpKSB7XG4gICAgICAgIF90aGlzLmlucHV0UmVmLmZvY3VzKCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGlmIChfdGhpcy5wcm9wcy5vbkJsdXIpIHtcbiAgICAgICAgX3RoaXMucHJvcHMub25CbHVyKGV2ZW50KTtcbiAgICAgIH1cbiAgICAgIF90aGlzLm9uSW5wdXRDaGFuZ2UoJycsIHtcbiAgICAgICAgYWN0aW9uOiAnaW5wdXQtYmx1cicsXG4gICAgICAgIHByZXZJbnB1dFZhbHVlOiBwcmV2SW5wdXRWYWx1ZVxuICAgICAgfSk7XG4gICAgICBfdGhpcy5vbk1lbnVDbG9zZSgpO1xuICAgICAgX3RoaXMuc2V0U3RhdGUoe1xuICAgICAgICBmb2N1c2VkVmFsdWU6IG51bGwsXG4gICAgICAgIGlzRm9jdXNlZDogZmFsc2VcbiAgICAgIH0pO1xuICAgIH07XG4gICAgX3RoaXMub25PcHRpb25Ib3ZlciA9IGZ1bmN0aW9uIChmb2N1c2VkT3B0aW9uKSB7XG4gICAgICBpZiAoX3RoaXMuYmxvY2tPcHRpb25Ib3ZlciB8fCBfdGhpcy5zdGF0ZS5mb2N1c2VkT3B0aW9uID09PSBmb2N1c2VkT3B0aW9uKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHZhciBvcHRpb25zID0gX3RoaXMuZ2V0Rm9jdXNhYmxlT3B0aW9ucygpO1xuICAgICAgdmFyIGZvY3VzZWRPcHRpb25JbmRleCA9IG9wdGlvbnMuaW5kZXhPZihmb2N1c2VkT3B0aW9uKTtcbiAgICAgIF90aGlzLnNldFN0YXRlKHtcbiAgICAgICAgZm9jdXNlZE9wdGlvbjogZm9jdXNlZE9wdGlvbixcbiAgICAgICAgZm9jdXNlZE9wdGlvbklkOiBmb2N1c2VkT3B0aW9uSW5kZXggPiAtMSA/IF90aGlzLmdldEZvY3VzZWRPcHRpb25JZChmb2N1c2VkT3B0aW9uKSA6IG51bGxcbiAgICAgIH0pO1xuICAgIH07XG4gICAgX3RoaXMuc2hvdWxkSGlkZVNlbGVjdGVkT3B0aW9ucyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiBzaG91bGRIaWRlU2VsZWN0ZWRPcHRpb25zKF90aGlzLnByb3BzKTtcbiAgICB9O1xuICAgIF90aGlzLm9uVmFsdWVJbnB1dEZvY3VzID0gZnVuY3Rpb24gKGUpIHtcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICBfdGhpcy5mb2N1cygpO1xuICAgIH07XG4gICAgX3RoaXMub25LZXlEb3duID0gZnVuY3Rpb24gKGV2ZW50KSB7XG4gICAgICB2YXIgX3RoaXMkcHJvcHM1ID0gX3RoaXMucHJvcHMsXG4gICAgICAgIGlzTXVsdGkgPSBfdGhpcyRwcm9wczUuaXNNdWx0aSxcbiAgICAgICAgYmFja3NwYWNlUmVtb3Zlc1ZhbHVlID0gX3RoaXMkcHJvcHM1LmJhY2tzcGFjZVJlbW92ZXNWYWx1ZSxcbiAgICAgICAgZXNjYXBlQ2xlYXJzVmFsdWUgPSBfdGhpcyRwcm9wczUuZXNjYXBlQ2xlYXJzVmFsdWUsXG4gICAgICAgIGlucHV0VmFsdWUgPSBfdGhpcyRwcm9wczUuaW5wdXRWYWx1ZSxcbiAgICAgICAgaXNDbGVhcmFibGUgPSBfdGhpcyRwcm9wczUuaXNDbGVhcmFibGUsXG4gICAgICAgIGlzRGlzYWJsZWQgPSBfdGhpcyRwcm9wczUuaXNEaXNhYmxlZCxcbiAgICAgICAgbWVudUlzT3BlbiA9IF90aGlzJHByb3BzNS5tZW51SXNPcGVuLFxuICAgICAgICBvbktleURvd24gPSBfdGhpcyRwcm9wczUub25LZXlEb3duLFxuICAgICAgICB0YWJTZWxlY3RzVmFsdWUgPSBfdGhpcyRwcm9wczUudGFiU2VsZWN0c1ZhbHVlLFxuICAgICAgICBvcGVuTWVudU9uRm9jdXMgPSBfdGhpcyRwcm9wczUub3Blbk1lbnVPbkZvY3VzO1xuICAgICAgdmFyIF90aGlzJHN0YXRlID0gX3RoaXMuc3RhdGUsXG4gICAgICAgIGZvY3VzZWRPcHRpb24gPSBfdGhpcyRzdGF0ZS5mb2N1c2VkT3B0aW9uLFxuICAgICAgICBmb2N1c2VkVmFsdWUgPSBfdGhpcyRzdGF0ZS5mb2N1c2VkVmFsdWUsXG4gICAgICAgIHNlbGVjdFZhbHVlID0gX3RoaXMkc3RhdGUuc2VsZWN0VmFsdWU7XG4gICAgICBpZiAoaXNEaXNhYmxlZCkgcmV0dXJuO1xuICAgICAgaWYgKHR5cGVvZiBvbktleURvd24gPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgb25LZXlEb3duKGV2ZW50KTtcbiAgICAgICAgaWYgKGV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gQmxvY2sgb3B0aW9uIGhvdmVyIGV2ZW50cyB3aGVuIHRoZSB1c2VyIGhhcyBqdXN0IHByZXNzZWQgYSBrZXlcbiAgICAgIF90aGlzLmJsb2NrT3B0aW9uSG92ZXIgPSB0cnVlO1xuICAgICAgc3dpdGNoIChldmVudC5rZXkpIHtcbiAgICAgICAgY2FzZSAnQXJyb3dMZWZ0JzpcbiAgICAgICAgICBpZiAoIWlzTXVsdGkgfHwgaW5wdXRWYWx1ZSkgcmV0dXJuO1xuICAgICAgICAgIF90aGlzLmZvY3VzVmFsdWUoJ3ByZXZpb3VzJyk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ0Fycm93UmlnaHQnOlxuICAgICAgICAgIGlmICghaXNNdWx0aSB8fCBpbnB1dFZhbHVlKSByZXR1cm47XG4gICAgICAgICAgX3RoaXMuZm9jdXNWYWx1ZSgnbmV4dCcpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdEZWxldGUnOlxuICAgICAgICBjYXNlICdCYWNrc3BhY2UnOlxuICAgICAgICAgIGlmIChpbnB1dFZhbHVlKSByZXR1cm47XG4gICAgICAgICAgaWYgKGZvY3VzZWRWYWx1ZSkge1xuICAgICAgICAgICAgX3RoaXMucmVtb3ZlVmFsdWUoZm9jdXNlZFZhbHVlKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgaWYgKCFiYWNrc3BhY2VSZW1vdmVzVmFsdWUpIHJldHVybjtcbiAgICAgICAgICAgIGlmIChpc011bHRpKSB7XG4gICAgICAgICAgICAgIF90aGlzLnBvcFZhbHVlKCk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKGlzQ2xlYXJhYmxlKSB7XG4gICAgICAgICAgICAgIF90aGlzLmNsZWFyVmFsdWUoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ1RhYic6XG4gICAgICAgICAgaWYgKF90aGlzLmlzQ29tcG9zaW5nKSByZXR1cm47XG4gICAgICAgICAgaWYgKGV2ZW50LnNoaWZ0S2V5IHx8ICFtZW51SXNPcGVuIHx8ICF0YWJTZWxlY3RzVmFsdWUgfHwgIWZvY3VzZWRPcHRpb24gfHxcbiAgICAgICAgICAvLyBkb24ndCBjYXB0dXJlIHRoZSBldmVudCBpZiB0aGUgbWVudSBvcGVucyBvbiBmb2N1cyBhbmQgdGhlIGZvY3VzZWRcbiAgICAgICAgICAvLyBvcHRpb24gaXMgYWxyZWFkeSBzZWxlY3RlZDsgaXQgYnJlYWtzIHRoZSBmbG93IG9mIG5hdmlnYXRpb25cbiAgICAgICAgICBvcGVuTWVudU9uRm9jdXMgJiYgX3RoaXMuaXNPcHRpb25TZWxlY3RlZChmb2N1c2VkT3B0aW9uLCBzZWxlY3RWYWx1ZSkpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICB9XG4gICAgICAgICAgX3RoaXMuc2VsZWN0T3B0aW9uKGZvY3VzZWRPcHRpb24pO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdFbnRlcic6XG4gICAgICAgICAgaWYgKGV2ZW50LmtleUNvZGUgPT09IDIyOSkge1xuICAgICAgICAgICAgLy8gaWdub3JlIHRoZSBrZXlkb3duIGV2ZW50IGZyb20gYW4gSW5wdXQgTWV0aG9kIEVkaXRvcihJTUUpXG4gICAgICAgICAgICAvLyByZWYuIGh0dHBzOi8vd3d3LnczLm9yZy9UUi91aWV2ZW50cy8jZGV0ZXJtaW5lLWtleWRvd24ta2V5dXAta2V5Q29kZVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChtZW51SXNPcGVuKSB7XG4gICAgICAgICAgICBpZiAoIWZvY3VzZWRPcHRpb24pIHJldHVybjtcbiAgICAgICAgICAgIGlmIChfdGhpcy5pc0NvbXBvc2luZykgcmV0dXJuO1xuICAgICAgICAgICAgX3RoaXMuc2VsZWN0T3B0aW9uKGZvY3VzZWRPcHRpb24pO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgY2FzZSAnRXNjYXBlJzpcbiAgICAgICAgICBpZiAobWVudUlzT3Blbikge1xuICAgICAgICAgICAgX3RoaXMuc2V0U3RhdGUoe1xuICAgICAgICAgICAgICBpbnB1dElzSGlkZGVuQWZ0ZXJVcGRhdGU6IGZhbHNlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIF90aGlzLm9uSW5wdXRDaGFuZ2UoJycsIHtcbiAgICAgICAgICAgICAgYWN0aW9uOiAnbWVudS1jbG9zZScsXG4gICAgICAgICAgICAgIHByZXZJbnB1dFZhbHVlOiBpbnB1dFZhbHVlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIF90aGlzLm9uTWVudUNsb3NlKCk7XG4gICAgICAgICAgfSBlbHNlIGlmIChpc0NsZWFyYWJsZSAmJiBlc2NhcGVDbGVhcnNWYWx1ZSkge1xuICAgICAgICAgICAgX3RoaXMuY2xlYXJWYWx1ZSgpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnICc6XG4gICAgICAgICAgLy8gc3BhY2VcbiAgICAgICAgICBpZiAoaW5wdXRWYWx1ZSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAoIW1lbnVJc09wZW4pIHtcbiAgICAgICAgICAgIF90aGlzLm9wZW5NZW51KCdmaXJzdCcpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmICghZm9jdXNlZE9wdGlvbikgcmV0dXJuO1xuICAgICAgICAgIF90aGlzLnNlbGVjdE9wdGlvbihmb2N1c2VkT3B0aW9uKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnQXJyb3dVcCc6XG4gICAgICAgICAgaWYgKG1lbnVJc09wZW4pIHtcbiAgICAgICAgICAgIF90aGlzLmZvY3VzT3B0aW9uKCd1cCcpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBfdGhpcy5vcGVuTWVudSgnbGFzdCcpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnQXJyb3dEb3duJzpcbiAgICAgICAgICBpZiAobWVudUlzT3Blbikge1xuICAgICAgICAgICAgX3RoaXMuZm9jdXNPcHRpb24oJ2Rvd24nKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgX3RoaXMub3Blbk1lbnUoJ2ZpcnN0Jyk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdQYWdlVXAnOlxuICAgICAgICAgIGlmICghbWVudUlzT3BlbikgcmV0dXJuO1xuICAgICAgICAgIF90aGlzLmZvY3VzT3B0aW9uKCdwYWdldXAnKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnUGFnZURvd24nOlxuICAgICAgICAgIGlmICghbWVudUlzT3BlbikgcmV0dXJuO1xuICAgICAgICAgIF90aGlzLmZvY3VzT3B0aW9uKCdwYWdlZG93bicpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdIb21lJzpcbiAgICAgICAgICBpZiAoIW1lbnVJc09wZW4pIHJldHVybjtcbiAgICAgICAgICBfdGhpcy5mb2N1c09wdGlvbignZmlyc3QnKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnRW5kJzpcbiAgICAgICAgICBpZiAoIW1lbnVJc09wZW4pIHJldHVybjtcbiAgICAgICAgICBfdGhpcy5mb2N1c09wdGlvbignbGFzdCcpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgfTtcbiAgICBfdGhpcy5zdGF0ZS5pbnN0YW5jZVByZWZpeCA9ICdyZWFjdC1zZWxlY3QtJyArIChfdGhpcy5wcm9wcy5pbnN0YW5jZUlkIHx8ICsraW5zdGFuY2VJZCk7XG4gICAgX3RoaXMuc3RhdGUuc2VsZWN0VmFsdWUgPSBjbGVhblZhbHVlKF9wcm9wcy52YWx1ZSk7XG4gICAgLy8gU2V0IGZvY3VzZWRPcHRpb24gaWYgbWVudUlzT3BlbiBpcyBzZXQgb24gaW5pdCAoZS5nLiBkZWZhdWx0TWVudUlzT3BlbilcbiAgICBpZiAoX3Byb3BzLm1lbnVJc09wZW4gJiYgX3RoaXMuc3RhdGUuc2VsZWN0VmFsdWUubGVuZ3RoKSB7XG4gICAgICB2YXIgZm9jdXNhYmxlT3B0aW9uc1dpdGhJZHMgPSBfdGhpcy5nZXRGb2N1c2FibGVPcHRpb25zV2l0aElkcygpO1xuICAgICAgdmFyIGZvY3VzYWJsZU9wdGlvbnMgPSBfdGhpcy5idWlsZEZvY3VzYWJsZU9wdGlvbnMoKTtcbiAgICAgIHZhciBvcHRpb25JbmRleCA9IGZvY3VzYWJsZU9wdGlvbnMuaW5kZXhPZihfdGhpcy5zdGF0ZS5zZWxlY3RWYWx1ZVswXSk7XG4gICAgICBfdGhpcy5zdGF0ZS5mb2N1c2FibGVPcHRpb25zV2l0aElkcyA9IGZvY3VzYWJsZU9wdGlvbnNXaXRoSWRzO1xuICAgICAgX3RoaXMuc3RhdGUuZm9jdXNlZE9wdGlvbiA9IGZvY3VzYWJsZU9wdGlvbnNbb3B0aW9uSW5kZXhdO1xuICAgICAgX3RoaXMuc3RhdGUuZm9jdXNlZE9wdGlvbklkID0gZ2V0Rm9jdXNlZE9wdGlvbklkKGZvY3VzYWJsZU9wdGlvbnNXaXRoSWRzLCBmb2N1c2FibGVPcHRpb25zW29wdGlvbkluZGV4XSk7XG4gICAgfVxuICAgIHJldHVybiBfdGhpcztcbiAgfVxuICBfY3JlYXRlQ2xhc3MoU2VsZWN0LCBbe1xuICAgIGtleTogXCJjb21wb25lbnREaWRNb3VudFwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBjb21wb25lbnREaWRNb3VudCgpIHtcbiAgICAgIHRoaXMuc3RhcnRMaXN0ZW5pbmdDb21wb3NpdGlvbigpO1xuICAgICAgdGhpcy5zdGFydExpc3RlbmluZ1RvVG91Y2goKTtcbiAgICAgIGlmICh0aGlzLnByb3BzLmNsb3NlTWVudU9uU2Nyb2xsICYmIGRvY3VtZW50ICYmIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIpIHtcbiAgICAgICAgLy8gTGlzdGVuIHRvIGFsbCBzY3JvbGwgZXZlbnRzLCBhbmQgZmlsdGVyIHRoZW0gb3V0IGluc2lkZSBvZiAnb25TY3JvbGwnXG4gICAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIHRoaXMub25TY3JvbGwsIHRydWUpO1xuICAgICAgfVxuICAgICAgaWYgKHRoaXMucHJvcHMuYXV0b0ZvY3VzKSB7XG4gICAgICAgIHRoaXMuZm9jdXNJbnB1dCgpO1xuICAgICAgfVxuXG4gICAgICAvLyBTY3JvbGwgZm9jdXNlZE9wdGlvbiBpbnRvIHZpZXcgaWYgbWVudUlzT3BlbiBpcyBzZXQgb24gbW91bnQgKGUuZy4gZGVmYXVsdE1lbnVJc09wZW4pXG4gICAgICBpZiAodGhpcy5wcm9wcy5tZW51SXNPcGVuICYmIHRoaXMuc3RhdGUuZm9jdXNlZE9wdGlvbiAmJiB0aGlzLm1lbnVMaXN0UmVmICYmIHRoaXMuZm9jdXNlZE9wdGlvblJlZikge1xuICAgICAgICBzY3JvbGxJbnRvVmlldyh0aGlzLm1lbnVMaXN0UmVmLCB0aGlzLmZvY3VzZWRPcHRpb25SZWYpO1xuICAgICAgfVxuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJjb21wb25lbnREaWRVcGRhdGVcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gY29tcG9uZW50RGlkVXBkYXRlKHByZXZQcm9wcykge1xuICAgICAgdmFyIF90aGlzJHByb3BzNiA9IHRoaXMucHJvcHMsXG4gICAgICAgIGlzRGlzYWJsZWQgPSBfdGhpcyRwcm9wczYuaXNEaXNhYmxlZCxcbiAgICAgICAgbWVudUlzT3BlbiA9IF90aGlzJHByb3BzNi5tZW51SXNPcGVuO1xuICAgICAgdmFyIGlzRm9jdXNlZCA9IHRoaXMuc3RhdGUuaXNGb2N1c2VkO1xuICAgICAgaWYgKFxuICAgICAgLy8gZW5zdXJlIGZvY3VzIGlzIHJlc3RvcmVkIGNvcnJlY3RseSB3aGVuIHRoZSBjb250cm9sIGJlY29tZXMgZW5hYmxlZFxuICAgICAgaXNGb2N1c2VkICYmICFpc0Rpc2FibGVkICYmIHByZXZQcm9wcy5pc0Rpc2FibGVkIHx8XG4gICAgICAvLyBlbnN1cmUgZm9jdXMgaXMgb24gdGhlIElucHV0IHdoZW4gdGhlIG1lbnUgb3BlbnNcbiAgICAgIGlzRm9jdXNlZCAmJiBtZW51SXNPcGVuICYmICFwcmV2UHJvcHMubWVudUlzT3Blbikge1xuICAgICAgICB0aGlzLmZvY3VzSW5wdXQoKTtcbiAgICAgIH1cbiAgICAgIGlmIChpc0ZvY3VzZWQgJiYgaXNEaXNhYmxlZCAmJiAhcHJldlByb3BzLmlzRGlzYWJsZWQpIHtcbiAgICAgICAgLy8gZW5zdXJlIHNlbGVjdCBzdGF0ZSBnZXRzIGJsdXJyZWQgaW4gY2FzZSBTZWxlY3QgaXMgcHJvZ3JhbW1hdGljYWxseSBkaXNhYmxlZCB3aGlsZSBmb2N1c2VkXG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC9uby1kaWQtdXBkYXRlLXNldC1zdGF0ZVxuICAgICAgICB0aGlzLnNldFN0YXRlKHtcbiAgICAgICAgICBpc0ZvY3VzZWQ6IGZhbHNlXG4gICAgICAgIH0sIHRoaXMub25NZW51Q2xvc2UpO1xuICAgICAgfSBlbHNlIGlmICghaXNGb2N1c2VkICYmICFpc0Rpc2FibGVkICYmIHByZXZQcm9wcy5pc0Rpc2FibGVkICYmIHRoaXMuaW5wdXRSZWYgPT09IGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQpIHtcbiAgICAgICAgLy8gZW5zdXJlIHNlbGVjdCBzdGF0ZSBnZXRzIGZvY3VzZWQgaW4gY2FzZSBTZWxlY3QgaXMgcHJvZ3JhbWF0aWNhbGx5IHJlLWVuYWJsZWQgd2hpbGUgZm9jdXNlZCAoRmlyZWZveClcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0L25vLWRpZC11cGRhdGUtc2V0LXN0YXRlXG4gICAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICAgIGlzRm9jdXNlZDogdHJ1ZVxuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gc2Nyb2xsIHRoZSBmb2N1c2VkIG9wdGlvbiBpbnRvIHZpZXcgaWYgbmVjZXNzYXJ5XG4gICAgICBpZiAodGhpcy5tZW51TGlzdFJlZiAmJiB0aGlzLmZvY3VzZWRPcHRpb25SZWYgJiYgdGhpcy5zY3JvbGxUb0ZvY3VzZWRPcHRpb25PblVwZGF0ZSkge1xuICAgICAgICBzY3JvbGxJbnRvVmlldyh0aGlzLm1lbnVMaXN0UmVmLCB0aGlzLmZvY3VzZWRPcHRpb25SZWYpO1xuICAgICAgICB0aGlzLnNjcm9sbFRvRm9jdXNlZE9wdGlvbk9uVXBkYXRlID0gZmFsc2U7XG4gICAgICB9XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcImNvbXBvbmVudFdpbGxVbm1vdW50XCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIGNvbXBvbmVudFdpbGxVbm1vdW50KCkge1xuICAgICAgdGhpcy5zdG9wTGlzdGVuaW5nQ29tcG9zaXRpb24oKTtcbiAgICAgIHRoaXMuc3RvcExpc3RlbmluZ1RvVG91Y2goKTtcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIHRoaXMub25TY3JvbGwsIHRydWUpO1xuICAgIH1cblxuICAgIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAgIC8vIENvbnN1bWVyIEhhbmRsZXJzXG4gICAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIH0sIHtcbiAgICBrZXk6IFwib25NZW51T3BlblwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBvbk1lbnVPcGVuKCkge1xuICAgICAgdGhpcy5wcm9wcy5vbk1lbnVPcGVuKCk7XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcIm9uTWVudUNsb3NlXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIG9uTWVudUNsb3NlKCkge1xuICAgICAgdGhpcy5vbklucHV0Q2hhbmdlKCcnLCB7XG4gICAgICAgIGFjdGlvbjogJ21lbnUtY2xvc2UnLFxuICAgICAgICBwcmV2SW5wdXRWYWx1ZTogdGhpcy5wcm9wcy5pbnB1dFZhbHVlXG4gICAgICB9KTtcbiAgICAgIHRoaXMucHJvcHMub25NZW51Q2xvc2UoKTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwib25JbnB1dENoYW5nZVwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBvbklucHV0Q2hhbmdlKG5ld1ZhbHVlLCBhY3Rpb25NZXRhKSB7XG4gICAgICB0aGlzLnByb3BzLm9uSW5wdXRDaGFuZ2UobmV3VmFsdWUsIGFjdGlvbk1ldGEpO1xuICAgIH1cblxuICAgIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAgIC8vIE1ldGhvZHNcbiAgICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgfSwge1xuICAgIGtleTogXCJmb2N1c0lucHV0XCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIGZvY3VzSW5wdXQoKSB7XG4gICAgICBpZiAoIXRoaXMuaW5wdXRSZWYpIHJldHVybjtcbiAgICAgIHRoaXMuaW5wdXRSZWYuZm9jdXMoKTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiYmx1cklucHV0XCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIGJsdXJJbnB1dCgpIHtcbiAgICAgIGlmICghdGhpcy5pbnB1dFJlZikgcmV0dXJuO1xuICAgICAgdGhpcy5pbnB1dFJlZi5ibHVyKCk7XG4gICAgfVxuXG4gICAgLy8gYWxpYXNlZCBmb3IgY29uc3VtZXJzXG4gIH0sIHtcbiAgICBrZXk6IFwib3Blbk1lbnVcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gb3Blbk1lbnUoZm9jdXNPcHRpb24pIHtcbiAgICAgIHZhciBfdGhpczIgPSB0aGlzO1xuICAgICAgdmFyIF90aGlzJHN0YXRlMiA9IHRoaXMuc3RhdGUsXG4gICAgICAgIHNlbGVjdFZhbHVlID0gX3RoaXMkc3RhdGUyLnNlbGVjdFZhbHVlLFxuICAgICAgICBpc0ZvY3VzZWQgPSBfdGhpcyRzdGF0ZTIuaXNGb2N1c2VkO1xuICAgICAgdmFyIGZvY3VzYWJsZU9wdGlvbnMgPSB0aGlzLmJ1aWxkRm9jdXNhYmxlT3B0aW9ucygpO1xuICAgICAgdmFyIG9wZW5BdEluZGV4ID0gZm9jdXNPcHRpb24gPT09ICdmaXJzdCcgPyAwIDogZm9jdXNhYmxlT3B0aW9ucy5sZW5ndGggLSAxO1xuICAgICAgaWYgKCF0aGlzLnByb3BzLmlzTXVsdGkpIHtcbiAgICAgICAgdmFyIHNlbGVjdGVkSW5kZXggPSBmb2N1c2FibGVPcHRpb25zLmluZGV4T2Yoc2VsZWN0VmFsdWVbMF0pO1xuICAgICAgICBpZiAoc2VsZWN0ZWRJbmRleCA+IC0xKSB7XG4gICAgICAgICAgb3BlbkF0SW5kZXggPSBzZWxlY3RlZEluZGV4O1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIG9ubHkgc2Nyb2xsIGlmIHRoZSBtZW51IGlzbid0IGFscmVhZHkgb3BlblxuICAgICAgdGhpcy5zY3JvbGxUb0ZvY3VzZWRPcHRpb25PblVwZGF0ZSA9ICEoaXNGb2N1c2VkICYmIHRoaXMubWVudUxpc3RSZWYpO1xuICAgICAgdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIGlucHV0SXNIaWRkZW5BZnRlclVwZGF0ZTogZmFsc2UsXG4gICAgICAgIGZvY3VzZWRWYWx1ZTogbnVsbCxcbiAgICAgICAgZm9jdXNlZE9wdGlvbjogZm9jdXNhYmxlT3B0aW9uc1tvcGVuQXRJbmRleF0sXG4gICAgICAgIGZvY3VzZWRPcHRpb25JZDogdGhpcy5nZXRGb2N1c2VkT3B0aW9uSWQoZm9jdXNhYmxlT3B0aW9uc1tvcGVuQXRJbmRleF0pXG4gICAgICB9LCBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBfdGhpczIub25NZW51T3BlbigpO1xuICAgICAgfSk7XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcImZvY3VzVmFsdWVcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gZm9jdXNWYWx1ZShkaXJlY3Rpb24pIHtcbiAgICAgIHZhciBfdGhpcyRzdGF0ZTMgPSB0aGlzLnN0YXRlLFxuICAgICAgICBzZWxlY3RWYWx1ZSA9IF90aGlzJHN0YXRlMy5zZWxlY3RWYWx1ZSxcbiAgICAgICAgZm9jdXNlZFZhbHVlID0gX3RoaXMkc3RhdGUzLmZvY3VzZWRWYWx1ZTtcblxuICAgICAgLy8gT25seSBtdWx0aXNlbGVjdHMgc3VwcG9ydCB2YWx1ZSBmb2N1c2luZ1xuICAgICAgaWYgKCF0aGlzLnByb3BzLmlzTXVsdGkpIHJldHVybjtcbiAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICBmb2N1c2VkT3B0aW9uOiBudWxsXG4gICAgICB9KTtcbiAgICAgIHZhciBmb2N1c2VkSW5kZXggPSBzZWxlY3RWYWx1ZS5pbmRleE9mKGZvY3VzZWRWYWx1ZSk7XG4gICAgICBpZiAoIWZvY3VzZWRWYWx1ZSkge1xuICAgICAgICBmb2N1c2VkSW5kZXggPSAtMTtcbiAgICAgIH1cbiAgICAgIHZhciBsYXN0SW5kZXggPSBzZWxlY3RWYWx1ZS5sZW5ndGggLSAxO1xuICAgICAgdmFyIG5leHRGb2N1cyA9IC0xO1xuICAgICAgaWYgKCFzZWxlY3RWYWx1ZS5sZW5ndGgpIHJldHVybjtcbiAgICAgIHN3aXRjaCAoZGlyZWN0aW9uKSB7XG4gICAgICAgIGNhc2UgJ3ByZXZpb3VzJzpcbiAgICAgICAgICBpZiAoZm9jdXNlZEluZGV4ID09PSAwKSB7XG4gICAgICAgICAgICAvLyBkb24ndCBjeWNsZSBmcm9tIHRoZSBzdGFydCB0byB0aGUgZW5kXG4gICAgICAgICAgICBuZXh0Rm9jdXMgPSAwO1xuICAgICAgICAgIH0gZWxzZSBpZiAoZm9jdXNlZEluZGV4ID09PSAtMSkge1xuICAgICAgICAgICAgLy8gaWYgbm90aGluZyBpcyBmb2N1c2VkLCBmb2N1cyB0aGUgbGFzdCB2YWx1ZSBmaXJzdFxuICAgICAgICAgICAgbmV4dEZvY3VzID0gbGFzdEluZGV4O1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBuZXh0Rm9jdXMgPSBmb2N1c2VkSW5kZXggLSAxO1xuICAgICAgICAgIH1cbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnbmV4dCc6XG4gICAgICAgICAgaWYgKGZvY3VzZWRJbmRleCA+IC0xICYmIGZvY3VzZWRJbmRleCA8IGxhc3RJbmRleCkge1xuICAgICAgICAgICAgbmV4dEZvY3VzID0gZm9jdXNlZEluZGV4ICsgMTtcbiAgICAgICAgICB9XG4gICAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICB0aGlzLnNldFN0YXRlKHtcbiAgICAgICAgaW5wdXRJc0hpZGRlbjogbmV4dEZvY3VzICE9PSAtMSxcbiAgICAgICAgZm9jdXNlZFZhbHVlOiBzZWxlY3RWYWx1ZVtuZXh0Rm9jdXNdXG4gICAgICB9KTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiZm9jdXNPcHRpb25cIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gZm9jdXNPcHRpb24oKSB7XG4gICAgICB2YXIgZGlyZWN0aW9uID0gYXJndW1lbnRzLmxlbmd0aCA+IDAgJiYgYXJndW1lbnRzWzBdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMF0gOiAnZmlyc3QnO1xuICAgICAgdmFyIHBhZ2VTaXplID0gdGhpcy5wcm9wcy5wYWdlU2l6ZTtcbiAgICAgIHZhciBmb2N1c2VkT3B0aW9uID0gdGhpcy5zdGF0ZS5mb2N1c2VkT3B0aW9uO1xuICAgICAgdmFyIG9wdGlvbnMgPSB0aGlzLmdldEZvY3VzYWJsZU9wdGlvbnMoKTtcbiAgICAgIGlmICghb3B0aW9ucy5sZW5ndGgpIHJldHVybjtcbiAgICAgIHZhciBuZXh0Rm9jdXMgPSAwOyAvLyBoYW5kbGVzICdmaXJzdCdcbiAgICAgIHZhciBmb2N1c2VkSW5kZXggPSBvcHRpb25zLmluZGV4T2YoZm9jdXNlZE9wdGlvbik7XG4gICAgICBpZiAoIWZvY3VzZWRPcHRpb24pIHtcbiAgICAgICAgZm9jdXNlZEluZGV4ID0gLTE7XG4gICAgICB9XG4gICAgICBpZiAoZGlyZWN0aW9uID09PSAndXAnKSB7XG4gICAgICAgIG5leHRGb2N1cyA9IGZvY3VzZWRJbmRleCA+IDAgPyBmb2N1c2VkSW5kZXggLSAxIDogb3B0aW9ucy5sZW5ndGggLSAxO1xuICAgICAgfSBlbHNlIGlmIChkaXJlY3Rpb24gPT09ICdkb3duJykge1xuICAgICAgICBuZXh0Rm9jdXMgPSAoZm9jdXNlZEluZGV4ICsgMSkgJSBvcHRpb25zLmxlbmd0aDtcbiAgICAgIH0gZWxzZSBpZiAoZGlyZWN0aW9uID09PSAncGFnZXVwJykge1xuICAgICAgICBuZXh0Rm9jdXMgPSBmb2N1c2VkSW5kZXggLSBwYWdlU2l6ZTtcbiAgICAgICAgaWYgKG5leHRGb2N1cyA8IDApIG5leHRGb2N1cyA9IDA7XG4gICAgICB9IGVsc2UgaWYgKGRpcmVjdGlvbiA9PT0gJ3BhZ2Vkb3duJykge1xuICAgICAgICBuZXh0Rm9jdXMgPSBmb2N1c2VkSW5kZXggKyBwYWdlU2l6ZTtcbiAgICAgICAgaWYgKG5leHRGb2N1cyA+IG9wdGlvbnMubGVuZ3RoIC0gMSkgbmV4dEZvY3VzID0gb3B0aW9ucy5sZW5ndGggLSAxO1xuICAgICAgfSBlbHNlIGlmIChkaXJlY3Rpb24gPT09ICdsYXN0Jykge1xuICAgICAgICBuZXh0Rm9jdXMgPSBvcHRpb25zLmxlbmd0aCAtIDE7XG4gICAgICB9XG4gICAgICB0aGlzLnNjcm9sbFRvRm9jdXNlZE9wdGlvbk9uVXBkYXRlID0gdHJ1ZTtcbiAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICBmb2N1c2VkT3B0aW9uOiBvcHRpb25zW25leHRGb2N1c10sXG4gICAgICAgIGZvY3VzZWRWYWx1ZTogbnVsbCxcbiAgICAgICAgZm9jdXNlZE9wdGlvbklkOiB0aGlzLmdldEZvY3VzZWRPcHRpb25JZChvcHRpb25zW25leHRGb2N1c10pXG4gICAgICB9KTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiZ2V0VGhlbWVcIixcbiAgICB2YWx1ZTpcbiAgICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgICAvLyBHZXR0ZXJzXG4gICAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbiAgICBmdW5jdGlvbiBnZXRUaGVtZSgpIHtcbiAgICAgIC8vIFVzZSB0aGUgZGVmYXVsdCB0aGVtZSBpZiB0aGVyZSBhcmUgbm8gY3VzdG9taXNhdGlvbnMuXG4gICAgICBpZiAoIXRoaXMucHJvcHMudGhlbWUpIHtcbiAgICAgICAgcmV0dXJuIGRlZmF1bHRUaGVtZTtcbiAgICAgIH1cbiAgICAgIC8vIElmIHRoZSB0aGVtZSBwcm9wIGlzIGEgZnVuY3Rpb24sIGFzc3VtZSB0aGUgZnVuY3Rpb25cbiAgICAgIC8vIGtub3dzIGhvdyB0byBtZXJnZSB0aGUgcGFzc2VkLWluIGRlZmF1bHQgdGhlbWUgd2l0aFxuICAgICAgLy8gaXRzIG93biBtb2RpZmljYXRpb25zLlxuICAgICAgaWYgKHR5cGVvZiB0aGlzLnByb3BzLnRoZW1lID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnByb3BzLnRoZW1lKGRlZmF1bHRUaGVtZSk7XG4gICAgICB9XG4gICAgICAvLyBPdGhlcndpc2UsIGlmIGEgcGxhaW4gdGhlbWUgb2JqZWN0IHdhcyBwYXNzZWQgaW4sXG4gICAgICAvLyBvdmVybGF5IGl0IHdpdGggdGhlIGRlZmF1bHQgdGhlbWUuXG4gICAgICByZXR1cm4gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBkZWZhdWx0VGhlbWUpLCB0aGlzLnByb3BzLnRoZW1lKTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiZ2V0Q29tbW9uUHJvcHNcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gZ2V0Q29tbW9uUHJvcHMoKSB7XG4gICAgICB2YXIgY2xlYXJWYWx1ZSA9IHRoaXMuY2xlYXJWYWx1ZSxcbiAgICAgICAgY3ggPSB0aGlzLmN4LFxuICAgICAgICBnZXRTdHlsZXMgPSB0aGlzLmdldFN0eWxlcyxcbiAgICAgICAgZ2V0Q2xhc3NOYW1lcyA9IHRoaXMuZ2V0Q2xhc3NOYW1lcyxcbiAgICAgICAgZ2V0VmFsdWUgPSB0aGlzLmdldFZhbHVlLFxuICAgICAgICBzZWxlY3RPcHRpb24gPSB0aGlzLnNlbGVjdE9wdGlvbixcbiAgICAgICAgc2V0VmFsdWUgPSB0aGlzLnNldFZhbHVlLFxuICAgICAgICBwcm9wcyA9IHRoaXMucHJvcHM7XG4gICAgICB2YXIgaXNNdWx0aSA9IHByb3BzLmlzTXVsdGksXG4gICAgICAgIGlzUnRsID0gcHJvcHMuaXNSdGwsXG4gICAgICAgIG9wdGlvbnMgPSBwcm9wcy5vcHRpb25zO1xuICAgICAgdmFyIGhhc1ZhbHVlID0gdGhpcy5oYXNWYWx1ZSgpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgY2xlYXJWYWx1ZTogY2xlYXJWYWx1ZSxcbiAgICAgICAgY3g6IGN4LFxuICAgICAgICBnZXRTdHlsZXM6IGdldFN0eWxlcyxcbiAgICAgICAgZ2V0Q2xhc3NOYW1lczogZ2V0Q2xhc3NOYW1lcyxcbiAgICAgICAgZ2V0VmFsdWU6IGdldFZhbHVlLFxuICAgICAgICBoYXNWYWx1ZTogaGFzVmFsdWUsXG4gICAgICAgIGlzTXVsdGk6IGlzTXVsdGksXG4gICAgICAgIGlzUnRsOiBpc1J0bCxcbiAgICAgICAgb3B0aW9uczogb3B0aW9ucyxcbiAgICAgICAgc2VsZWN0T3B0aW9uOiBzZWxlY3RPcHRpb24sXG4gICAgICAgIHNlbGVjdFByb3BzOiBwcm9wcyxcbiAgICAgICAgc2V0VmFsdWU6IHNldFZhbHVlLFxuICAgICAgICB0aGVtZTogdGhpcy5nZXRUaGVtZSgpXG4gICAgICB9O1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJoYXNWYWx1ZVwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBoYXNWYWx1ZSgpIHtcbiAgICAgIHZhciBzZWxlY3RWYWx1ZSA9IHRoaXMuc3RhdGUuc2VsZWN0VmFsdWU7XG4gICAgICByZXR1cm4gc2VsZWN0VmFsdWUubGVuZ3RoID4gMDtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiaGFzT3B0aW9uc1wiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBoYXNPcHRpb25zKCkge1xuICAgICAgcmV0dXJuICEhdGhpcy5nZXRGb2N1c2FibGVPcHRpb25zKCkubGVuZ3RoO1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJpc0NsZWFyYWJsZVwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBpc0NsZWFyYWJsZSgpIHtcbiAgICAgIHZhciBfdGhpcyRwcm9wczcgPSB0aGlzLnByb3BzLFxuICAgICAgICBpc0NsZWFyYWJsZSA9IF90aGlzJHByb3BzNy5pc0NsZWFyYWJsZSxcbiAgICAgICAgaXNNdWx0aSA9IF90aGlzJHByb3BzNy5pc011bHRpO1xuXG4gICAgICAvLyBzaW5nbGUgc2VsZWN0LCBieSBkZWZhdWx0LCBJUyBOT1QgY2xlYXJhYmxlXG4gICAgICAvLyBtdWx0aSBzZWxlY3QsIGJ5IGRlZmF1bHQsIElTIGNsZWFyYWJsZVxuICAgICAgaWYgKGlzQ2xlYXJhYmxlID09PSB1bmRlZmluZWQpIHJldHVybiBpc011bHRpO1xuICAgICAgcmV0dXJuIGlzQ2xlYXJhYmxlO1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJpc09wdGlvbkRpc2FibGVkXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIGlzT3B0aW9uRGlzYWJsZWQob3B0aW9uLCBzZWxlY3RWYWx1ZSkge1xuICAgICAgcmV0dXJuIF9pc09wdGlvbkRpc2FibGVkKHRoaXMucHJvcHMsIG9wdGlvbiwgc2VsZWN0VmFsdWUpO1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJpc09wdGlvblNlbGVjdGVkXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIGlzT3B0aW9uU2VsZWN0ZWQob3B0aW9uLCBzZWxlY3RWYWx1ZSkge1xuICAgICAgcmV0dXJuIF9pc09wdGlvblNlbGVjdGVkKHRoaXMucHJvcHMsIG9wdGlvbiwgc2VsZWN0VmFsdWUpO1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJmaWx0ZXJPcHRpb25cIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gZmlsdGVyT3B0aW9uKG9wdGlvbiwgaW5wdXRWYWx1ZSkge1xuICAgICAgcmV0dXJuIF9maWx0ZXJPcHRpb24odGhpcy5wcm9wcywgb3B0aW9uLCBpbnB1dFZhbHVlKTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiZm9ybWF0T3B0aW9uTGFiZWxcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gZm9ybWF0T3B0aW9uTGFiZWwoZGF0YSwgY29udGV4dCkge1xuICAgICAgaWYgKHR5cGVvZiB0aGlzLnByb3BzLmZvcm1hdE9wdGlvbkxhYmVsID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHZhciBfaW5wdXRWYWx1ZSA9IHRoaXMucHJvcHMuaW5wdXRWYWx1ZTtcbiAgICAgICAgdmFyIF9zZWxlY3RWYWx1ZSA9IHRoaXMuc3RhdGUuc2VsZWN0VmFsdWU7XG4gICAgICAgIHJldHVybiB0aGlzLnByb3BzLmZvcm1hdE9wdGlvbkxhYmVsKGRhdGEsIHtcbiAgICAgICAgICBjb250ZXh0OiBjb250ZXh0LFxuICAgICAgICAgIGlucHV0VmFsdWU6IF9pbnB1dFZhbHVlLFxuICAgICAgICAgIHNlbGVjdFZhbHVlOiBfc2VsZWN0VmFsdWVcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gdGhpcy5nZXRPcHRpb25MYWJlbChkYXRhKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiZm9ybWF0R3JvdXBMYWJlbFwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBmb3JtYXRHcm91cExhYmVsKGRhdGEpIHtcbiAgICAgIHJldHVybiB0aGlzLnByb3BzLmZvcm1hdEdyb3VwTGFiZWwoZGF0YSk7XG4gICAgfVxuXG4gICAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gICAgLy8gTW91c2UgSGFuZGxlcnNcbiAgICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgfSwge1xuICAgIGtleTogXCJzdGFydExpc3RlbmluZ0NvbXBvc2l0aW9uXCIsXG4gICAgdmFsdWU6XG4gICAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gICAgLy8gQ29tcG9zaXRpb24gSGFuZGxlcnNcbiAgICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuICAgIGZ1bmN0aW9uIHN0YXJ0TGlzdGVuaW5nQ29tcG9zaXRpb24oKSB7XG4gICAgICBpZiAoZG9jdW1lbnQgJiYgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcikge1xuICAgICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdjb21wb3NpdGlvbnN0YXJ0JywgdGhpcy5vbkNvbXBvc2l0aW9uU3RhcnQsIGZhbHNlKTtcbiAgICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignY29tcG9zaXRpb25lbmQnLCB0aGlzLm9uQ29tcG9zaXRpb25FbmQsIGZhbHNlKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwic3RvcExpc3RlbmluZ0NvbXBvc2l0aW9uXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIHN0b3BMaXN0ZW5pbmdDb21wb3NpdGlvbigpIHtcbiAgICAgIGlmIChkb2N1bWVudCAmJiBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKSB7XG4gICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NvbXBvc2l0aW9uc3RhcnQnLCB0aGlzLm9uQ29tcG9zaXRpb25TdGFydCk7XG4gICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NvbXBvc2l0aW9uZW5kJywgdGhpcy5vbkNvbXBvc2l0aW9uRW5kKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwic3RhcnRMaXN0ZW5pbmdUb1RvdWNoXCIsXG4gICAgdmFsdWU6XG4gICAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gICAgLy8gVG91Y2ggSGFuZGxlcnNcbiAgICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuICAgIGZ1bmN0aW9uIHN0YXJ0TGlzdGVuaW5nVG9Ub3VjaCgpIHtcbiAgICAgIGlmIChkb2N1bWVudCAmJiBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKSB7XG4gICAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3RvdWNoc3RhcnQnLCB0aGlzLm9uVG91Y2hTdGFydCwgZmFsc2UpO1xuICAgICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCd0b3VjaG1vdmUnLCB0aGlzLm9uVG91Y2hNb3ZlLCBmYWxzZSk7XG4gICAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3RvdWNoZW5kJywgdGhpcy5vblRvdWNoRW5kLCBmYWxzZSk7XG4gICAgICB9XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcInN0b3BMaXN0ZW5pbmdUb1RvdWNoXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIHN0b3BMaXN0ZW5pbmdUb1RvdWNoKCkge1xuICAgICAgaWYgKGRvY3VtZW50ICYmIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIpIHtcbiAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigndG91Y2hzdGFydCcsIHRoaXMub25Ub3VjaFN0YXJ0KTtcbiAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigndG91Y2htb3ZlJywgdGhpcy5vblRvdWNoTW92ZSk7XG4gICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3RvdWNoZW5kJywgdGhpcy5vblRvdWNoRW5kKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwicmVuZGVySW5wdXRcIixcbiAgICB2YWx1ZTpcbiAgICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgICAvLyBSZW5kZXJlcnNcbiAgICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgICBmdW5jdGlvbiByZW5kZXJJbnB1dCgpIHtcbiAgICAgIHZhciBfdGhpcyRwcm9wczggPSB0aGlzLnByb3BzLFxuICAgICAgICBpc0Rpc2FibGVkID0gX3RoaXMkcHJvcHM4LmlzRGlzYWJsZWQsXG4gICAgICAgIGlzU2VhcmNoYWJsZSA9IF90aGlzJHByb3BzOC5pc1NlYXJjaGFibGUsXG4gICAgICAgIGlucHV0SWQgPSBfdGhpcyRwcm9wczguaW5wdXRJZCxcbiAgICAgICAgaW5wdXRWYWx1ZSA9IF90aGlzJHByb3BzOC5pbnB1dFZhbHVlLFxuICAgICAgICB0YWJJbmRleCA9IF90aGlzJHByb3BzOC50YWJJbmRleCxcbiAgICAgICAgZm9ybSA9IF90aGlzJHByb3BzOC5mb3JtLFxuICAgICAgICBtZW51SXNPcGVuID0gX3RoaXMkcHJvcHM4Lm1lbnVJc09wZW4sXG4gICAgICAgIHJlcXVpcmVkID0gX3RoaXMkcHJvcHM4LnJlcXVpcmVkO1xuICAgICAgdmFyIF90aGlzJGdldENvbXBvbmVudHMgPSB0aGlzLmdldENvbXBvbmVudHMoKSxcbiAgICAgICAgSW5wdXQgPSBfdGhpcyRnZXRDb21wb25lbnRzLklucHV0O1xuICAgICAgdmFyIF90aGlzJHN0YXRlNCA9IHRoaXMuc3RhdGUsXG4gICAgICAgIGlucHV0SXNIaWRkZW4gPSBfdGhpcyRzdGF0ZTQuaW5wdXRJc0hpZGRlbixcbiAgICAgICAgYXJpYVNlbGVjdGlvbiA9IF90aGlzJHN0YXRlNC5hcmlhU2VsZWN0aW9uO1xuICAgICAgdmFyIGNvbW1vblByb3BzID0gdGhpcy5jb21tb25Qcm9wcztcbiAgICAgIHZhciBpZCA9IGlucHV0SWQgfHwgdGhpcy5nZXRFbGVtZW50SWQoJ2lucHV0Jyk7XG5cbiAgICAgIC8vIGFyaWEgYXR0cmlidXRlcyBtYWtlcyB0aGUgSlNYIFwibm9pc3lcIiwgc2VwYXJhdGVkIGZvciBjbGFyaXR5XG4gICAgICB2YXIgYXJpYUF0dHJpYnV0ZXMgPSBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7XG4gICAgICAgICdhcmlhLWF1dG9jb21wbGV0ZSc6ICdsaXN0JyxcbiAgICAgICAgJ2FyaWEtZXhwYW5kZWQnOiBtZW51SXNPcGVuLFxuICAgICAgICAnYXJpYS1oYXNwb3B1cCc6IHRydWUsXG4gICAgICAgICdhcmlhLWVycm9ybWVzc2FnZSc6IHRoaXMucHJvcHNbJ2FyaWEtZXJyb3JtZXNzYWdlJ10sXG4gICAgICAgICdhcmlhLWludmFsaWQnOiB0aGlzLnByb3BzWydhcmlhLWludmFsaWQnXSxcbiAgICAgICAgJ2FyaWEtbGFiZWwnOiB0aGlzLnByb3BzWydhcmlhLWxhYmVsJ10sXG4gICAgICAgICdhcmlhLWxhYmVsbGVkYnknOiB0aGlzLnByb3BzWydhcmlhLWxhYmVsbGVkYnknXSxcbiAgICAgICAgJ2FyaWEtcmVxdWlyZWQnOiByZXF1aXJlZCxcbiAgICAgICAgcm9sZTogJ2NvbWJvYm94JyxcbiAgICAgICAgJ2FyaWEtYWN0aXZlZGVzY2VuZGFudCc6IHRoaXMuaXNBcHBsZURldmljZSA/IHVuZGVmaW5lZCA6IHRoaXMuc3RhdGUuZm9jdXNlZE9wdGlvbklkIHx8ICcnXG4gICAgICB9LCBtZW51SXNPcGVuICYmIHtcbiAgICAgICAgJ2FyaWEtY29udHJvbHMnOiB0aGlzLmdldEVsZW1lbnRJZCgnbGlzdGJveCcpXG4gICAgICB9KSwgIWlzU2VhcmNoYWJsZSAmJiB7XG4gICAgICAgICdhcmlhLXJlYWRvbmx5JzogdHJ1ZVxuICAgICAgfSksIHRoaXMuaGFzVmFsdWUoKSA/IChhcmlhU2VsZWN0aW9uID09PSBudWxsIHx8IGFyaWFTZWxlY3Rpb24gPT09IHZvaWQgMCA/IHZvaWQgMCA6IGFyaWFTZWxlY3Rpb24uYWN0aW9uKSA9PT0gJ2luaXRpYWwtaW5wdXQtZm9jdXMnICYmIHtcbiAgICAgICAgJ2FyaWEtZGVzY3JpYmVkYnknOiB0aGlzLmdldEVsZW1lbnRJZCgnbGl2ZS1yZWdpb24nKVxuICAgICAgfSA6IHtcbiAgICAgICAgJ2FyaWEtZGVzY3JpYmVkYnknOiB0aGlzLmdldEVsZW1lbnRJZCgncGxhY2Vob2xkZXInKVxuICAgICAgfSk7XG4gICAgICBpZiAoIWlzU2VhcmNoYWJsZSkge1xuICAgICAgICAvLyB1c2UgYSBkdW1teSBpbnB1dCB0byBtYWludGFpbiBmb2N1cy9ibHVyIGZ1bmN0aW9uYWxpdHlcbiAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KER1bW15SW5wdXQsIF9leHRlbmRzKHtcbiAgICAgICAgICBpZDogaWQsXG4gICAgICAgICAgaW5uZXJSZWY6IHRoaXMuZ2V0SW5wdXRSZWYsXG4gICAgICAgICAgb25CbHVyOiB0aGlzLm9uSW5wdXRCbHVyLFxuICAgICAgICAgIG9uQ2hhbmdlOiBub29wLFxuICAgICAgICAgIG9uRm9jdXM6IHRoaXMub25JbnB1dEZvY3VzLFxuICAgICAgICAgIGRpc2FibGVkOiBpc0Rpc2FibGVkLFxuICAgICAgICAgIHRhYkluZGV4OiB0YWJJbmRleCxcbiAgICAgICAgICBpbnB1dE1vZGU6IFwibm9uZVwiLFxuICAgICAgICAgIGZvcm06IGZvcm0sXG4gICAgICAgICAgdmFsdWU6IFwiXCJcbiAgICAgICAgfSwgYXJpYUF0dHJpYnV0ZXMpKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChJbnB1dCwgX2V4dGVuZHMoe30sIGNvbW1vblByb3BzLCB7XG4gICAgICAgIGF1dG9DYXBpdGFsaXplOiBcIm5vbmVcIixcbiAgICAgICAgYXV0b0NvbXBsZXRlOiBcIm9mZlwiLFxuICAgICAgICBhdXRvQ29ycmVjdDogXCJvZmZcIixcbiAgICAgICAgaWQ6IGlkLFxuICAgICAgICBpbm5lclJlZjogdGhpcy5nZXRJbnB1dFJlZixcbiAgICAgICAgaXNEaXNhYmxlZDogaXNEaXNhYmxlZCxcbiAgICAgICAgaXNIaWRkZW46IGlucHV0SXNIaWRkZW4sXG4gICAgICAgIG9uQmx1cjogdGhpcy5vbklucHV0Qmx1cixcbiAgICAgICAgb25DaGFuZ2U6IHRoaXMuaGFuZGxlSW5wdXRDaGFuZ2UsXG4gICAgICAgIG9uRm9jdXM6IHRoaXMub25JbnB1dEZvY3VzLFxuICAgICAgICBzcGVsbENoZWNrOiBcImZhbHNlXCIsXG4gICAgICAgIHRhYkluZGV4OiB0YWJJbmRleCxcbiAgICAgICAgZm9ybTogZm9ybSxcbiAgICAgICAgdHlwZTogXCJ0ZXh0XCIsXG4gICAgICAgIHZhbHVlOiBpbnB1dFZhbHVlXG4gICAgICB9LCBhcmlhQXR0cmlidXRlcykpO1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJyZW5kZXJQbGFjZWhvbGRlck9yVmFsdWVcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gcmVuZGVyUGxhY2Vob2xkZXJPclZhbHVlKCkge1xuICAgICAgdmFyIF90aGlzMyA9IHRoaXM7XG4gICAgICB2YXIgX3RoaXMkZ2V0Q29tcG9uZW50czIgPSB0aGlzLmdldENvbXBvbmVudHMoKSxcbiAgICAgICAgTXVsdGlWYWx1ZSA9IF90aGlzJGdldENvbXBvbmVudHMyLk11bHRpVmFsdWUsXG4gICAgICAgIE11bHRpVmFsdWVDb250YWluZXIgPSBfdGhpcyRnZXRDb21wb25lbnRzMi5NdWx0aVZhbHVlQ29udGFpbmVyLFxuICAgICAgICBNdWx0aVZhbHVlTGFiZWwgPSBfdGhpcyRnZXRDb21wb25lbnRzMi5NdWx0aVZhbHVlTGFiZWwsXG4gICAgICAgIE11bHRpVmFsdWVSZW1vdmUgPSBfdGhpcyRnZXRDb21wb25lbnRzMi5NdWx0aVZhbHVlUmVtb3ZlLFxuICAgICAgICBTaW5nbGVWYWx1ZSA9IF90aGlzJGdldENvbXBvbmVudHMyLlNpbmdsZVZhbHVlLFxuICAgICAgICBQbGFjZWhvbGRlciA9IF90aGlzJGdldENvbXBvbmVudHMyLlBsYWNlaG9sZGVyO1xuICAgICAgdmFyIGNvbW1vblByb3BzID0gdGhpcy5jb21tb25Qcm9wcztcbiAgICAgIHZhciBfdGhpcyRwcm9wczkgPSB0aGlzLnByb3BzLFxuICAgICAgICBjb250cm9sU2hvdWxkUmVuZGVyVmFsdWUgPSBfdGhpcyRwcm9wczkuY29udHJvbFNob3VsZFJlbmRlclZhbHVlLFxuICAgICAgICBpc0Rpc2FibGVkID0gX3RoaXMkcHJvcHM5LmlzRGlzYWJsZWQsXG4gICAgICAgIGlzTXVsdGkgPSBfdGhpcyRwcm9wczkuaXNNdWx0aSxcbiAgICAgICAgaW5wdXRWYWx1ZSA9IF90aGlzJHByb3BzOS5pbnB1dFZhbHVlLFxuICAgICAgICBwbGFjZWhvbGRlciA9IF90aGlzJHByb3BzOS5wbGFjZWhvbGRlcjtcbiAgICAgIHZhciBfdGhpcyRzdGF0ZTUgPSB0aGlzLnN0YXRlLFxuICAgICAgICBzZWxlY3RWYWx1ZSA9IF90aGlzJHN0YXRlNS5zZWxlY3RWYWx1ZSxcbiAgICAgICAgZm9jdXNlZFZhbHVlID0gX3RoaXMkc3RhdGU1LmZvY3VzZWRWYWx1ZSxcbiAgICAgICAgaXNGb2N1c2VkID0gX3RoaXMkc3RhdGU1LmlzRm9jdXNlZDtcbiAgICAgIGlmICghdGhpcy5oYXNWYWx1ZSgpIHx8ICFjb250cm9sU2hvdWxkUmVuZGVyVmFsdWUpIHtcbiAgICAgICAgcmV0dXJuIGlucHV0VmFsdWUgPyBudWxsIDogLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUGxhY2Vob2xkZXIsIF9leHRlbmRzKHt9LCBjb21tb25Qcm9wcywge1xuICAgICAgICAgIGtleTogXCJwbGFjZWhvbGRlclwiLFxuICAgICAgICAgIGlzRGlzYWJsZWQ6IGlzRGlzYWJsZWQsXG4gICAgICAgICAgaXNGb2N1c2VkOiBpc0ZvY3VzZWQsXG4gICAgICAgICAgaW5uZXJQcm9wczoge1xuICAgICAgICAgICAgaWQ6IHRoaXMuZ2V0RWxlbWVudElkKCdwbGFjZWhvbGRlcicpXG4gICAgICAgICAgfVxuICAgICAgICB9KSwgcGxhY2Vob2xkZXIpO1xuICAgICAgfVxuICAgICAgaWYgKGlzTXVsdGkpIHtcbiAgICAgICAgcmV0dXJuIHNlbGVjdFZhbHVlLm1hcChmdW5jdGlvbiAob3B0LCBpbmRleCkge1xuICAgICAgICAgIHZhciBpc09wdGlvbkZvY3VzZWQgPSBvcHQgPT09IGZvY3VzZWRWYWx1ZTtcbiAgICAgICAgICB2YXIga2V5ID0gXCJcIi5jb25jYXQoX3RoaXMzLmdldE9wdGlvbkxhYmVsKG9wdCksIFwiLVwiKS5jb25jYXQoX3RoaXMzLmdldE9wdGlvblZhbHVlKG9wdCkpO1xuICAgICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChNdWx0aVZhbHVlLCBfZXh0ZW5kcyh7fSwgY29tbW9uUHJvcHMsIHtcbiAgICAgICAgICAgIGNvbXBvbmVudHM6IHtcbiAgICAgICAgICAgICAgQ29udGFpbmVyOiBNdWx0aVZhbHVlQ29udGFpbmVyLFxuICAgICAgICAgICAgICBMYWJlbDogTXVsdGlWYWx1ZUxhYmVsLFxuICAgICAgICAgICAgICBSZW1vdmU6IE11bHRpVmFsdWVSZW1vdmVcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBpc0ZvY3VzZWQ6IGlzT3B0aW9uRm9jdXNlZCxcbiAgICAgICAgICAgIGlzRGlzYWJsZWQ6IGlzRGlzYWJsZWQsXG4gICAgICAgICAgICBrZXk6IGtleSxcbiAgICAgICAgICAgIGluZGV4OiBpbmRleCxcbiAgICAgICAgICAgIHJlbW92ZVByb3BzOiB7XG4gICAgICAgICAgICAgIG9uQ2xpY2s6IGZ1bmN0aW9uIG9uQ2xpY2soKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMy5yZW1vdmVWYWx1ZShvcHQpO1xuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICBvblRvdWNoRW5kOiBmdW5jdGlvbiBvblRvdWNoRW5kKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfdGhpczMucmVtb3ZlVmFsdWUob3B0KTtcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgb25Nb3VzZURvd246IGZ1bmN0aW9uIG9uTW91c2VEb3duKGUpIHtcbiAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBkYXRhOiBvcHRcbiAgICAgICAgICB9KSwgX3RoaXMzLmZvcm1hdE9wdGlvbkxhYmVsKG9wdCwgJ3ZhbHVlJykpO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIGlmIChpbnB1dFZhbHVlKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfVxuICAgICAgdmFyIHNpbmdsZVZhbHVlID0gc2VsZWN0VmFsdWVbMF07XG4gICAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoU2luZ2xlVmFsdWUsIF9leHRlbmRzKHt9LCBjb21tb25Qcm9wcywge1xuICAgICAgICBkYXRhOiBzaW5nbGVWYWx1ZSxcbiAgICAgICAgaXNEaXNhYmxlZDogaXNEaXNhYmxlZFxuICAgICAgfSksIHRoaXMuZm9ybWF0T3B0aW9uTGFiZWwoc2luZ2xlVmFsdWUsICd2YWx1ZScpKTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwicmVuZGVyQ2xlYXJJbmRpY2F0b3JcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gcmVuZGVyQ2xlYXJJbmRpY2F0b3IoKSB7XG4gICAgICB2YXIgX3RoaXMkZ2V0Q29tcG9uZW50czMgPSB0aGlzLmdldENvbXBvbmVudHMoKSxcbiAgICAgICAgQ2xlYXJJbmRpY2F0b3IgPSBfdGhpcyRnZXRDb21wb25lbnRzMy5DbGVhckluZGljYXRvcjtcbiAgICAgIHZhciBjb21tb25Qcm9wcyA9IHRoaXMuY29tbW9uUHJvcHM7XG4gICAgICB2YXIgX3RoaXMkcHJvcHMxMCA9IHRoaXMucHJvcHMsXG4gICAgICAgIGlzRGlzYWJsZWQgPSBfdGhpcyRwcm9wczEwLmlzRGlzYWJsZWQsXG4gICAgICAgIGlzTG9hZGluZyA9IF90aGlzJHByb3BzMTAuaXNMb2FkaW5nO1xuICAgICAgdmFyIGlzRm9jdXNlZCA9IHRoaXMuc3RhdGUuaXNGb2N1c2VkO1xuICAgICAgaWYgKCF0aGlzLmlzQ2xlYXJhYmxlKCkgfHwgIUNsZWFySW5kaWNhdG9yIHx8IGlzRGlzYWJsZWQgfHwgIXRoaXMuaGFzVmFsdWUoKSB8fCBpc0xvYWRpbmcpIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG4gICAgICB2YXIgaW5uZXJQcm9wcyA9IHtcbiAgICAgICAgb25Nb3VzZURvd246IHRoaXMub25DbGVhckluZGljYXRvck1vdXNlRG93bixcbiAgICAgICAgb25Ub3VjaEVuZDogdGhpcy5vbkNsZWFySW5kaWNhdG9yVG91Y2hFbmQsXG4gICAgICAgICdhcmlhLWhpZGRlbic6ICd0cnVlJ1xuICAgICAgfTtcbiAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChDbGVhckluZGljYXRvciwgX2V4dGVuZHMoe30sIGNvbW1vblByb3BzLCB7XG4gICAgICAgIGlubmVyUHJvcHM6IGlubmVyUHJvcHMsXG4gICAgICAgIGlzRm9jdXNlZDogaXNGb2N1c2VkXG4gICAgICB9KSk7XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcInJlbmRlckxvYWRpbmdJbmRpY2F0b3JcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gcmVuZGVyTG9hZGluZ0luZGljYXRvcigpIHtcbiAgICAgIHZhciBfdGhpcyRnZXRDb21wb25lbnRzNCA9IHRoaXMuZ2V0Q29tcG9uZW50cygpLFxuICAgICAgICBMb2FkaW5nSW5kaWNhdG9yID0gX3RoaXMkZ2V0Q29tcG9uZW50czQuTG9hZGluZ0luZGljYXRvcjtcbiAgICAgIHZhciBjb21tb25Qcm9wcyA9IHRoaXMuY29tbW9uUHJvcHM7XG4gICAgICB2YXIgX3RoaXMkcHJvcHMxMSA9IHRoaXMucHJvcHMsXG4gICAgICAgIGlzRGlzYWJsZWQgPSBfdGhpcyRwcm9wczExLmlzRGlzYWJsZWQsXG4gICAgICAgIGlzTG9hZGluZyA9IF90aGlzJHByb3BzMTEuaXNMb2FkaW5nO1xuICAgICAgdmFyIGlzRm9jdXNlZCA9IHRoaXMuc3RhdGUuaXNGb2N1c2VkO1xuICAgICAgaWYgKCFMb2FkaW5nSW5kaWNhdG9yIHx8ICFpc0xvYWRpbmcpIHJldHVybiBudWxsO1xuICAgICAgdmFyIGlubmVyUHJvcHMgPSB7XG4gICAgICAgICdhcmlhLWhpZGRlbic6ICd0cnVlJ1xuICAgICAgfTtcbiAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChMb2FkaW5nSW5kaWNhdG9yLCBfZXh0ZW5kcyh7fSwgY29tbW9uUHJvcHMsIHtcbiAgICAgICAgaW5uZXJQcm9wczogaW5uZXJQcm9wcyxcbiAgICAgICAgaXNEaXNhYmxlZDogaXNEaXNhYmxlZCxcbiAgICAgICAgaXNGb2N1c2VkOiBpc0ZvY3VzZWRcbiAgICAgIH0pKTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwicmVuZGVySW5kaWNhdG9yU2VwYXJhdG9yXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIHJlbmRlckluZGljYXRvclNlcGFyYXRvcigpIHtcbiAgICAgIHZhciBfdGhpcyRnZXRDb21wb25lbnRzNSA9IHRoaXMuZ2V0Q29tcG9uZW50cygpLFxuICAgICAgICBEcm9wZG93bkluZGljYXRvciA9IF90aGlzJGdldENvbXBvbmVudHM1LkRyb3Bkb3duSW5kaWNhdG9yLFxuICAgICAgICBJbmRpY2F0b3JTZXBhcmF0b3IgPSBfdGhpcyRnZXRDb21wb25lbnRzNS5JbmRpY2F0b3JTZXBhcmF0b3I7XG5cbiAgICAgIC8vIHNlcGFyYXRvciBkb2Vzbid0IG1ha2Ugc2Vuc2Ugd2l0aG91dCB0aGUgZHJvcGRvd24gaW5kaWNhdG9yXG4gICAgICBpZiAoIURyb3Bkb3duSW5kaWNhdG9yIHx8ICFJbmRpY2F0b3JTZXBhcmF0b3IpIHJldHVybiBudWxsO1xuICAgICAgdmFyIGNvbW1vblByb3BzID0gdGhpcy5jb21tb25Qcm9wcztcbiAgICAgIHZhciBpc0Rpc2FibGVkID0gdGhpcy5wcm9wcy5pc0Rpc2FibGVkO1xuICAgICAgdmFyIGlzRm9jdXNlZCA9IHRoaXMuc3RhdGUuaXNGb2N1c2VkO1xuICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEluZGljYXRvclNlcGFyYXRvciwgX2V4dGVuZHMoe30sIGNvbW1vblByb3BzLCB7XG4gICAgICAgIGlzRGlzYWJsZWQ6IGlzRGlzYWJsZWQsXG4gICAgICAgIGlzRm9jdXNlZDogaXNGb2N1c2VkXG4gICAgICB9KSk7XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcInJlbmRlckRyb3Bkb3duSW5kaWNhdG9yXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIHJlbmRlckRyb3Bkb3duSW5kaWNhdG9yKCkge1xuICAgICAgdmFyIF90aGlzJGdldENvbXBvbmVudHM2ID0gdGhpcy5nZXRDb21wb25lbnRzKCksXG4gICAgICAgIERyb3Bkb3duSW5kaWNhdG9yID0gX3RoaXMkZ2V0Q29tcG9uZW50czYuRHJvcGRvd25JbmRpY2F0b3I7XG4gICAgICBpZiAoIURyb3Bkb3duSW5kaWNhdG9yKSByZXR1cm4gbnVsbDtcbiAgICAgIHZhciBjb21tb25Qcm9wcyA9IHRoaXMuY29tbW9uUHJvcHM7XG4gICAgICB2YXIgaXNEaXNhYmxlZCA9IHRoaXMucHJvcHMuaXNEaXNhYmxlZDtcbiAgICAgIHZhciBpc0ZvY3VzZWQgPSB0aGlzLnN0YXRlLmlzRm9jdXNlZDtcbiAgICAgIHZhciBpbm5lclByb3BzID0ge1xuICAgICAgICBvbk1vdXNlRG93bjogdGhpcy5vbkRyb3Bkb3duSW5kaWNhdG9yTW91c2VEb3duLFxuICAgICAgICBvblRvdWNoRW5kOiB0aGlzLm9uRHJvcGRvd25JbmRpY2F0b3JUb3VjaEVuZCxcbiAgICAgICAgJ2FyaWEtaGlkZGVuJzogJ3RydWUnXG4gICAgICB9O1xuICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KERyb3Bkb3duSW5kaWNhdG9yLCBfZXh0ZW5kcyh7fSwgY29tbW9uUHJvcHMsIHtcbiAgICAgICAgaW5uZXJQcm9wczogaW5uZXJQcm9wcyxcbiAgICAgICAgaXNEaXNhYmxlZDogaXNEaXNhYmxlZCxcbiAgICAgICAgaXNGb2N1c2VkOiBpc0ZvY3VzZWRcbiAgICAgIH0pKTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwicmVuZGVyTWVudVwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiByZW5kZXJNZW51KCkge1xuICAgICAgdmFyIF90aGlzNCA9IHRoaXM7XG4gICAgICB2YXIgX3RoaXMkZ2V0Q29tcG9uZW50czcgPSB0aGlzLmdldENvbXBvbmVudHMoKSxcbiAgICAgICAgR3JvdXAgPSBfdGhpcyRnZXRDb21wb25lbnRzNy5Hcm91cCxcbiAgICAgICAgR3JvdXBIZWFkaW5nID0gX3RoaXMkZ2V0Q29tcG9uZW50czcuR3JvdXBIZWFkaW5nLFxuICAgICAgICBNZW51ID0gX3RoaXMkZ2V0Q29tcG9uZW50czcuTWVudSxcbiAgICAgICAgTWVudUxpc3QgPSBfdGhpcyRnZXRDb21wb25lbnRzNy5NZW51TGlzdCxcbiAgICAgICAgTWVudVBvcnRhbCA9IF90aGlzJGdldENvbXBvbmVudHM3Lk1lbnVQb3J0YWwsXG4gICAgICAgIExvYWRpbmdNZXNzYWdlID0gX3RoaXMkZ2V0Q29tcG9uZW50czcuTG9hZGluZ01lc3NhZ2UsXG4gICAgICAgIE5vT3B0aW9uc01lc3NhZ2UgPSBfdGhpcyRnZXRDb21wb25lbnRzNy5Ob09wdGlvbnNNZXNzYWdlLFxuICAgICAgICBPcHRpb24gPSBfdGhpcyRnZXRDb21wb25lbnRzNy5PcHRpb247XG4gICAgICB2YXIgY29tbW9uUHJvcHMgPSB0aGlzLmNvbW1vblByb3BzO1xuICAgICAgdmFyIGZvY3VzZWRPcHRpb24gPSB0aGlzLnN0YXRlLmZvY3VzZWRPcHRpb247XG4gICAgICB2YXIgX3RoaXMkcHJvcHMxMiA9IHRoaXMucHJvcHMsXG4gICAgICAgIGNhcHR1cmVNZW51U2Nyb2xsID0gX3RoaXMkcHJvcHMxMi5jYXB0dXJlTWVudVNjcm9sbCxcbiAgICAgICAgaW5wdXRWYWx1ZSA9IF90aGlzJHByb3BzMTIuaW5wdXRWYWx1ZSxcbiAgICAgICAgaXNMb2FkaW5nID0gX3RoaXMkcHJvcHMxMi5pc0xvYWRpbmcsXG4gICAgICAgIGxvYWRpbmdNZXNzYWdlID0gX3RoaXMkcHJvcHMxMi5sb2FkaW5nTWVzc2FnZSxcbiAgICAgICAgbWluTWVudUhlaWdodCA9IF90aGlzJHByb3BzMTIubWluTWVudUhlaWdodCxcbiAgICAgICAgbWF4TWVudUhlaWdodCA9IF90aGlzJHByb3BzMTIubWF4TWVudUhlaWdodCxcbiAgICAgICAgbWVudUlzT3BlbiA9IF90aGlzJHByb3BzMTIubWVudUlzT3BlbixcbiAgICAgICAgbWVudVBsYWNlbWVudCA9IF90aGlzJHByb3BzMTIubWVudVBsYWNlbWVudCxcbiAgICAgICAgbWVudVBvc2l0aW9uID0gX3RoaXMkcHJvcHMxMi5tZW51UG9zaXRpb24sXG4gICAgICAgIG1lbnVQb3J0YWxUYXJnZXQgPSBfdGhpcyRwcm9wczEyLm1lbnVQb3J0YWxUYXJnZXQsXG4gICAgICAgIG1lbnVTaG91bGRCbG9ja1Njcm9sbCA9IF90aGlzJHByb3BzMTIubWVudVNob3VsZEJsb2NrU2Nyb2xsLFxuICAgICAgICBtZW51U2hvdWxkU2Nyb2xsSW50b1ZpZXcgPSBfdGhpcyRwcm9wczEyLm1lbnVTaG91bGRTY3JvbGxJbnRvVmlldyxcbiAgICAgICAgbm9PcHRpb25zTWVzc2FnZSA9IF90aGlzJHByb3BzMTIubm9PcHRpb25zTWVzc2FnZSxcbiAgICAgICAgb25NZW51U2Nyb2xsVG9Ub3AgPSBfdGhpcyRwcm9wczEyLm9uTWVudVNjcm9sbFRvVG9wLFxuICAgICAgICBvbk1lbnVTY3JvbGxUb0JvdHRvbSA9IF90aGlzJHByb3BzMTIub25NZW51U2Nyb2xsVG9Cb3R0b207XG4gICAgICBpZiAoIW1lbnVJc09wZW4pIHJldHVybiBudWxsO1xuXG4gICAgICAvLyBUT0RPOiBJbnRlcm5hbCBPcHRpb24gVHlwZSBoZXJlXG4gICAgICB2YXIgcmVuZGVyID0gZnVuY3Rpb24gcmVuZGVyKHByb3BzLCBpZCkge1xuICAgICAgICB2YXIgdHlwZSA9IHByb3BzLnR5cGUsXG4gICAgICAgICAgZGF0YSA9IHByb3BzLmRhdGEsXG4gICAgICAgICAgaXNEaXNhYmxlZCA9IHByb3BzLmlzRGlzYWJsZWQsXG4gICAgICAgICAgaXNTZWxlY3RlZCA9IHByb3BzLmlzU2VsZWN0ZWQsXG4gICAgICAgICAgbGFiZWwgPSBwcm9wcy5sYWJlbCxcbiAgICAgICAgICB2YWx1ZSA9IHByb3BzLnZhbHVlO1xuICAgICAgICB2YXIgaXNGb2N1c2VkID0gZm9jdXNlZE9wdGlvbiA9PT0gZGF0YTtcbiAgICAgICAgdmFyIG9uSG92ZXIgPSBpc0Rpc2FibGVkID8gdW5kZWZpbmVkIDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgIHJldHVybiBfdGhpczQub25PcHRpb25Ib3ZlcihkYXRhKTtcbiAgICAgICAgfTtcbiAgICAgICAgdmFyIG9uU2VsZWN0ID0gaXNEaXNhYmxlZCA/IHVuZGVmaW5lZCA6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICByZXR1cm4gX3RoaXM0LnNlbGVjdE9wdGlvbihkYXRhKTtcbiAgICAgICAgfTtcbiAgICAgICAgdmFyIG9wdGlvbklkID0gXCJcIi5jb25jYXQoX3RoaXM0LmdldEVsZW1lbnRJZCgnb3B0aW9uJyksIFwiLVwiKS5jb25jYXQoaWQpO1xuICAgICAgICB2YXIgaW5uZXJQcm9wcyA9IHtcbiAgICAgICAgICBpZDogb3B0aW9uSWQsXG4gICAgICAgICAgb25DbGljazogb25TZWxlY3QsXG4gICAgICAgICAgb25Nb3VzZU1vdmU6IG9uSG92ZXIsXG4gICAgICAgICAgb25Nb3VzZU92ZXI6IG9uSG92ZXIsXG4gICAgICAgICAgdGFiSW5kZXg6IC0xLFxuICAgICAgICAgIHJvbGU6ICdvcHRpb24nLFxuICAgICAgICAgICdhcmlhLXNlbGVjdGVkJzogX3RoaXM0LmlzQXBwbGVEZXZpY2UgPyB1bmRlZmluZWQgOiBpc1NlbGVjdGVkIC8vIGlzIG5vdCBzdXBwb3J0ZWQgb24gQXBwbGUgZGV2aWNlc1xuICAgICAgICB9O1xuXG4gICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChPcHRpb24sIF9leHRlbmRzKHt9LCBjb21tb25Qcm9wcywge1xuICAgICAgICAgIGlubmVyUHJvcHM6IGlubmVyUHJvcHMsXG4gICAgICAgICAgZGF0YTogZGF0YSxcbiAgICAgICAgICBpc0Rpc2FibGVkOiBpc0Rpc2FibGVkLFxuICAgICAgICAgIGlzU2VsZWN0ZWQ6IGlzU2VsZWN0ZWQsXG4gICAgICAgICAga2V5OiBvcHRpb25JZCxcbiAgICAgICAgICBsYWJlbDogbGFiZWwsXG4gICAgICAgICAgdHlwZTogdHlwZSxcbiAgICAgICAgICB2YWx1ZTogdmFsdWUsXG4gICAgICAgICAgaXNGb2N1c2VkOiBpc0ZvY3VzZWQsXG4gICAgICAgICAgaW5uZXJSZWY6IGlzRm9jdXNlZCA/IF90aGlzNC5nZXRGb2N1c2VkT3B0aW9uUmVmIDogdW5kZWZpbmVkXG4gICAgICAgIH0pLCBfdGhpczQuZm9ybWF0T3B0aW9uTGFiZWwocHJvcHMuZGF0YSwgJ21lbnUnKSk7XG4gICAgICB9O1xuICAgICAgdmFyIG1lbnVVSTtcbiAgICAgIGlmICh0aGlzLmhhc09wdGlvbnMoKSkge1xuICAgICAgICBtZW51VUkgPSB0aGlzLmdldENhdGVnb3JpemVkT3B0aW9ucygpLm1hcChmdW5jdGlvbiAoaXRlbSkge1xuICAgICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICdncm91cCcpIHtcbiAgICAgICAgICAgIHZhciBfZGF0YSA9IGl0ZW0uZGF0YSxcbiAgICAgICAgICAgICAgb3B0aW9ucyA9IGl0ZW0ub3B0aW9ucyxcbiAgICAgICAgICAgICAgZ3JvdXBJbmRleCA9IGl0ZW0uaW5kZXg7XG4gICAgICAgICAgICB2YXIgZ3JvdXBJZCA9IFwiXCIuY29uY2F0KF90aGlzNC5nZXRFbGVtZW50SWQoJ2dyb3VwJyksIFwiLVwiKS5jb25jYXQoZ3JvdXBJbmRleCk7XG4gICAgICAgICAgICB2YXIgaGVhZGluZ0lkID0gXCJcIi5jb25jYXQoZ3JvdXBJZCwgXCItaGVhZGluZ1wiKTtcbiAgICAgICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChHcm91cCwgX2V4dGVuZHMoe30sIGNvbW1vblByb3BzLCB7XG4gICAgICAgICAgICAgIGtleTogZ3JvdXBJZCxcbiAgICAgICAgICAgICAgZGF0YTogX2RhdGEsXG4gICAgICAgICAgICAgIG9wdGlvbnM6IG9wdGlvbnMsXG4gICAgICAgICAgICAgIEhlYWRpbmc6IEdyb3VwSGVhZGluZyxcbiAgICAgICAgICAgICAgaGVhZGluZ1Byb3BzOiB7XG4gICAgICAgICAgICAgICAgaWQ6IGhlYWRpbmdJZCxcbiAgICAgICAgICAgICAgICBkYXRhOiBpdGVtLmRhdGFcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgbGFiZWw6IF90aGlzNC5mb3JtYXRHcm91cExhYmVsKGl0ZW0uZGF0YSlcbiAgICAgICAgICAgIH0pLCBpdGVtLm9wdGlvbnMubWFwKGZ1bmN0aW9uIChvcHRpb24pIHtcbiAgICAgICAgICAgICAgcmV0dXJuIHJlbmRlcihvcHRpb24sIFwiXCIuY29uY2F0KGdyb3VwSW5kZXgsIFwiLVwiKS5jb25jYXQob3B0aW9uLmluZGV4KSk7XG4gICAgICAgICAgICB9KSk7XG4gICAgICAgICAgfSBlbHNlIGlmIChpdGVtLnR5cGUgPT09ICdvcHRpb24nKSB7XG4gICAgICAgICAgICByZXR1cm4gcmVuZGVyKGl0ZW0sIFwiXCIuY29uY2F0KGl0ZW0uaW5kZXgpKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIGlmIChpc0xvYWRpbmcpIHtcbiAgICAgICAgdmFyIG1lc3NhZ2UgPSBsb2FkaW5nTWVzc2FnZSh7XG4gICAgICAgICAgaW5wdXRWYWx1ZTogaW5wdXRWYWx1ZVxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKG1lc3NhZ2UgPT09IG51bGwpIHJldHVybiBudWxsO1xuICAgICAgICBtZW51VUkgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChMb2FkaW5nTWVzc2FnZSwgY29tbW9uUHJvcHMsIG1lc3NhZ2UpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdmFyIF9tZXNzYWdlID0gbm9PcHRpb25zTWVzc2FnZSh7XG4gICAgICAgICAgaW5wdXRWYWx1ZTogaW5wdXRWYWx1ZVxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKF9tZXNzYWdlID09PSBudWxsKSByZXR1cm4gbnVsbDtcbiAgICAgICAgbWVudVVJID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoTm9PcHRpb25zTWVzc2FnZSwgY29tbW9uUHJvcHMsIF9tZXNzYWdlKTtcbiAgICAgIH1cbiAgICAgIHZhciBtZW51UGxhY2VtZW50UHJvcHMgPSB7XG4gICAgICAgIG1pbk1lbnVIZWlnaHQ6IG1pbk1lbnVIZWlnaHQsXG4gICAgICAgIG1heE1lbnVIZWlnaHQ6IG1heE1lbnVIZWlnaHQsXG4gICAgICAgIG1lbnVQbGFjZW1lbnQ6IG1lbnVQbGFjZW1lbnQsXG4gICAgICAgIG1lbnVQb3NpdGlvbjogbWVudVBvc2l0aW9uLFxuICAgICAgICBtZW51U2hvdWxkU2Nyb2xsSW50b1ZpZXc6IG1lbnVTaG91bGRTY3JvbGxJbnRvVmlld1xuICAgICAgfTtcbiAgICAgIHZhciBtZW51RWxlbWVudCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE1lbnVQbGFjZXIsIF9leHRlbmRzKHt9LCBjb21tb25Qcm9wcywgbWVudVBsYWNlbWVudFByb3BzKSwgZnVuY3Rpb24gKF9yZWY0KSB7XG4gICAgICAgIHZhciByZWYgPSBfcmVmNC5yZWYsXG4gICAgICAgICAgX3JlZjQkcGxhY2VyUHJvcHMgPSBfcmVmNC5wbGFjZXJQcm9wcyxcbiAgICAgICAgICBwbGFjZW1lbnQgPSBfcmVmNCRwbGFjZXJQcm9wcy5wbGFjZW1lbnQsXG4gICAgICAgICAgbWF4SGVpZ2h0ID0gX3JlZjQkcGxhY2VyUHJvcHMubWF4SGVpZ2h0O1xuICAgICAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoTWVudSwgX2V4dGVuZHMoe30sIGNvbW1vblByb3BzLCBtZW51UGxhY2VtZW50UHJvcHMsIHtcbiAgICAgICAgICBpbm5lclJlZjogcmVmLFxuICAgICAgICAgIGlubmVyUHJvcHM6IHtcbiAgICAgICAgICAgIG9uTW91c2VEb3duOiBfdGhpczQub25NZW51TW91c2VEb3duLFxuICAgICAgICAgICAgb25Nb3VzZU1vdmU6IF90aGlzNC5vbk1lbnVNb3VzZU1vdmVcbiAgICAgICAgICB9LFxuICAgICAgICAgIGlzTG9hZGluZzogaXNMb2FkaW5nLFxuICAgICAgICAgIHBsYWNlbWVudDogcGxhY2VtZW50XG4gICAgICAgIH0pLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChTY3JvbGxNYW5hZ2VyLCB7XG4gICAgICAgICAgY2FwdHVyZUVuYWJsZWQ6IGNhcHR1cmVNZW51U2Nyb2xsLFxuICAgICAgICAgIG9uVG9wQXJyaXZlOiBvbk1lbnVTY3JvbGxUb1RvcCxcbiAgICAgICAgICBvbkJvdHRvbUFycml2ZTogb25NZW51U2Nyb2xsVG9Cb3R0b20sXG4gICAgICAgICAgbG9ja0VuYWJsZWQ6IG1lbnVTaG91bGRCbG9ja1Njcm9sbFxuICAgICAgICB9LCBmdW5jdGlvbiAoc2Nyb2xsVGFyZ2V0UmVmKSB7XG4gICAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE1lbnVMaXN0LCBfZXh0ZW5kcyh7fSwgY29tbW9uUHJvcHMsIHtcbiAgICAgICAgICAgIGlubmVyUmVmOiBmdW5jdGlvbiBpbm5lclJlZihpbnN0YW5jZSkge1xuICAgICAgICAgICAgICBfdGhpczQuZ2V0TWVudUxpc3RSZWYoaW5zdGFuY2UpO1xuICAgICAgICAgICAgICBzY3JvbGxUYXJnZXRSZWYoaW5zdGFuY2UpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGlubmVyUHJvcHM6IHtcbiAgICAgICAgICAgICAgcm9sZTogJ2xpc3Rib3gnLFxuICAgICAgICAgICAgICAnYXJpYS1tdWx0aXNlbGVjdGFibGUnOiBjb21tb25Qcm9wcy5pc011bHRpLFxuICAgICAgICAgICAgICBpZDogX3RoaXM0LmdldEVsZW1lbnRJZCgnbGlzdGJveCcpXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgaXNMb2FkaW5nOiBpc0xvYWRpbmcsXG4gICAgICAgICAgICBtYXhIZWlnaHQ6IG1heEhlaWdodCxcbiAgICAgICAgICAgIGZvY3VzZWRPcHRpb246IGZvY3VzZWRPcHRpb25cbiAgICAgICAgICB9KSwgbWVudVVJKTtcbiAgICAgICAgfSkpO1xuICAgICAgfSk7XG5cbiAgICAgIC8vIHBvc2l0aW9uaW5nIGJlaGF2aW91ciBpcyBhbG1vc3QgaWRlbnRpY2FsIGZvciBwb3J0YWxsZWQgYW5kIGZpeGVkLFxuICAgICAgLy8gc28gd2UgdXNlIHRoZSBzYW1lIGNvbXBvbmVudC4gdGhlIGFjdHVhbCBwb3J0YWxsaW5nIGxvZ2ljIGlzIGZvcmtlZFxuICAgICAgLy8gd2l0aGluIHRoZSBjb21wb25lbnQgYmFzZWQgb24gYG1lbnVQb3NpdGlvbmBcbiAgICAgIHJldHVybiBtZW51UG9ydGFsVGFyZ2V0IHx8IG1lbnVQb3NpdGlvbiA9PT0gJ2ZpeGVkJyA/IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE1lbnVQb3J0YWwsIF9leHRlbmRzKHt9LCBjb21tb25Qcm9wcywge1xuICAgICAgICBhcHBlbmRUbzogbWVudVBvcnRhbFRhcmdldCxcbiAgICAgICAgY29udHJvbEVsZW1lbnQ6IHRoaXMuY29udHJvbFJlZixcbiAgICAgICAgbWVudVBsYWNlbWVudDogbWVudVBsYWNlbWVudCxcbiAgICAgICAgbWVudVBvc2l0aW9uOiBtZW51UG9zaXRpb25cbiAgICAgIH0pLCBtZW51RWxlbWVudCkgOiBtZW51RWxlbWVudDtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwicmVuZGVyRm9ybUZpZWxkXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIHJlbmRlckZvcm1GaWVsZCgpIHtcbiAgICAgIHZhciBfdGhpczUgPSB0aGlzO1xuICAgICAgdmFyIF90aGlzJHByb3BzMTMgPSB0aGlzLnByb3BzLFxuICAgICAgICBkZWxpbWl0ZXIgPSBfdGhpcyRwcm9wczEzLmRlbGltaXRlcixcbiAgICAgICAgaXNEaXNhYmxlZCA9IF90aGlzJHByb3BzMTMuaXNEaXNhYmxlZCxcbiAgICAgICAgaXNNdWx0aSA9IF90aGlzJHByb3BzMTMuaXNNdWx0aSxcbiAgICAgICAgbmFtZSA9IF90aGlzJHByb3BzMTMubmFtZSxcbiAgICAgICAgcmVxdWlyZWQgPSBfdGhpcyRwcm9wczEzLnJlcXVpcmVkO1xuICAgICAgdmFyIHNlbGVjdFZhbHVlID0gdGhpcy5zdGF0ZS5zZWxlY3RWYWx1ZTtcbiAgICAgIGlmIChyZXF1aXJlZCAmJiAhdGhpcy5oYXNWYWx1ZSgpICYmICFpc0Rpc2FibGVkKSB7XG4gICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChSZXF1aXJlZElucHV0JDEsIHtcbiAgICAgICAgICBuYW1lOiBuYW1lLFxuICAgICAgICAgIG9uRm9jdXM6IHRoaXMub25WYWx1ZUlucHV0Rm9jdXNcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICBpZiAoIW5hbWUgfHwgaXNEaXNhYmxlZCkgcmV0dXJuO1xuICAgICAgaWYgKGlzTXVsdGkpIHtcbiAgICAgICAgaWYgKGRlbGltaXRlcikge1xuICAgICAgICAgIHZhciB2YWx1ZSA9IHNlbGVjdFZhbHVlLm1hcChmdW5jdGlvbiAob3B0KSB7XG4gICAgICAgICAgICByZXR1cm4gX3RoaXM1LmdldE9wdGlvblZhbHVlKG9wdCk7XG4gICAgICAgICAgfSkuam9pbihkZWxpbWl0ZXIpO1xuICAgICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImlucHV0XCIsIHtcbiAgICAgICAgICAgIG5hbWU6IG5hbWUsXG4gICAgICAgICAgICB0eXBlOiBcImhpZGRlblwiLFxuICAgICAgICAgICAgdmFsdWU6IHZhbHVlXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdmFyIGlucHV0ID0gc2VsZWN0VmFsdWUubGVuZ3RoID4gMCA/IHNlbGVjdFZhbHVlLm1hcChmdW5jdGlvbiAob3B0LCBpKSB7XG4gICAgICAgICAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJpbnB1dFwiLCB7XG4gICAgICAgICAgICAgIGtleTogXCJpLVwiLmNvbmNhdChpKSxcbiAgICAgICAgICAgICAgbmFtZTogbmFtZSxcbiAgICAgICAgICAgICAgdHlwZTogXCJoaWRkZW5cIixcbiAgICAgICAgICAgICAgdmFsdWU6IF90aGlzNS5nZXRPcHRpb25WYWx1ZShvcHQpXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9KSA6IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiaW5wdXRcIiwge1xuICAgICAgICAgICAgbmFtZTogbmFtZSxcbiAgICAgICAgICAgIHR5cGU6IFwiaGlkZGVuXCIsXG4gICAgICAgICAgICB2YWx1ZTogXCJcIlxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCBudWxsLCBpbnB1dCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHZhciBfdmFsdWUgPSBzZWxlY3RWYWx1ZVswXSA/IHRoaXMuZ2V0T3B0aW9uVmFsdWUoc2VsZWN0VmFsdWVbMF0pIDogJyc7XG4gICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImlucHV0XCIsIHtcbiAgICAgICAgICBuYW1lOiBuYW1lLFxuICAgICAgICAgIHR5cGU6IFwiaGlkZGVuXCIsXG4gICAgICAgICAgdmFsdWU6IF92YWx1ZVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwicmVuZGVyTGl2ZVJlZ2lvblwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiByZW5kZXJMaXZlUmVnaW9uKCkge1xuICAgICAgdmFyIGNvbW1vblByb3BzID0gdGhpcy5jb21tb25Qcm9wcztcbiAgICAgIHZhciBfdGhpcyRzdGF0ZTYgPSB0aGlzLnN0YXRlLFxuICAgICAgICBhcmlhU2VsZWN0aW9uID0gX3RoaXMkc3RhdGU2LmFyaWFTZWxlY3Rpb24sXG4gICAgICAgIGZvY3VzZWRPcHRpb24gPSBfdGhpcyRzdGF0ZTYuZm9jdXNlZE9wdGlvbixcbiAgICAgICAgZm9jdXNlZFZhbHVlID0gX3RoaXMkc3RhdGU2LmZvY3VzZWRWYWx1ZSxcbiAgICAgICAgaXNGb2N1c2VkID0gX3RoaXMkc3RhdGU2LmlzRm9jdXNlZCxcbiAgICAgICAgc2VsZWN0VmFsdWUgPSBfdGhpcyRzdGF0ZTYuc2VsZWN0VmFsdWU7XG4gICAgICB2YXIgZm9jdXNhYmxlT3B0aW9ucyA9IHRoaXMuZ2V0Rm9jdXNhYmxlT3B0aW9ucygpO1xuICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KExpdmVSZWdpb24kMSwgX2V4dGVuZHMoe30sIGNvbW1vblByb3BzLCB7XG4gICAgICAgIGlkOiB0aGlzLmdldEVsZW1lbnRJZCgnbGl2ZS1yZWdpb24nKSxcbiAgICAgICAgYXJpYVNlbGVjdGlvbjogYXJpYVNlbGVjdGlvbixcbiAgICAgICAgZm9jdXNlZE9wdGlvbjogZm9jdXNlZE9wdGlvbixcbiAgICAgICAgZm9jdXNlZFZhbHVlOiBmb2N1c2VkVmFsdWUsXG4gICAgICAgIGlzRm9jdXNlZDogaXNGb2N1c2VkLFxuICAgICAgICBzZWxlY3RWYWx1ZTogc2VsZWN0VmFsdWUsXG4gICAgICAgIGZvY3VzYWJsZU9wdGlvbnM6IGZvY3VzYWJsZU9wdGlvbnMsXG4gICAgICAgIGlzQXBwbGVEZXZpY2U6IHRoaXMuaXNBcHBsZURldmljZVxuICAgICAgfSkpO1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJyZW5kZXJcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gcmVuZGVyKCkge1xuICAgICAgdmFyIF90aGlzJGdldENvbXBvbmVudHM4ID0gdGhpcy5nZXRDb21wb25lbnRzKCksXG4gICAgICAgIENvbnRyb2wgPSBfdGhpcyRnZXRDb21wb25lbnRzOC5Db250cm9sLFxuICAgICAgICBJbmRpY2F0b3JzQ29udGFpbmVyID0gX3RoaXMkZ2V0Q29tcG9uZW50czguSW5kaWNhdG9yc0NvbnRhaW5lcixcbiAgICAgICAgU2VsZWN0Q29udGFpbmVyID0gX3RoaXMkZ2V0Q29tcG9uZW50czguU2VsZWN0Q29udGFpbmVyLFxuICAgICAgICBWYWx1ZUNvbnRhaW5lciA9IF90aGlzJGdldENvbXBvbmVudHM4LlZhbHVlQ29udGFpbmVyO1xuICAgICAgdmFyIF90aGlzJHByb3BzMTQgPSB0aGlzLnByb3BzLFxuICAgICAgICBjbGFzc05hbWUgPSBfdGhpcyRwcm9wczE0LmNsYXNzTmFtZSxcbiAgICAgICAgaWQgPSBfdGhpcyRwcm9wczE0LmlkLFxuICAgICAgICBpc0Rpc2FibGVkID0gX3RoaXMkcHJvcHMxNC5pc0Rpc2FibGVkLFxuICAgICAgICBtZW51SXNPcGVuID0gX3RoaXMkcHJvcHMxNC5tZW51SXNPcGVuO1xuICAgICAgdmFyIGlzRm9jdXNlZCA9IHRoaXMuc3RhdGUuaXNGb2N1c2VkO1xuICAgICAgdmFyIGNvbW1vblByb3BzID0gdGhpcy5jb21tb25Qcm9wcyA9IHRoaXMuZ2V0Q29tbW9uUHJvcHMoKTtcbiAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChTZWxlY3RDb250YWluZXIsIF9leHRlbmRzKHt9LCBjb21tb25Qcm9wcywge1xuICAgICAgICBjbGFzc05hbWU6IGNsYXNzTmFtZSxcbiAgICAgICAgaW5uZXJQcm9wczoge1xuICAgICAgICAgIGlkOiBpZCxcbiAgICAgICAgICBvbktleURvd246IHRoaXMub25LZXlEb3duXG4gICAgICAgIH0sXG4gICAgICAgIGlzRGlzYWJsZWQ6IGlzRGlzYWJsZWQsXG4gICAgICAgIGlzRm9jdXNlZDogaXNGb2N1c2VkXG4gICAgICB9KSwgdGhpcy5yZW5kZXJMaXZlUmVnaW9uKCksIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KENvbnRyb2wsIF9leHRlbmRzKHt9LCBjb21tb25Qcm9wcywge1xuICAgICAgICBpbm5lclJlZjogdGhpcy5nZXRDb250cm9sUmVmLFxuICAgICAgICBpbm5lclByb3BzOiB7XG4gICAgICAgICAgb25Nb3VzZURvd246IHRoaXMub25Db250cm9sTW91c2VEb3duLFxuICAgICAgICAgIG9uVG91Y2hFbmQ6IHRoaXMub25Db250cm9sVG91Y2hFbmRcbiAgICAgICAgfSxcbiAgICAgICAgaXNEaXNhYmxlZDogaXNEaXNhYmxlZCxcbiAgICAgICAgaXNGb2N1c2VkOiBpc0ZvY3VzZWQsXG4gICAgICAgIG1lbnVJc09wZW46IG1lbnVJc09wZW5cbiAgICAgIH0pLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChWYWx1ZUNvbnRhaW5lciwgX2V4dGVuZHMoe30sIGNvbW1vblByb3BzLCB7XG4gICAgICAgIGlzRGlzYWJsZWQ6IGlzRGlzYWJsZWRcbiAgICAgIH0pLCB0aGlzLnJlbmRlclBsYWNlaG9sZGVyT3JWYWx1ZSgpLCB0aGlzLnJlbmRlcklucHV0KCkpLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChJbmRpY2F0b3JzQ29udGFpbmVyLCBfZXh0ZW5kcyh7fSwgY29tbW9uUHJvcHMsIHtcbiAgICAgICAgaXNEaXNhYmxlZDogaXNEaXNhYmxlZFxuICAgICAgfSksIHRoaXMucmVuZGVyQ2xlYXJJbmRpY2F0b3IoKSwgdGhpcy5yZW5kZXJMb2FkaW5nSW5kaWNhdG9yKCksIHRoaXMucmVuZGVySW5kaWNhdG9yU2VwYXJhdG9yKCksIHRoaXMucmVuZGVyRHJvcGRvd25JbmRpY2F0b3IoKSkpLCB0aGlzLnJlbmRlck1lbnUoKSwgdGhpcy5yZW5kZXJGb3JtRmllbGQoKSk7XG4gICAgfVxuICB9XSwgW3tcbiAgICBrZXk6IFwiZ2V0RGVyaXZlZFN0YXRlRnJvbVByb3BzXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIGdldERlcml2ZWRTdGF0ZUZyb21Qcm9wcyhwcm9wcywgc3RhdGUpIHtcbiAgICAgIHZhciBwcmV2UHJvcHMgPSBzdGF0ZS5wcmV2UHJvcHMsXG4gICAgICAgIGNsZWFyRm9jdXNWYWx1ZU9uVXBkYXRlID0gc3RhdGUuY2xlYXJGb2N1c1ZhbHVlT25VcGRhdGUsXG4gICAgICAgIGlucHV0SXNIaWRkZW5BZnRlclVwZGF0ZSA9IHN0YXRlLmlucHV0SXNIaWRkZW5BZnRlclVwZGF0ZSxcbiAgICAgICAgYXJpYVNlbGVjdGlvbiA9IHN0YXRlLmFyaWFTZWxlY3Rpb24sXG4gICAgICAgIGlzRm9jdXNlZCA9IHN0YXRlLmlzRm9jdXNlZCxcbiAgICAgICAgcHJldldhc0ZvY3VzZWQgPSBzdGF0ZS5wcmV2V2FzRm9jdXNlZCxcbiAgICAgICAgaW5zdGFuY2VQcmVmaXggPSBzdGF0ZS5pbnN0YW5jZVByZWZpeDtcbiAgICAgIHZhciBvcHRpb25zID0gcHJvcHMub3B0aW9ucyxcbiAgICAgICAgdmFsdWUgPSBwcm9wcy52YWx1ZSxcbiAgICAgICAgbWVudUlzT3BlbiA9IHByb3BzLm1lbnVJc09wZW4sXG4gICAgICAgIGlucHV0VmFsdWUgPSBwcm9wcy5pbnB1dFZhbHVlLFxuICAgICAgICBpc011bHRpID0gcHJvcHMuaXNNdWx0aTtcbiAgICAgIHZhciBzZWxlY3RWYWx1ZSA9IGNsZWFuVmFsdWUodmFsdWUpO1xuICAgICAgdmFyIG5ld01lbnVPcHRpb25zU3RhdGUgPSB7fTtcbiAgICAgIGlmIChwcmV2UHJvcHMgJiYgKHZhbHVlICE9PSBwcmV2UHJvcHMudmFsdWUgfHwgb3B0aW9ucyAhPT0gcHJldlByb3BzLm9wdGlvbnMgfHwgbWVudUlzT3BlbiAhPT0gcHJldlByb3BzLm1lbnVJc09wZW4gfHwgaW5wdXRWYWx1ZSAhPT0gcHJldlByb3BzLmlucHV0VmFsdWUpKSB7XG4gICAgICAgIHZhciBmb2N1c2FibGVPcHRpb25zID0gbWVudUlzT3BlbiA/IGJ1aWxkRm9jdXNhYmxlT3B0aW9ucyhwcm9wcywgc2VsZWN0VmFsdWUpIDogW107XG4gICAgICAgIHZhciBmb2N1c2FibGVPcHRpb25zV2l0aElkcyA9IG1lbnVJc09wZW4gPyBidWlsZEZvY3VzYWJsZU9wdGlvbnNXaXRoSWRzKGJ1aWxkQ2F0ZWdvcml6ZWRPcHRpb25zKHByb3BzLCBzZWxlY3RWYWx1ZSksIFwiXCIuY29uY2F0KGluc3RhbmNlUHJlZml4LCBcIi1vcHRpb25cIikpIDogW107XG4gICAgICAgIHZhciBmb2N1c2VkVmFsdWUgPSBjbGVhckZvY3VzVmFsdWVPblVwZGF0ZSA/IGdldE5leHRGb2N1c2VkVmFsdWUoc3RhdGUsIHNlbGVjdFZhbHVlKSA6IG51bGw7XG4gICAgICAgIHZhciBmb2N1c2VkT3B0aW9uID0gZ2V0TmV4dEZvY3VzZWRPcHRpb24oc3RhdGUsIGZvY3VzYWJsZU9wdGlvbnMpO1xuICAgICAgICB2YXIgZm9jdXNlZE9wdGlvbklkID0gZ2V0Rm9jdXNlZE9wdGlvbklkKGZvY3VzYWJsZU9wdGlvbnNXaXRoSWRzLCBmb2N1c2VkT3B0aW9uKTtcbiAgICAgICAgbmV3TWVudU9wdGlvbnNTdGF0ZSA9IHtcbiAgICAgICAgICBzZWxlY3RWYWx1ZTogc2VsZWN0VmFsdWUsXG4gICAgICAgICAgZm9jdXNlZE9wdGlvbjogZm9jdXNlZE9wdGlvbixcbiAgICAgICAgICBmb2N1c2VkT3B0aW9uSWQ6IGZvY3VzZWRPcHRpb25JZCxcbiAgICAgICAgICBmb2N1c2FibGVPcHRpb25zV2l0aElkczogZm9jdXNhYmxlT3B0aW9uc1dpdGhJZHMsXG4gICAgICAgICAgZm9jdXNlZFZhbHVlOiBmb2N1c2VkVmFsdWUsXG4gICAgICAgICAgY2xlYXJGb2N1c1ZhbHVlT25VcGRhdGU6IGZhbHNlXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICAvLyBzb21lIHVwZGF0ZXMgc2hvdWxkIHRvZ2dsZSB0aGUgc3RhdGUgb2YgdGhlIGlucHV0IHZpc2liaWxpdHlcbiAgICAgIHZhciBuZXdJbnB1dElzSGlkZGVuU3RhdGUgPSBpbnB1dElzSGlkZGVuQWZ0ZXJVcGRhdGUgIT0gbnVsbCAmJiBwcm9wcyAhPT0gcHJldlByb3BzID8ge1xuICAgICAgICBpbnB1dElzSGlkZGVuOiBpbnB1dElzSGlkZGVuQWZ0ZXJVcGRhdGUsXG4gICAgICAgIGlucHV0SXNIaWRkZW5BZnRlclVwZGF0ZTogdW5kZWZpbmVkXG4gICAgICB9IDoge307XG4gICAgICB2YXIgbmV3QXJpYVNlbGVjdGlvbiA9IGFyaWFTZWxlY3Rpb247XG4gICAgICB2YXIgaGFzS2VwdEZvY3VzID0gaXNGb2N1c2VkICYmIHByZXZXYXNGb2N1c2VkO1xuICAgICAgaWYgKGlzRm9jdXNlZCAmJiAhaGFzS2VwdEZvY3VzKSB7XG4gICAgICAgIC8vIElmIGB2YWx1ZWAgb3IgYGRlZmF1bHRWYWx1ZWAgcHJvcHMgYXJlIG5vdCBlbXB0eSB0aGVuIGFubm91bmNlIHRoZW1cbiAgICAgICAgLy8gd2hlbiB0aGUgU2VsZWN0IGlzIGluaXRpYWxseSBmb2N1c2VkXG4gICAgICAgIG5ld0FyaWFTZWxlY3Rpb24gPSB7XG4gICAgICAgICAgdmFsdWU6IHZhbHVlVGVybmFyeShpc011bHRpLCBzZWxlY3RWYWx1ZSwgc2VsZWN0VmFsdWVbMF0gfHwgbnVsbCksXG4gICAgICAgICAgb3B0aW9uczogc2VsZWN0VmFsdWUsXG4gICAgICAgICAgYWN0aW9uOiAnaW5pdGlhbC1pbnB1dC1mb2N1cydcbiAgICAgICAgfTtcbiAgICAgICAgaGFzS2VwdEZvY3VzID0gIXByZXZXYXNGb2N1c2VkO1xuICAgICAgfVxuXG4gICAgICAvLyBJZiB0aGUgJ2luaXRpYWwtaW5wdXQtZm9jdXMnIGFjdGlvbiBoYXMgYmVlbiBzZXQgYWxyZWFkeVxuICAgICAgLy8gdGhlbiByZXNldCB0aGUgYXJpYVNlbGVjdGlvbiB0byBudWxsXG4gICAgICBpZiAoKGFyaWFTZWxlY3Rpb24gPT09IG51bGwgfHwgYXJpYVNlbGVjdGlvbiA9PT0gdm9pZCAwID8gdm9pZCAwIDogYXJpYVNlbGVjdGlvbi5hY3Rpb24pID09PSAnaW5pdGlhbC1pbnB1dC1mb2N1cycpIHtcbiAgICAgICAgbmV3QXJpYVNlbGVjdGlvbiA9IG51bGw7XG4gICAgICB9XG4gICAgICByZXR1cm4gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIG5ld01lbnVPcHRpb25zU3RhdGUpLCBuZXdJbnB1dElzSGlkZGVuU3RhdGUpLCB7fSwge1xuICAgICAgICBwcmV2UHJvcHM6IHByb3BzLFxuICAgICAgICBhcmlhU2VsZWN0aW9uOiBuZXdBcmlhU2VsZWN0aW9uLFxuICAgICAgICBwcmV2V2FzRm9jdXNlZDogaGFzS2VwdEZvY3VzXG4gICAgICB9KTtcbiAgICB9XG4gIH1dKTtcbiAgcmV0dXJuIFNlbGVjdDtcbn0oQ29tcG9uZW50KTtcblNlbGVjdC5kZWZhdWx0UHJvcHMgPSBkZWZhdWx0UHJvcHM7XG5cbmV4cG9ydCB7IFNlbGVjdCBhcyBTLCBkZWZhdWx0UHJvcHMgYXMgYSwgZ2V0T3B0aW9uTGFiZWwkMSBhcyBiLCBjcmVhdGVGaWx0ZXIgYXMgYywgZGVmYXVsdFRoZW1lIGFzIGQsIGdldE9wdGlvblZhbHVlJDEgYXMgZywgbWVyZ2VTdHlsZXMgYXMgbSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-select/dist/Select-aab027f3.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-select/dist/index-641ee5b8.esm.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-select/dist/index-641ee5b8.esm.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ isMobileDevice),\n/* harmony export */   B: () => (/* binding */ multiValueAsValue),\n/* harmony export */   C: () => (/* binding */ singleValueAsValue),\n/* harmony export */   D: () => (/* binding */ valueTernary),\n/* harmony export */   E: () => (/* binding */ classNames),\n/* harmony export */   F: () => (/* binding */ defaultComponents),\n/* harmony export */   G: () => (/* binding */ isDocumentElement),\n/* harmony export */   H: () => (/* binding */ cleanValue),\n/* harmony export */   I: () => (/* binding */ scrollIntoView),\n/* harmony export */   J: () => (/* binding */ noop),\n/* harmony export */   K: () => (/* binding */ notNullish),\n/* harmony export */   L: () => (/* binding */ handleInputChange),\n/* harmony export */   M: () => (/* binding */ MenuPlacer),\n/* harmony export */   a: () => (/* binding */ clearIndicatorCSS),\n/* harmony export */   b: () => (/* binding */ containerCSS),\n/* harmony export */   c: () => (/* binding */ components),\n/* harmony export */   d: () => (/* binding */ css$1),\n/* harmony export */   e: () => (/* binding */ dropdownIndicatorCSS),\n/* harmony export */   f: () => (/* binding */ groupHeadingCSS),\n/* harmony export */   g: () => (/* binding */ groupCSS),\n/* harmony export */   h: () => (/* binding */ indicatorSeparatorCSS),\n/* harmony export */   i: () => (/* binding */ indicatorsContainerCSS),\n/* harmony export */   j: () => (/* binding */ inputCSS),\n/* harmony export */   k: () => (/* binding */ loadingMessageCSS),\n/* harmony export */   l: () => (/* binding */ loadingIndicatorCSS),\n/* harmony export */   m: () => (/* binding */ menuCSS),\n/* harmony export */   n: () => (/* binding */ menuListCSS),\n/* harmony export */   o: () => (/* binding */ menuPortalCSS),\n/* harmony export */   p: () => (/* binding */ multiValueCSS),\n/* harmony export */   q: () => (/* binding */ multiValueLabelCSS),\n/* harmony export */   r: () => (/* binding */ removeProps),\n/* harmony export */   s: () => (/* binding */ supportsPassiveEvents),\n/* harmony export */   t: () => (/* binding */ multiValueRemoveCSS),\n/* harmony export */   u: () => (/* binding */ noOptionsMessageCSS),\n/* harmony export */   v: () => (/* binding */ optionCSS),\n/* harmony export */   w: () => (/* binding */ placeholderCSS),\n/* harmony export */   x: () => (/* binding */ css),\n/* harmony export */   y: () => (/* binding */ valueContainerCSS),\n/* harmony export */   z: () => (/* binding */ isTouchCapable)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @emotion/react */ \"(ssr)/./node_modules/@emotion/react/dist/emotion-react.development.esm.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/taggedTemplateLiteral */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _floating_ui_dom__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @floating-ui/dom */ \"(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! use-isomorphic-layout-effect */ \"(ssr)/./node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar _excluded$4 = [\"className\", \"clearValue\", \"cx\", \"getStyles\", \"getClassNames\", \"getValue\", \"hasValue\", \"isMulti\", \"isRtl\", \"options\", \"selectOption\", \"selectProps\", \"setValue\", \"theme\"];\n// ==============================\n// NO OP\n// ==============================\n\nvar noop = function noop() {};\n\n// ==============================\n// Class Name Prefixer\n// ==============================\n\n/**\n String representation of component state for styling with class names.\n\n Expects an array of strings OR a string/object pair:\n - className(['comp', 'comp-arg', 'comp-arg-2'])\n   @returns 'react-select__comp react-select__comp-arg react-select__comp-arg-2'\n - className('comp', { some: true, state: false })\n   @returns 'react-select__comp react-select__comp--some'\n*/\nfunction applyPrefixToName(prefix, name) {\n  if (!name) {\n    return prefix;\n  } else if (name[0] === '-') {\n    return prefix + name;\n  } else {\n    return prefix + '__' + name;\n  }\n}\nfunction classNames(prefix, state) {\n  for (var _len = arguments.length, classNameList = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    classNameList[_key - 2] = arguments[_key];\n  }\n  var arr = [].concat(classNameList);\n  if (state && prefix) {\n    for (var key in state) {\n      if (state.hasOwnProperty(key) && state[key]) {\n        arr.push(\"\".concat(applyPrefixToName(prefix, key)));\n      }\n    }\n  }\n  return arr.filter(function (i) {\n    return i;\n  }).map(function (i) {\n    return String(i).trim();\n  }).join(' ');\n}\n// ==============================\n// Clean Value\n// ==============================\n\nvar cleanValue = function cleanValue(value) {\n  if (isArray(value)) return value.filter(Boolean);\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(value) === 'object' && value !== null) return [value];\n  return [];\n};\n\n// ==============================\n// Clean Common Props\n// ==============================\n\nvar cleanCommonProps = function cleanCommonProps(props) {\n  //className\n  props.className;\n    props.clearValue;\n    props.cx;\n    props.getStyles;\n    props.getClassNames;\n    props.getValue;\n    props.hasValue;\n    props.isMulti;\n    props.isRtl;\n    props.options;\n    props.selectOption;\n    props.selectProps;\n    props.setValue;\n    props.theme;\n    var innerProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(props, _excluded$4);\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, innerProps);\n};\n\n// ==============================\n// Get Style Props\n// ==============================\n\nvar getStyleProps = function getStyleProps(props, name, classNamesState) {\n  var cx = props.cx,\n    getStyles = props.getStyles,\n    getClassNames = props.getClassNames,\n    className = props.className;\n  return {\n    css: getStyles(name, props),\n    className: cx(classNamesState !== null && classNamesState !== void 0 ? classNamesState : {}, getClassNames(name, props), className)\n  };\n};\n\n// ==============================\n// Handle Input Change\n// ==============================\n\nfunction handleInputChange(inputValue, actionMeta, onInputChange) {\n  if (onInputChange) {\n    var _newValue = onInputChange(inputValue, actionMeta);\n    if (typeof _newValue === 'string') return _newValue;\n  }\n  return inputValue;\n}\n\n// ==============================\n// Scroll Helpers\n// ==============================\n\nfunction isDocumentElement(el) {\n  return [document.documentElement, document.body, window].indexOf(el) > -1;\n}\n\n// Normalized Scroll Top\n// ------------------------------\n\nfunction normalizedHeight(el) {\n  if (isDocumentElement(el)) {\n    return window.innerHeight;\n  }\n  return el.clientHeight;\n}\n\n// Normalized scrollTo & scrollTop\n// ------------------------------\n\nfunction getScrollTop(el) {\n  if (isDocumentElement(el)) {\n    return window.pageYOffset;\n  }\n  return el.scrollTop;\n}\nfunction scrollTo(el, top) {\n  // with a scroll distance, we perform scroll on the element\n  if (isDocumentElement(el)) {\n    window.scrollTo(0, top);\n    return;\n  }\n  el.scrollTop = top;\n}\n\n// Get Scroll Parent\n// ------------------------------\n\nfunction getScrollParent(element) {\n  var style = getComputedStyle(element);\n  var excludeStaticParent = style.position === 'absolute';\n  var overflowRx = /(auto|scroll)/;\n  if (style.position === 'fixed') return document.documentElement;\n  for (var parent = element; parent = parent.parentElement;) {\n    style = getComputedStyle(parent);\n    if (excludeStaticParent && style.position === 'static') {\n      continue;\n    }\n    if (overflowRx.test(style.overflow + style.overflowY + style.overflowX)) {\n      return parent;\n    }\n  }\n  return document.documentElement;\n}\n\n// Animated Scroll To\n// ------------------------------\n\n/**\n  @param t: time (elapsed)\n  @param b: initial value\n  @param c: amount of change\n  @param d: duration\n*/\nfunction easeOutCubic(t, b, c, d) {\n  return c * ((t = t / d - 1) * t * t + 1) + b;\n}\nfunction animatedScrollTo(element, to) {\n  var duration = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 200;\n  var callback = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : noop;\n  var start = getScrollTop(element);\n  var change = to - start;\n  var increment = 10;\n  var currentTime = 0;\n  function animateScroll() {\n    currentTime += increment;\n    var val = easeOutCubic(currentTime, start, change, duration);\n    scrollTo(element, val);\n    if (currentTime < duration) {\n      window.requestAnimationFrame(animateScroll);\n    } else {\n      callback(element);\n    }\n  }\n  animateScroll();\n}\n\n// Scroll Into View\n// ------------------------------\n\nfunction scrollIntoView(menuEl, focusedEl) {\n  var menuRect = menuEl.getBoundingClientRect();\n  var focusedRect = focusedEl.getBoundingClientRect();\n  var overScroll = focusedEl.offsetHeight / 3;\n  if (focusedRect.bottom + overScroll > menuRect.bottom) {\n    scrollTo(menuEl, Math.min(focusedEl.offsetTop + focusedEl.clientHeight - menuEl.offsetHeight + overScroll, menuEl.scrollHeight));\n  } else if (focusedRect.top - overScroll < menuRect.top) {\n    scrollTo(menuEl, Math.max(focusedEl.offsetTop - overScroll, 0));\n  }\n}\n\n// ==============================\n// Get bounding client object\n// ==============================\n\n// cannot get keys using array notation with DOMRect\nfunction getBoundingClientObj(element) {\n  var rect = element.getBoundingClientRect();\n  return {\n    bottom: rect.bottom,\n    height: rect.height,\n    left: rect.left,\n    right: rect.right,\n    top: rect.top,\n    width: rect.width\n  };\n}\n\n// ==============================\n// Touch Capability Detector\n// ==============================\n\nfunction isTouchCapable() {\n  try {\n    document.createEvent('TouchEvent');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n// ==============================\n// Mobile Device Detector\n// ==============================\n\nfunction isMobileDevice() {\n  try {\n    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n  } catch (e) {\n    return false;\n  }\n}\n\n// ==============================\n// Passive Event Detector\n// ==============================\n\n// https://github.com/rafgraph/detect-it/blob/main/src/index.ts#L19-L36\nvar passiveOptionAccessed = false;\nvar options = {\n  get passive() {\n    return passiveOptionAccessed = true;\n  }\n};\n// check for SSR\nvar w = typeof window !== 'undefined' ? window : {};\nif (w.addEventListener && w.removeEventListener) {\n  w.addEventListener('p', noop, options);\n  w.removeEventListener('p', noop, false);\n}\nvar supportsPassiveEvents = passiveOptionAccessed;\nfunction notNullish(item) {\n  return item != null;\n}\nfunction isArray(arg) {\n  return Array.isArray(arg);\n}\nfunction valueTernary(isMulti, multiValue, singleValue) {\n  return isMulti ? multiValue : singleValue;\n}\nfunction singleValueAsValue(singleValue) {\n  return singleValue;\n}\nfunction multiValueAsValue(multiValue) {\n  return multiValue;\n}\nvar removeProps = function removeProps(propsObj) {\n  for (var _len2 = arguments.length, properties = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    properties[_key2 - 1] = arguments[_key2];\n  }\n  var propsMap = Object.entries(propsObj).filter(function (_ref) {\n    var _ref2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_ref, 1),\n      key = _ref2[0];\n    return !properties.includes(key);\n  });\n  return propsMap.reduce(function (newProps, _ref3) {\n    var _ref4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_ref3, 2),\n      key = _ref4[0],\n      val = _ref4[1];\n    newProps[key] = val;\n    return newProps;\n  }, {});\n};\n\nvar _excluded$3 = [\"children\", \"innerProps\"],\n  _excluded2$1 = [\"children\", \"innerProps\"];\nfunction getMenuPlacement(_ref) {\n  var preferredMaxHeight = _ref.maxHeight,\n    menuEl = _ref.menuEl,\n    minHeight = _ref.minHeight,\n    preferredPlacement = _ref.placement,\n    shouldScroll = _ref.shouldScroll,\n    isFixedPosition = _ref.isFixedPosition,\n    controlHeight = _ref.controlHeight;\n  var scrollParent = getScrollParent(menuEl);\n  var defaultState = {\n    placement: 'bottom',\n    maxHeight: preferredMaxHeight\n  };\n\n  // something went wrong, return default state\n  if (!menuEl || !menuEl.offsetParent) return defaultState;\n\n  // we can't trust `scrollParent.scrollHeight` --> it may increase when\n  // the menu is rendered\n  var _scrollParent$getBoun = scrollParent.getBoundingClientRect(),\n    scrollHeight = _scrollParent$getBoun.height;\n  var _menuEl$getBoundingCl = menuEl.getBoundingClientRect(),\n    menuBottom = _menuEl$getBoundingCl.bottom,\n    menuHeight = _menuEl$getBoundingCl.height,\n    menuTop = _menuEl$getBoundingCl.top;\n  var _menuEl$offsetParent$ = menuEl.offsetParent.getBoundingClientRect(),\n    containerTop = _menuEl$offsetParent$.top;\n  var viewHeight = isFixedPosition ? window.innerHeight : normalizedHeight(scrollParent);\n  var scrollTop = getScrollTop(scrollParent);\n  var marginBottom = parseInt(getComputedStyle(menuEl).marginBottom, 10);\n  var marginTop = parseInt(getComputedStyle(menuEl).marginTop, 10);\n  var viewSpaceAbove = containerTop - marginTop;\n  var viewSpaceBelow = viewHeight - menuTop;\n  var scrollSpaceAbove = viewSpaceAbove + scrollTop;\n  var scrollSpaceBelow = scrollHeight - scrollTop - menuTop;\n  var scrollDown = menuBottom - viewHeight + scrollTop + marginBottom;\n  var scrollUp = scrollTop + menuTop - marginTop;\n  var scrollDuration = 160;\n  switch (preferredPlacement) {\n    case 'auto':\n    case 'bottom':\n      // 1: the menu will fit, do nothing\n      if (viewSpaceBelow >= menuHeight) {\n        return {\n          placement: 'bottom',\n          maxHeight: preferredMaxHeight\n        };\n      }\n\n      // 2: the menu will fit, if scrolled\n      if (scrollSpaceBelow >= menuHeight && !isFixedPosition) {\n        if (shouldScroll) {\n          animatedScrollTo(scrollParent, scrollDown, scrollDuration);\n        }\n        return {\n          placement: 'bottom',\n          maxHeight: preferredMaxHeight\n        };\n      }\n\n      // 3: the menu will fit, if constrained\n      if (!isFixedPosition && scrollSpaceBelow >= minHeight || isFixedPosition && viewSpaceBelow >= minHeight) {\n        if (shouldScroll) {\n          animatedScrollTo(scrollParent, scrollDown, scrollDuration);\n        }\n\n        // we want to provide as much of the menu as possible to the user,\n        // so give them whatever is available below rather than the minHeight.\n        var constrainedHeight = isFixedPosition ? viewSpaceBelow - marginBottom : scrollSpaceBelow - marginBottom;\n        return {\n          placement: 'bottom',\n          maxHeight: constrainedHeight\n        };\n      }\n\n      // 4. Forked beviour when there isn't enough space below\n\n      // AUTO: flip the menu, render above\n      if (preferredPlacement === 'auto' || isFixedPosition) {\n        // may need to be constrained after flipping\n        var _constrainedHeight = preferredMaxHeight;\n        var spaceAbove = isFixedPosition ? viewSpaceAbove : scrollSpaceAbove;\n        if (spaceAbove >= minHeight) {\n          _constrainedHeight = Math.min(spaceAbove - marginBottom - controlHeight, preferredMaxHeight);\n        }\n        return {\n          placement: 'top',\n          maxHeight: _constrainedHeight\n        };\n      }\n\n      // BOTTOM: allow browser to increase scrollable area and immediately set scroll\n      if (preferredPlacement === 'bottom') {\n        if (shouldScroll) {\n          scrollTo(scrollParent, scrollDown);\n        }\n        return {\n          placement: 'bottom',\n          maxHeight: preferredMaxHeight\n        };\n      }\n      break;\n    case 'top':\n      // 1: the menu will fit, do nothing\n      if (viewSpaceAbove >= menuHeight) {\n        return {\n          placement: 'top',\n          maxHeight: preferredMaxHeight\n        };\n      }\n\n      // 2: the menu will fit, if scrolled\n      if (scrollSpaceAbove >= menuHeight && !isFixedPosition) {\n        if (shouldScroll) {\n          animatedScrollTo(scrollParent, scrollUp, scrollDuration);\n        }\n        return {\n          placement: 'top',\n          maxHeight: preferredMaxHeight\n        };\n      }\n\n      // 3: the menu will fit, if constrained\n      if (!isFixedPosition && scrollSpaceAbove >= minHeight || isFixedPosition && viewSpaceAbove >= minHeight) {\n        var _constrainedHeight2 = preferredMaxHeight;\n\n        // we want to provide as much of the menu as possible to the user,\n        // so give them whatever is available below rather than the minHeight.\n        if (!isFixedPosition && scrollSpaceAbove >= minHeight || isFixedPosition && viewSpaceAbove >= minHeight) {\n          _constrainedHeight2 = isFixedPosition ? viewSpaceAbove - marginTop : scrollSpaceAbove - marginTop;\n        }\n        if (shouldScroll) {\n          animatedScrollTo(scrollParent, scrollUp, scrollDuration);\n        }\n        return {\n          placement: 'top',\n          maxHeight: _constrainedHeight2\n        };\n      }\n\n      // 4. not enough space, the browser WILL NOT increase scrollable area when\n      // absolutely positioned element rendered above the viewport (only below).\n      // Flip the menu, render below\n      return {\n        placement: 'bottom',\n        maxHeight: preferredMaxHeight\n      };\n    default:\n      throw new Error(\"Invalid placement provided \\\"\".concat(preferredPlacement, \"\\\".\"));\n  }\n  return defaultState;\n}\n\n// Menu Component\n// ------------------------------\n\nfunction alignToControl(placement) {\n  var placementToCSSProp = {\n    bottom: 'top',\n    top: 'bottom'\n  };\n  return placement ? placementToCSSProp[placement] : 'bottom';\n}\nvar coercePlacement = function coercePlacement(p) {\n  return p === 'auto' ? 'bottom' : p;\n};\nvar menuCSS = function menuCSS(_ref2, unstyled) {\n  var _objectSpread2;\n  var placement = _ref2.placement,\n    _ref2$theme = _ref2.theme,\n    borderRadius = _ref2$theme.borderRadius,\n    spacing = _ref2$theme.spacing,\n    colors = _ref2$theme.colors;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((_objectSpread2 = {\n    label: 'menu'\n  }, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_objectSpread2, alignToControl(placement), '100%'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_objectSpread2, \"position\", 'absolute'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_objectSpread2, \"width\", '100%'), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_objectSpread2, \"zIndex\", 1), _objectSpread2), unstyled ? {} : {\n    backgroundColor: colors.neutral0,\n    borderRadius: borderRadius,\n    boxShadow: '0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)',\n    marginBottom: spacing.menuGutter,\n    marginTop: spacing.menuGutter\n  });\n};\nvar PortalPlacementContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_7__.createContext)(null);\n\n// NOTE: internal only\nvar MenuPlacer = function MenuPlacer(props) {\n  var children = props.children,\n    minMenuHeight = props.minMenuHeight,\n    maxMenuHeight = props.maxMenuHeight,\n    menuPlacement = props.menuPlacement,\n    menuPosition = props.menuPosition,\n    menuShouldScrollIntoView = props.menuShouldScrollIntoView,\n    theme = props.theme;\n  var _ref3 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useContext)(PortalPlacementContext) || {},\n    setPortalPlacement = _ref3.setPortalPlacement;\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(maxMenuHeight),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    maxHeight = _useState2[0],\n    setMaxHeight = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    placement = _useState4[0],\n    setPlacement = _useState4[1];\n  var controlHeight = theme.spacing.controlHeight;\n  (0,use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function () {\n    var menuEl = ref.current;\n    if (!menuEl) return;\n\n    // DO NOT scroll if position is fixed\n    var isFixedPosition = menuPosition === 'fixed';\n    var shouldScroll = menuShouldScrollIntoView && !isFixedPosition;\n    var state = getMenuPlacement({\n      maxHeight: maxMenuHeight,\n      menuEl: menuEl,\n      minHeight: minMenuHeight,\n      placement: menuPlacement,\n      shouldScroll: shouldScroll,\n      isFixedPosition: isFixedPosition,\n      controlHeight: controlHeight\n    });\n    setMaxHeight(state.maxHeight);\n    setPlacement(state.placement);\n    setPortalPlacement === null || setPortalPlacement === void 0 ? void 0 : setPortalPlacement(state.placement);\n  }, [maxMenuHeight, menuPlacement, menuPosition, menuShouldScrollIntoView, minMenuHeight, setPortalPlacement, controlHeight]);\n  return children({\n    ref: ref,\n    placerProps: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props), {}, {\n      placement: placement || coercePlacement(menuPlacement),\n      maxHeight: maxHeight\n    })\n  });\n};\nvar Menu = function Menu(props) {\n  var children = props.children,\n    innerRef = props.innerRef,\n    innerProps = props.innerProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'menu', {\n    menu: true\n  }), {\n    ref: innerRef\n  }, innerProps), children);\n};\nvar Menu$1 = Menu;\n\n// ==============================\n// Menu List\n// ==============================\n\nvar menuListCSS = function menuListCSS(_ref4, unstyled) {\n  var maxHeight = _ref4.maxHeight,\n    baseUnit = _ref4.theme.spacing.baseUnit;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    maxHeight: maxHeight,\n    overflowY: 'auto',\n    position: 'relative',\n    // required for offset[Height, Top] > keyboard scroll\n    WebkitOverflowScrolling: 'touch'\n  }, unstyled ? {} : {\n    paddingBottom: baseUnit,\n    paddingTop: baseUnit\n  });\n};\nvar MenuList = function MenuList(props) {\n  var children = props.children,\n    innerProps = props.innerProps,\n    innerRef = props.innerRef,\n    isMulti = props.isMulti;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'menuList', {\n    'menu-list': true,\n    'menu-list--is-multi': isMulti\n  }), {\n    ref: innerRef\n  }, innerProps), children);\n};\n\n// ==============================\n// Menu Notices\n// ==============================\n\nvar noticeCSS = function noticeCSS(_ref5, unstyled) {\n  var _ref5$theme = _ref5.theme,\n    baseUnit = _ref5$theme.spacing.baseUnit,\n    colors = _ref5$theme.colors;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    textAlign: 'center'\n  }, unstyled ? {} : {\n    color: colors.neutral40,\n    padding: \"\".concat(baseUnit * 2, \"px \").concat(baseUnit * 3, \"px\")\n  });\n};\nvar noOptionsMessageCSS = noticeCSS;\nvar loadingMessageCSS = noticeCSS;\nvar NoOptionsMessage = function NoOptionsMessage(_ref6) {\n  var _ref6$children = _ref6.children,\n    children = _ref6$children === void 0 ? 'No options' : _ref6$children,\n    innerProps = _ref6.innerProps,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref6, _excluded$3);\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps), {}, {\n    children: children,\n    innerProps: innerProps\n  }), 'noOptionsMessage', {\n    'menu-notice': true,\n    'menu-notice--no-options': true\n  }), innerProps), children);\n};\nvar LoadingMessage = function LoadingMessage(_ref7) {\n  var _ref7$children = _ref7.children,\n    children = _ref7$children === void 0 ? 'Loading...' : _ref7$children,\n    innerProps = _ref7.innerProps,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref7, _excluded2$1);\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps), {}, {\n    children: children,\n    innerProps: innerProps\n  }), 'loadingMessage', {\n    'menu-notice': true,\n    'menu-notice--loading': true\n  }), innerProps), children);\n};\n\n// ==============================\n// Menu Portal\n// ==============================\n\nvar menuPortalCSS = function menuPortalCSS(_ref8) {\n  var rect = _ref8.rect,\n    offset = _ref8.offset,\n    position = _ref8.position;\n  return {\n    left: rect.left,\n    position: position,\n    top: offset,\n    width: rect.width,\n    zIndex: 1\n  };\n};\nvar MenuPortal = function MenuPortal(props) {\n  var appendTo = props.appendTo,\n    children = props.children,\n    controlElement = props.controlElement,\n    innerProps = props.innerProps,\n    menuPlacement = props.menuPlacement,\n    menuPosition = props.menuPosition;\n  var menuPortalRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n  var cleanupRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)(null);\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(coercePlacement(menuPlacement)),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState5, 2),\n    placement = _useState6[0],\n    setPortalPlacement = _useState6[1];\n  var portalPlacementContext = (0,react__WEBPACK_IMPORTED_MODULE_7__.useMemo)(function () {\n    return {\n      setPortalPlacement: setPortalPlacement\n    };\n  }, []);\n  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),\n    _useState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState7, 2),\n    computedPosition = _useState8[0],\n    setComputedPosition = _useState8[1];\n  var updateComputedPosition = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function () {\n    if (!controlElement) return;\n    var rect = getBoundingClientObj(controlElement);\n    var scrollDistance = menuPosition === 'fixed' ? 0 : window.pageYOffset;\n    var offset = rect[placement] + scrollDistance;\n    if (offset !== (computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.offset) || rect.left !== (computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.rect.left) || rect.width !== (computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.rect.width)) {\n      setComputedPosition({\n        offset: offset,\n        rect: rect\n      });\n    }\n  }, [controlElement, menuPosition, placement, computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.offset, computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.rect.left, computedPosition === null || computedPosition === void 0 ? void 0 : computedPosition.rect.width]);\n  (0,use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function () {\n    updateComputedPosition();\n  }, [updateComputedPosition]);\n  var runAutoUpdate = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function () {\n    if (typeof cleanupRef.current === 'function') {\n      cleanupRef.current();\n      cleanupRef.current = null;\n    }\n    if (controlElement && menuPortalRef.current) {\n      cleanupRef.current = (0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_11__.autoUpdate)(controlElement, menuPortalRef.current, updateComputedPosition, {\n        elementResize: 'ResizeObserver' in window\n      });\n    }\n  }, [controlElement, updateComputedPosition]);\n  (0,use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function () {\n    runAutoUpdate();\n  }, [runAutoUpdate]);\n  var setMenuPortalElement = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (menuPortalElement) {\n    menuPortalRef.current = menuPortalElement;\n    runAutoUpdate();\n  }, [runAutoUpdate]);\n\n  // bail early if required elements aren't present\n  if (!appendTo && menuPosition !== 'fixed' || !computedPosition) return null;\n\n  // same wrapper element whether fixed or portalled\n  var menuWrapper = (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    ref: setMenuPortalElement\n  }, getStyleProps((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props), {}, {\n    offset: computedPosition.offset,\n    position: menuPosition,\n    rect: computedPosition.rect\n  }), 'menuPortal', {\n    'menu-portal': true\n  }), innerProps), children);\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(PortalPlacementContext.Provider, {\n    value: portalPlacementContext\n  }, appendTo ? /*#__PURE__*/(0,react_dom__WEBPACK_IMPORTED_MODULE_8__.createPortal)(menuWrapper, appendTo) : menuWrapper);\n};\n\n// ==============================\n// Root Container\n// ==============================\n\nvar containerCSS = function containerCSS(_ref) {\n  var isDisabled = _ref.isDisabled,\n    isRtl = _ref.isRtl;\n  return {\n    label: 'container',\n    direction: isRtl ? 'rtl' : undefined,\n    pointerEvents: isDisabled ? 'none' : undefined,\n    // cancel mouse events when disabled\n    position: 'relative'\n  };\n};\nvar SelectContainer = function SelectContainer(props) {\n  var children = props.children,\n    innerProps = props.innerProps,\n    isDisabled = props.isDisabled,\n    isRtl = props.isRtl;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'container', {\n    '--is-disabled': isDisabled,\n    '--is-rtl': isRtl\n  }), innerProps), children);\n};\n\n// ==============================\n// Value Container\n// ==============================\n\nvar valueContainerCSS = function valueContainerCSS(_ref2, unstyled) {\n  var spacing = _ref2.theme.spacing,\n    isMulti = _ref2.isMulti,\n    hasValue = _ref2.hasValue,\n    controlShouldRenderValue = _ref2.selectProps.controlShouldRenderValue;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    alignItems: 'center',\n    display: isMulti && hasValue && controlShouldRenderValue ? 'flex' : 'grid',\n    flex: 1,\n    flexWrap: 'wrap',\n    WebkitOverflowScrolling: 'touch',\n    position: 'relative',\n    overflow: 'hidden'\n  }, unstyled ? {} : {\n    padding: \"\".concat(spacing.baseUnit / 2, \"px \").concat(spacing.baseUnit * 2, \"px\")\n  });\n};\nvar ValueContainer = function ValueContainer(props) {\n  var children = props.children,\n    innerProps = props.innerProps,\n    isMulti = props.isMulti,\n    hasValue = props.hasValue;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'valueContainer', {\n    'value-container': true,\n    'value-container--is-multi': isMulti,\n    'value-container--has-value': hasValue\n  }), innerProps), children);\n};\n\n// ==============================\n// Indicator Container\n// ==============================\n\nvar indicatorsContainerCSS = function indicatorsContainerCSS() {\n  return {\n    alignItems: 'center',\n    alignSelf: 'stretch',\n    display: 'flex',\n    flexShrink: 0\n  };\n};\nvar IndicatorsContainer = function IndicatorsContainer(props) {\n  var children = props.children,\n    innerProps = props.innerProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'indicatorsContainer', {\n    indicators: true\n  }), innerProps), children);\n};\n\nvar _templateObject;\nvar _excluded$2 = [\"size\"],\n  _excluded2 = [\"innerProps\", \"isRtl\", \"size\"];\nfunction _EMOTION_STRINGIFIED_CSS_ERROR__() { return \"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).\"; }\n\n// ==============================\n// Dropdown & Clear Icons\n// ==============================\nvar _ref2 =  false ? 0 : {\n  name: \"tj5bde-Svg\",\n  styles: \"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0;label:Svg;\",\n  map: \"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\",\n  toString: _EMOTION_STRINGIFIED_CSS_ERROR__\n};\nvar Svg = function Svg(_ref) {\n  var size = _ref.size,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref, _excluded$2);\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"svg\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    height: size,\n    width: size,\n    viewBox: \"0 0 20 20\",\n    \"aria-hidden\": \"true\",\n    focusable: \"false\",\n    css: _ref2\n  }, props));\n};\nvar CrossIcon = function CrossIcon(props) {\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(Svg, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    size: 20\n  }, props), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"path\", {\n    d: \"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z\"\n  }));\n};\nvar DownChevron = function DownChevron(props) {\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(Svg, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    size: 20\n  }, props), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"path\", {\n    d: \"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z\"\n  }));\n};\n\n// ==============================\n// Dropdown & Clear Buttons\n// ==============================\n\nvar baseCSS = function baseCSS(_ref3, unstyled) {\n  var isFocused = _ref3.isFocused,\n    _ref3$theme = _ref3.theme,\n    baseUnit = _ref3$theme.spacing.baseUnit,\n    colors = _ref3$theme.colors;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    label: 'indicatorContainer',\n    display: 'flex',\n    transition: 'color 150ms'\n  }, unstyled ? {} : {\n    color: isFocused ? colors.neutral60 : colors.neutral20,\n    padding: baseUnit * 2,\n    ':hover': {\n      color: isFocused ? colors.neutral80 : colors.neutral40\n    }\n  });\n};\nvar dropdownIndicatorCSS = baseCSS;\nvar DropdownIndicator = function DropdownIndicator(props) {\n  var children = props.children,\n    innerProps = props.innerProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'dropdownIndicator', {\n    indicator: true,\n    'dropdown-indicator': true\n  }), innerProps), children || (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(DownChevron, null));\n};\nvar clearIndicatorCSS = baseCSS;\nvar ClearIndicator = function ClearIndicator(props) {\n  var children = props.children,\n    innerProps = props.innerProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'clearIndicator', {\n    indicator: true,\n    'clear-indicator': true\n  }), innerProps), children || (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(CrossIcon, null));\n};\n\n// ==============================\n// Separator\n// ==============================\n\nvar indicatorSeparatorCSS = function indicatorSeparatorCSS(_ref4, unstyled) {\n  var isDisabled = _ref4.isDisabled,\n    _ref4$theme = _ref4.theme,\n    baseUnit = _ref4$theme.spacing.baseUnit,\n    colors = _ref4$theme.colors;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    label: 'indicatorSeparator',\n    alignSelf: 'stretch',\n    width: 1\n  }, unstyled ? {} : {\n    backgroundColor: isDisabled ? colors.neutral10 : colors.neutral20,\n    marginBottom: baseUnit * 2,\n    marginTop: baseUnit * 2\n  });\n};\nvar IndicatorSeparator = function IndicatorSeparator(props) {\n  var innerProps = props.innerProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"span\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, innerProps, getStyleProps(props, 'indicatorSeparator', {\n    'indicator-separator': true\n  })));\n};\n\n// ==============================\n// Loading\n// ==============================\n\nvar loadingDotAnimations = (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.keyframes)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_esm_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__[\"default\"])([\"\\n  0%, 80%, 100% { opacity: 0; }\\n  40% { opacity: 1; }\\n\"])));\nvar loadingIndicatorCSS = function loadingIndicatorCSS(_ref5, unstyled) {\n  var isFocused = _ref5.isFocused,\n    size = _ref5.size,\n    _ref5$theme = _ref5.theme,\n    colors = _ref5$theme.colors,\n    baseUnit = _ref5$theme.spacing.baseUnit;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    label: 'loadingIndicator',\n    display: 'flex',\n    transition: 'color 150ms',\n    alignSelf: 'center',\n    fontSize: size,\n    lineHeight: 1,\n    marginRight: size,\n    textAlign: 'center',\n    verticalAlign: 'middle'\n  }, unstyled ? {} : {\n    color: isFocused ? colors.neutral60 : colors.neutral20,\n    padding: baseUnit * 2\n  });\n};\nvar LoadingDot = function LoadingDot(_ref6) {\n  var delay = _ref6.delay,\n    offset = _ref6.offset;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"span\", {\n    css: /*#__PURE__*/(0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.css)({\n      animation: \"\".concat(loadingDotAnimations, \" 1s ease-in-out \").concat(delay, \"ms infinite;\"),\n      backgroundColor: 'currentColor',\n      borderRadius: '1em',\n      display: 'inline-block',\n      marginLeft: offset ? '1em' : undefined,\n      height: '1em',\n      verticalAlign: 'top',\n      width: '1em'\n    },  false ? 0 : \";label:LoadingDot;\",  false ? 0 : \"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImluZGljYXRvcnMudHN4Il0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQW1RSSIsImZpbGUiOiJpbmRpY2F0b3JzLnRzeCIsInNvdXJjZXNDb250ZW50IjpbIi8qKiBAanN4IGpzeCAqL1xuaW1wb3J0IHsgSlNYLCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBqc3gsIGtleWZyYW1lcyB9IGZyb20gJ0BlbW90aW9uL3JlYWN0JztcblxuaW1wb3J0IHtcbiAgQ29tbW9uUHJvcHNBbmRDbGFzc05hbWUsXG4gIENTU09iamVjdFdpdGhMYWJlbCxcbiAgR3JvdXBCYXNlLFxufSBmcm9tICcuLi90eXBlcyc7XG5pbXBvcnQgeyBnZXRTdHlsZVByb3BzIH0gZnJvbSAnLi4vdXRpbHMnO1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIERyb3Bkb3duICYgQ2xlYXIgSWNvbnNcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG5jb25zdCBTdmcgPSAoe1xuICBzaXplLFxuICAuLi5wcm9wc1xufTogSlNYLkludHJpbnNpY0VsZW1lbnRzWydzdmcnXSAmIHsgc2l6ZTogbnVtYmVyIH0pID0+IChcbiAgPHN2Z1xuICAgIGhlaWdodD17c2l6ZX1cbiAgICB3aWR0aD17c2l6ZX1cbiAgICB2aWV3Qm94PVwiMCAwIDIwIDIwXCJcbiAgICBhcmlhLWhpZGRlbj1cInRydWVcIlxuICAgIGZvY3VzYWJsZT1cImZhbHNlXCJcbiAgICBjc3M9e3tcbiAgICAgIGRpc3BsYXk6ICdpbmxpbmUtYmxvY2snLFxuICAgICAgZmlsbDogJ2N1cnJlbnRDb2xvcicsXG4gICAgICBsaW5lSGVpZ2h0OiAxLFxuICAgICAgc3Ryb2tlOiAnY3VycmVudENvbG9yJyxcbiAgICAgIHN0cm9rZVdpZHRoOiAwLFxuICAgIH19XG4gICAgey4uLnByb3BzfVxuICAvPlxuKTtcblxuZXhwb3J0IHR5cGUgQ3Jvc3NJY29uUHJvcHMgPSBKU1guSW50cmluc2ljRWxlbWVudHNbJ3N2ZyddICYgeyBzaXplPzogbnVtYmVyIH07XG5leHBvcnQgY29uc3QgQ3Jvc3NJY29uID0gKHByb3BzOiBDcm9zc0ljb25Qcm9wcykgPT4gKFxuICA8U3ZnIHNpemU9ezIwfSB7Li4ucHJvcHN9PlxuICAgIDxwYXRoIGQ9XCJNMTQuMzQ4IDE0Ljg0OWMtMC40NjkgMC40NjktMS4yMjkgMC40NjktMS42OTcgMGwtMi42NTEtMy4wMzAtMi42NTEgMy4wMjljLTAuNDY5IDAuNDY5LTEuMjI5IDAuNDY5LTEuNjk3IDAtMC40NjktMC40NjktMC40NjktMS4yMjkgMC0xLjY5N2wyLjc1OC0zLjE1LTIuNzU5LTMuMTUyYy0wLjQ2OS0wLjQ2OS0wLjQ2OS0xLjIyOCAwLTEuNjk3czEuMjI4LTAuNDY5IDEuNjk3IDBsMi42NTIgMy4wMzEgMi42NTEtMy4wMzFjMC40NjktMC40NjkgMS4yMjgtMC40NjkgMS42OTcgMHMwLjQ2OSAxLjIyOSAwIDEuNjk3bC0yLjc1OCAzLjE1MiAyLjc1OCAzLjE1YzAuNDY5IDAuNDY5IDAuNDY5IDEuMjI5IDAgMS42OTh6XCIgLz5cbiAgPC9Tdmc+XG4pO1xuZXhwb3J0IHR5cGUgRG93bkNoZXZyb25Qcm9wcyA9IEpTWC5JbnRyaW5zaWNFbGVtZW50c1snc3ZnJ10gJiB7IHNpemU/OiBudW1iZXIgfTtcbmV4cG9ydCBjb25zdCBEb3duQ2hldnJvbiA9IChwcm9wczogRG93bkNoZXZyb25Qcm9wcykgPT4gKFxuICA8U3ZnIHNpemU9ezIwfSB7Li4ucHJvcHN9PlxuICAgIDxwYXRoIGQ9XCJNNC41MTYgNy41NDhjMC40MzYtMC40NDYgMS4wNDMtMC40ODEgMS41NzYgMGwzLjkwOCAzLjc0NyAzLjkwOC0zLjc0N2MwLjUzMy0wLjQ4MSAxLjE0MS0wLjQ0NiAxLjU3NCAwIDAuNDM2IDAuNDQ1IDAuNDA4IDEuMTk3IDAgMS42MTUtMC40MDYgMC40MTgtNC42OTUgNC41MDItNC42OTUgNC41MDItMC4yMTcgMC4yMjMtMC41MDIgMC4zMzUtMC43ODcgMC4zMzVzLTAuNTctMC4xMTItMC43ODktMC4zMzVjMCAwLTQuMjg3LTQuMDg0LTQuNjk1LTQuNTAycy0wLjQzNi0xLjE3IDAtMS42MTV6XCIgLz5cbiAgPC9Tdmc+XG4pO1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIERyb3Bkb3duICYgQ2xlYXIgQnV0dG9uc1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbmV4cG9ydCBpbnRlcmZhY2UgRHJvcGRvd25JbmRpY2F0b3JQcm9wczxcbiAgT3B0aW9uID0gdW5rbm93bixcbiAgSXNNdWx0aSBleHRlbmRzIGJvb2xlYW4gPSBib29sZWFuLFxuICBHcm91cCBleHRlbmRzIEdyb3VwQmFzZTxPcHRpb24+ID0gR3JvdXBCYXNlPE9wdGlvbj5cbj4gZXh0ZW5kcyBDb21tb25Qcm9wc0FuZENsYXNzTmFtZTxPcHRpb24sIElzTXVsdGksIEdyb3VwPiB7XG4gIC8qKiBUaGUgY2hpbGRyZW4gdG8gYmUgcmVuZGVyZWQgaW5zaWRlIHRoZSBpbmRpY2F0b3IuICovXG4gIGNoaWxkcmVuPzogUmVhY3ROb2RlO1xuICAvKiogUHJvcHMgdGhhdCB3aWxsIGJlIHBhc3NlZCBvbiB0byB0aGUgY2hpbGRyZW4uICovXG4gIGlubmVyUHJvcHM6IEpTWC5JbnRyaW5zaWNFbGVtZW50c1snZGl2J107XG4gIC8qKiBUaGUgZm9jdXNlZCBzdGF0ZSBvZiB0aGUgc2VsZWN0LiAqL1xuICBpc0ZvY3VzZWQ6IGJvb2xlYW47XG4gIGlzRGlzYWJsZWQ6IGJvb2xlYW47XG59XG5cbmNvbnN0IGJhc2VDU1MgPSA8XG4gIE9wdGlvbixcbiAgSXNNdWx0aSBleHRlbmRzIGJvb2xlYW4sXG4gIEdyb3VwIGV4dGVuZHMgR3JvdXBCYXNlPE9wdGlvbj5cbj4oXG4gIHtcbiAgICBpc0ZvY3VzZWQsXG4gICAgdGhlbWU6IHtcbiAgICAgIHNwYWNpbmc6IHsgYmFzZVVuaXQgfSxcbiAgICAgIGNvbG9ycyxcbiAgICB9LFxuICB9OlxuICAgIHwgRHJvcGRvd25JbmRpY2F0b3JQcm9wczxPcHRpb24sIElzTXVsdGksIEdyb3VwPlxuICAgIHwgQ2xlYXJJbmRpY2F0b3JQcm9wczxPcHRpb24sIElzTXVsdGksIEdyb3VwPixcbiAgdW5zdHlsZWQ6IGJvb2xlYW5cbik6IENTU09iamVjdFdpdGhMYWJlbCA9PiAoe1xuICBsYWJlbDogJ2luZGljYXRvckNvbnRhaW5lcicsXG4gIGRpc3BsYXk6ICdmbGV4JyxcbiAgdHJhbnNpdGlvbjogJ2NvbG9yIDE1MG1zJyxcbiAgLi4uKHVuc3R5bGVkXG4gICAgPyB7fVxuICAgIDoge1xuICAgICAgICBjb2xvcjogaXNGb2N1c2VkID8gY29sb3JzLm5ldXRyYWw2MCA6IGNvbG9ycy5uZXV0cmFsMjAsXG4gICAgICAgIHBhZGRpbmc6IGJhc2VVbml0ICogMixcbiAgICAgICAgJzpob3Zlcic6IHtcbiAgICAgICAgICBjb2xvcjogaXNGb2N1c2VkID8gY29sb3JzLm5ldXRyYWw4MCA6IGNvbG9ycy5uZXV0cmFsNDAsXG4gICAgICAgIH0sXG4gICAgICB9KSxcbn0pO1xuXG5leHBvcnQgY29uc3QgZHJvcGRvd25JbmRpY2F0b3JDU1MgPSBiYXNlQ1NTO1xuZXhwb3J0IGNvbnN0IERyb3Bkb3duSW5kaWNhdG9yID0gPFxuICBPcHRpb24sXG4gIElzTXVsdGkgZXh0ZW5kcyBib29sZWFuLFxuICBHcm91cCBleHRlbmRzIEdyb3VwQmFzZTxPcHRpb24+XG4+KFxuICBwcm9wczogRHJvcGRvd25JbmRpY2F0b3JQcm9wczxPcHRpb24sIElzTXVsdGksIEdyb3VwPlxuKSA9PiB7XG4gIGNvbnN0IHsgY2hpbGRyZW4sIGlubmVyUHJvcHMgfSA9IHByb3BzO1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIHsuLi5nZXRTdHlsZVByb3BzKHByb3BzLCAnZHJvcGRvd25JbmRpY2F0b3InLCB7XG4gICAgICAgIGluZGljYXRvcjogdHJ1ZSxcbiAgICAgICAgJ2Ryb3Bkb3duLWluZGljYXRvcic6IHRydWUsXG4gICAgICB9KX1cbiAgICAgIHsuLi5pbm5lclByb3BzfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbiB8fCA8RG93bkNoZXZyb24gLz59XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgaW50ZXJmYWNlIENsZWFySW5kaWNhdG9yUHJvcHM8XG4gIE9wdGlvbiA9IHVua25vd24sXG4gIElzTXVsdGkgZXh0ZW5kcyBib29sZWFuID0gYm9vbGVhbixcbiAgR3JvdXAgZXh0ZW5kcyBHcm91cEJhc2U8T3B0aW9uPiA9IEdyb3VwQmFzZTxPcHRpb24+XG4+IGV4dGVuZHMgQ29tbW9uUHJvcHNBbmRDbGFzc05hbWU8T3B0aW9uLCBJc011bHRpLCBHcm91cD4ge1xuICAvKiogVGhlIGNoaWxkcmVuIHRvIGJlIHJlbmRlcmVkIGluc2lkZSB0aGUgaW5kaWNhdG9yLiAqL1xuICBjaGlsZHJlbj86IFJlYWN0Tm9kZTtcbiAgLyoqIFByb3BzIHRoYXQgd2lsbCBiZSBwYXNzZWQgb24gdG8gdGhlIGNoaWxkcmVuLiAqL1xuICBpbm5lclByb3BzOiBKU1guSW50cmluc2ljRWxlbWVudHNbJ2RpdiddO1xuICAvKiogVGhlIGZvY3VzZWQgc3RhdGUgb2YgdGhlIHNlbGVjdC4gKi9cbiAgaXNGb2N1c2VkOiBib29sZWFuO1xufVxuXG5leHBvcnQgY29uc3QgY2xlYXJJbmRpY2F0b3JDU1MgPSBiYXNlQ1NTO1xuZXhwb3J0IGNvbnN0IENsZWFySW5kaWNhdG9yID0gPFxuICBPcHRpb24sXG4gIElzTXVsdGkgZXh0ZW5kcyBib29sZWFuLFxuICBHcm91cCBleHRlbmRzIEdyb3VwQmFzZTxPcHRpb24+XG4+KFxuICBwcm9wczogQ2xlYXJJbmRpY2F0b3JQcm9wczxPcHRpb24sIElzTXVsdGksIEdyb3VwPlxuKSA9PiB7XG4gIGNvbnN0IHsgY2hpbGRyZW4sIGlubmVyUHJvcHMgfSA9IHByb3BzO1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIHsuLi5nZXRTdHlsZVByb3BzKHByb3BzLCAnY2xlYXJJbmRpY2F0b3InLCB7XG4gICAgICAgIGluZGljYXRvcjogdHJ1ZSxcbiAgICAgICAgJ2NsZWFyLWluZGljYXRvcic6IHRydWUsXG4gICAgICB9KX1cbiAgICAgIHsuLi5pbm5lclByb3BzfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbiB8fCA8Q3Jvc3NJY29uIC8+fVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyBTZXBhcmF0b3Jcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG5leHBvcnQgaW50ZXJmYWNlIEluZGljYXRvclNlcGFyYXRvclByb3BzPFxuICBPcHRpb24gPSB1bmtub3duLFxuICBJc011bHRpIGV4dGVuZHMgYm9vbGVhbiA9IGJvb2xlYW4sXG4gIEdyb3VwIGV4dGVuZHMgR3JvdXBCYXNlPE9wdGlvbj4gPSBHcm91cEJhc2U8T3B0aW9uPlxuPiBleHRlbmRzIENvbW1vblByb3BzQW5kQ2xhc3NOYW1lPE9wdGlvbiwgSXNNdWx0aSwgR3JvdXA+IHtcbiAgaXNEaXNhYmxlZDogYm9vbGVhbjtcbiAgaXNGb2N1c2VkOiBib29sZWFuO1xuICBpbm5lclByb3BzPzogSlNYLkludHJpbnNpY0VsZW1lbnRzWydzcGFuJ107XG59XG5cbmV4cG9ydCBjb25zdCBpbmRpY2F0b3JTZXBhcmF0b3JDU1MgPSA8XG4gIE9wdGlvbixcbiAgSXNNdWx0aSBleHRlbmRzIGJvb2xlYW4sXG4gIEdyb3VwIGV4dGVuZHMgR3JvdXBCYXNlPE9wdGlvbj5cbj4oXG4gIHtcbiAgICBpc0Rpc2FibGVkLFxuICAgIHRoZW1lOiB7XG4gICAgICBzcGFjaW5nOiB7IGJhc2VVbml0IH0sXG4gICAgICBjb2xvcnMsXG4gICAgfSxcbiAgfTogSW5kaWNhdG9yU2VwYXJhdG9yUHJvcHM8T3B0aW9uLCBJc011bHRpLCBHcm91cD4sXG4gIHVuc3R5bGVkOiBib29sZWFuXG4pOiBDU1NPYmplY3RXaXRoTGFiZWwgPT4gKHtcbiAgbGFiZWw6ICdpbmRpY2F0b3JTZXBhcmF0b3InLFxuICBhbGlnblNlbGY6ICdzdHJldGNoJyxcbiAgd2lkdGg6IDEsXG4gIC4uLih1bnN0eWxlZFxuICAgID8ge31cbiAgICA6IHtcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBpc0Rpc2FibGVkID8gY29sb3JzLm5ldXRyYWwxMCA6IGNvbG9ycy5uZXV0cmFsMjAsXG4gICAgICAgIG1hcmdpbkJvdHRvbTogYmFzZVVuaXQgKiAyLFxuICAgICAgICBtYXJnaW5Ub3A6IGJhc2VVbml0ICogMixcbiAgICAgIH0pLFxufSk7XG5cbmV4cG9ydCBjb25zdCBJbmRpY2F0b3JTZXBhcmF0b3IgPSA8XG4gIE9wdGlvbixcbiAgSXNNdWx0aSBleHRlbmRzIGJvb2xlYW4sXG4gIEdyb3VwIGV4dGVuZHMgR3JvdXBCYXNlPE9wdGlvbj5cbj4oXG4gIHByb3BzOiBJbmRpY2F0b3JTZXBhcmF0b3JQcm9wczxPcHRpb24sIElzTXVsdGksIEdyb3VwPlxuKSA9PiB7XG4gIGNvbnN0IHsgaW5uZXJQcm9wcyB9ID0gcHJvcHM7XG4gIHJldHVybiAoXG4gICAgPHNwYW5cbiAgICAgIHsuLi5pbm5lclByb3BzfVxuICAgICAgey4uLmdldFN0eWxlUHJvcHMocHJvcHMsICdpbmRpY2F0b3JTZXBhcmF0b3InLCB7XG4gICAgICAgICdpbmRpY2F0b3Itc2VwYXJhdG9yJzogdHJ1ZSxcbiAgICAgIH0pfVxuICAgIC8+XG4gICk7XG59O1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIExvYWRpbmdcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG5jb25zdCBsb2FkaW5nRG90QW5pbWF0aW9ucyA9IGtleWZyYW1lc2BcbiAgMCUsIDgwJSwgMTAwJSB7IG9wYWNpdHk6IDA7IH1cbiAgNDAlIHsgb3BhY2l0eTogMTsgfVxuYDtcblxuZXhwb3J0IGNvbnN0IGxvYWRpbmdJbmRpY2F0b3JDU1MgPSA8XG4gIE9wdGlvbixcbiAgSXNNdWx0aSBleHRlbmRzIGJvb2xlYW4sXG4gIEdyb3VwIGV4dGVuZHMgR3JvdXBCYXNlPE9wdGlvbj5cbj4oXG4gIHtcbiAgICBpc0ZvY3VzZWQsXG4gICAgc2l6ZSxcbiAgICB0aGVtZToge1xuICAgICAgY29sb3JzLFxuICAgICAgc3BhY2luZzogeyBiYXNlVW5pdCB9LFxuICAgIH0sXG4gIH06IExvYWRpbmdJbmRpY2F0b3JQcm9wczxPcHRpb24sIElzTXVsdGksIEdyb3VwPixcbiAgdW5zdHlsZWQ6IGJvb2xlYW5cbik6IENTU09iamVjdFdpdGhMYWJlbCA9PiAoe1xuICBsYWJlbDogJ2xvYWRpbmdJbmRpY2F0b3InLFxuICBkaXNwbGF5OiAnZmxleCcsXG4gIHRyYW5zaXRpb246ICdjb2xvciAxNTBtcycsXG4gIGFsaWduU2VsZjogJ2NlbnRlcicsXG4gIGZvbnRTaXplOiBzaXplLFxuICBsaW5lSGVpZ2h0OiAxLFxuICBtYXJnaW5SaWdodDogc2l6ZSxcbiAgdGV4dEFsaWduOiAnY2VudGVyJyxcbiAgdmVydGljYWxBbGlnbjogJ21pZGRsZScsXG4gIC4uLih1bnN0eWxlZFxuICAgID8ge31cbiAgICA6IHtcbiAgICAgICAgY29sb3I6IGlzRm9jdXNlZCA/IGNvbG9ycy5uZXV0cmFsNjAgOiBjb2xvcnMubmV1dHJhbDIwLFxuICAgICAgICBwYWRkaW5nOiBiYXNlVW5pdCAqIDIsXG4gICAgICB9KSxcbn0pO1xuXG5pbnRlcmZhY2UgTG9hZGluZ0RvdFByb3BzIHtcbiAgZGVsYXk6IG51bWJlcjtcbiAgb2Zmc2V0OiBib29sZWFuO1xufVxuY29uc3QgTG9hZGluZ0RvdCA9ICh7IGRlbGF5LCBvZmZzZXQgfTogTG9hZGluZ0RvdFByb3BzKSA9PiAoXG4gIDxzcGFuXG4gICAgY3NzPXt7XG4gICAgICBhbmltYXRpb246IGAke2xvYWRpbmdEb3RBbmltYXRpb25zfSAxcyBlYXNlLWluLW91dCAke2RlbGF5fW1zIGluZmluaXRlO2AsXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdjdXJyZW50Q29sb3InLFxuICAgICAgYm9yZGVyUmFkaXVzOiAnMWVtJyxcbiAgICAgIGRpc3BsYXk6ICdpbmxpbmUtYmxvY2snLFxuICAgICAgbWFyZ2luTGVmdDogb2Zmc2V0ID8gJzFlbScgOiB1bmRlZmluZWQsXG4gICAgICBoZWlnaHQ6ICcxZW0nLFxuICAgICAgdmVydGljYWxBbGlnbjogJ3RvcCcsXG4gICAgICB3aWR0aDogJzFlbScsXG4gICAgfX1cbiAgLz5cbik7XG5cbmV4cG9ydCBpbnRlcmZhY2UgTG9hZGluZ0luZGljYXRvclByb3BzPFxuICBPcHRpb24gPSB1bmtub3duLFxuICBJc011bHRpIGV4dGVuZHMgYm9vbGVhbiA9IGJvb2xlYW4sXG4gIEdyb3VwIGV4dGVuZHMgR3JvdXBCYXNlPE9wdGlvbj4gPSBHcm91cEJhc2U8T3B0aW9uPlxuPiBleHRlbmRzIENvbW1vblByb3BzQW5kQ2xhc3NOYW1lPE9wdGlvbiwgSXNNdWx0aSwgR3JvdXA+IHtcbiAgLyoqIFByb3BzIHRoYXQgd2lsbCBiZSBwYXNzZWQgb24gdG8gdGhlIGNoaWxkcmVuLiAqL1xuICBpbm5lclByb3BzOiBKU1guSW50cmluc2ljRWxlbWVudHNbJ2RpdiddO1xuICAvKiogVGhlIGZvY3VzZWQgc3RhdGUgb2YgdGhlIHNlbGVjdC4gKi9cbiAgaXNGb2N1c2VkOiBib29sZWFuO1xuICBpc0Rpc2FibGVkOiBib29sZWFuO1xuICAvKiogU2V0IHNpemUgb2YgdGhlIGNvbnRhaW5lci4gKi9cbiAgc2l6ZTogbnVtYmVyO1xufVxuZXhwb3J0IGNvbnN0IExvYWRpbmdJbmRpY2F0b3IgPSA8XG4gIE9wdGlvbixcbiAgSXNNdWx0aSBleHRlbmRzIGJvb2xlYW4sXG4gIEdyb3VwIGV4dGVuZHMgR3JvdXBCYXNlPE9wdGlvbj5cbj4oe1xuICBpbm5lclByb3BzLFxuICBpc1J0bCxcbiAgc2l6ZSA9IDQsXG4gIC4uLnJlc3RQcm9wc1xufTogTG9hZGluZ0luZGljYXRvclByb3BzPE9wdGlvbiwgSXNNdWx0aSwgR3JvdXA+KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgey4uLmdldFN0eWxlUHJvcHMoXG4gICAgICAgIHsgLi4ucmVzdFByb3BzLCBpbm5lclByb3BzLCBpc1J0bCwgc2l6ZSB9LFxuICAgICAgICAnbG9hZGluZ0luZGljYXRvcicsXG4gICAgICAgIHtcbiAgICAgICAgICBpbmRpY2F0b3I6IHRydWUsXG4gICAgICAgICAgJ2xvYWRpbmctaW5kaWNhdG9yJzogdHJ1ZSxcbiAgICAgICAgfVxuICAgICAgKX1cbiAgICAgIHsuLi5pbm5lclByb3BzfVxuICAgID5cbiAgICAgIDxMb2FkaW5nRG90IGRlbGF5PXswfSBvZmZzZXQ9e2lzUnRsfSAvPlxuICAgICAgPExvYWRpbmdEb3QgZGVsYXk9ezE2MH0gb2Zmc2V0IC8+XG4gICAgICA8TG9hZGluZ0RvdCBkZWxheT17MzIwfSBvZmZzZXQ9eyFpc1J0bH0gLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG4iXX0= */\")\n  });\n};\nvar LoadingIndicator = function LoadingIndicator(_ref7) {\n  var innerProps = _ref7.innerProps,\n    isRtl = _ref7.isRtl,\n    _ref7$size = _ref7.size,\n    size = _ref7$size === void 0 ? 4 : _ref7$size,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref7, _excluded2);\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps), {}, {\n    innerProps: innerProps,\n    isRtl: isRtl,\n    size: size\n  }), 'loadingIndicator', {\n    indicator: true,\n    'loading-indicator': true\n  }), innerProps), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(LoadingDot, {\n    delay: 0,\n    offset: isRtl\n  }), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(LoadingDot, {\n    delay: 160,\n    offset: true\n  }), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(LoadingDot, {\n    delay: 320,\n    offset: !isRtl\n  }));\n};\n\nvar css$1 = function css(_ref, unstyled) {\n  var isDisabled = _ref.isDisabled,\n    isFocused = _ref.isFocused,\n    _ref$theme = _ref.theme,\n    colors = _ref$theme.colors,\n    borderRadius = _ref$theme.borderRadius,\n    spacing = _ref$theme.spacing;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    label: 'control',\n    alignItems: 'center',\n    cursor: 'default',\n    display: 'flex',\n    flexWrap: 'wrap',\n    justifyContent: 'space-between',\n    minHeight: spacing.controlHeight,\n    outline: '0 !important',\n    position: 'relative',\n    transition: 'all 100ms'\n  }, unstyled ? {} : {\n    backgroundColor: isDisabled ? colors.neutral5 : colors.neutral0,\n    borderColor: isDisabled ? colors.neutral10 : isFocused ? colors.primary : colors.neutral20,\n    borderRadius: borderRadius,\n    borderStyle: 'solid',\n    borderWidth: 1,\n    boxShadow: isFocused ? \"0 0 0 1px \".concat(colors.primary) : undefined,\n    '&:hover': {\n      borderColor: isFocused ? colors.primary : colors.neutral30\n    }\n  });\n};\nvar Control = function Control(props) {\n  var children = props.children,\n    isDisabled = props.isDisabled,\n    isFocused = props.isFocused,\n    innerRef = props.innerRef,\n    innerProps = props.innerProps,\n    menuIsOpen = props.menuIsOpen;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    ref: innerRef\n  }, getStyleProps(props, 'control', {\n    control: true,\n    'control--is-disabled': isDisabled,\n    'control--is-focused': isFocused,\n    'control--menu-is-open': menuIsOpen\n  }), innerProps, {\n    \"aria-disabled\": isDisabled || undefined\n  }), children);\n};\nvar Control$1 = Control;\n\nvar _excluded$1 = [\"data\"];\nvar groupCSS = function groupCSS(_ref, unstyled) {\n  var spacing = _ref.theme.spacing;\n  return unstyled ? {} : {\n    paddingBottom: spacing.baseUnit * 2,\n    paddingTop: spacing.baseUnit * 2\n  };\n};\nvar Group = function Group(props) {\n  var children = props.children,\n    cx = props.cx,\n    getStyles = props.getStyles,\n    getClassNames = props.getClassNames,\n    Heading = props.Heading,\n    headingProps = props.headingProps,\n    innerProps = props.innerProps,\n    label = props.label,\n    theme = props.theme,\n    selectProps = props.selectProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'group', {\n    group: true\n  }), innerProps), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(Heading, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, headingProps, {\n    selectProps: selectProps,\n    theme: theme,\n    getStyles: getStyles,\n    getClassNames: getClassNames,\n    cx: cx\n  }), label), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", null, children));\n};\nvar groupHeadingCSS = function groupHeadingCSS(_ref2, unstyled) {\n  var _ref2$theme = _ref2.theme,\n    colors = _ref2$theme.colors,\n    spacing = _ref2$theme.spacing;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    label: 'group',\n    cursor: 'default',\n    display: 'block'\n  }, unstyled ? {} : {\n    color: colors.neutral40,\n    fontSize: '75%',\n    fontWeight: 500,\n    marginBottom: '0.25em',\n    paddingLeft: spacing.baseUnit * 3,\n    paddingRight: spacing.baseUnit * 3,\n    textTransform: 'uppercase'\n  });\n};\nvar GroupHeading = function GroupHeading(props) {\n  var _cleanCommonProps = cleanCommonProps(props);\n    _cleanCommonProps.data;\n    var innerProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_cleanCommonProps, _excluded$1);\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'groupHeading', {\n    'group-heading': true\n  }), innerProps));\n};\nvar Group$1 = Group;\n\nvar _excluded = [\"innerRef\", \"isDisabled\", \"isHidden\", \"inputClassName\"];\nvar inputCSS = function inputCSS(_ref, unstyled) {\n  var isDisabled = _ref.isDisabled,\n    value = _ref.value,\n    _ref$theme = _ref.theme,\n    spacing = _ref$theme.spacing,\n    colors = _ref$theme.colors;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    visibility: isDisabled ? 'hidden' : 'visible',\n    // force css to recompute when value change due to @emotion bug.\n    // We can remove it whenever the bug is fixed.\n    transform: value ? 'translateZ(0)' : ''\n  }, containerStyle), unstyled ? {} : {\n    margin: spacing.baseUnit / 2,\n    paddingBottom: spacing.baseUnit / 2,\n    paddingTop: spacing.baseUnit / 2,\n    color: colors.neutral80\n  });\n};\nvar spacingStyle = {\n  gridArea: '1 / 2',\n  font: 'inherit',\n  minWidth: '2px',\n  border: 0,\n  margin: 0,\n  outline: 0,\n  padding: 0\n};\nvar containerStyle = {\n  flex: '1 1 auto',\n  display: 'inline-grid',\n  gridArea: '1 / 1 / 2 / 3',\n  gridTemplateColumns: '0 min-content',\n  '&:after': (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    content: 'attr(data-value) \" \"',\n    visibility: 'hidden',\n    whiteSpace: 'pre'\n  }, spacingStyle)\n};\nvar inputStyle = function inputStyle(isHidden) {\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    label: 'input',\n    color: 'inherit',\n    background: 0,\n    opacity: isHidden ? 0 : 1,\n    width: '100%'\n  }, spacingStyle);\n};\nvar Input = function Input(props) {\n  var cx = props.cx,\n    value = props.value;\n  var _cleanCommonProps = cleanCommonProps(props),\n    innerRef = _cleanCommonProps.innerRef,\n    isDisabled = _cleanCommonProps.isDisabled,\n    isHidden = _cleanCommonProps.isHidden,\n    inputClassName = _cleanCommonProps.inputClassName,\n    innerProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_cleanCommonProps, _excluded);\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'input', {\n    'input-container': true\n  }), {\n    \"data-value\": value || ''\n  }), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"input\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    className: cx({\n      input: true\n    }, inputClassName),\n    ref: innerRef,\n    style: inputStyle(isHidden),\n    disabled: isDisabled\n  }, innerProps)));\n};\nvar Input$1 = Input;\n\nvar multiValueCSS = function multiValueCSS(_ref, unstyled) {\n  var _ref$theme = _ref.theme,\n    spacing = _ref$theme.spacing,\n    borderRadius = _ref$theme.borderRadius,\n    colors = _ref$theme.colors;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    label: 'multiValue',\n    display: 'flex',\n    minWidth: 0\n  }, unstyled ? {} : {\n    backgroundColor: colors.neutral10,\n    borderRadius: borderRadius / 2,\n    margin: spacing.baseUnit / 2\n  });\n};\nvar multiValueLabelCSS = function multiValueLabelCSS(_ref2, unstyled) {\n  var _ref2$theme = _ref2.theme,\n    borderRadius = _ref2$theme.borderRadius,\n    colors = _ref2$theme.colors,\n    cropWithEllipsis = _ref2.cropWithEllipsis;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    overflow: 'hidden',\n    textOverflow: cropWithEllipsis || cropWithEllipsis === undefined ? 'ellipsis' : undefined,\n    whiteSpace: 'nowrap'\n  }, unstyled ? {} : {\n    borderRadius: borderRadius / 2,\n    color: colors.neutral80,\n    fontSize: '85%',\n    padding: 3,\n    paddingLeft: 6\n  });\n};\nvar multiValueRemoveCSS = function multiValueRemoveCSS(_ref3, unstyled) {\n  var _ref3$theme = _ref3.theme,\n    spacing = _ref3$theme.spacing,\n    borderRadius = _ref3$theme.borderRadius,\n    colors = _ref3$theme.colors,\n    isFocused = _ref3.isFocused;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    alignItems: 'center',\n    display: 'flex'\n  }, unstyled ? {} : {\n    borderRadius: borderRadius / 2,\n    backgroundColor: isFocused ? colors.dangerLight : undefined,\n    paddingLeft: spacing.baseUnit,\n    paddingRight: spacing.baseUnit,\n    ':hover': {\n      backgroundColor: colors.dangerLight,\n      color: colors.danger\n    }\n  });\n};\nvar MultiValueGeneric = function MultiValueGeneric(_ref4) {\n  var children = _ref4.children,\n    innerProps = _ref4.innerProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", innerProps, children);\n};\nvar MultiValueContainer = MultiValueGeneric;\nvar MultiValueLabel = MultiValueGeneric;\nfunction MultiValueRemove(_ref5) {\n  var children = _ref5.children,\n    innerProps = _ref5.innerProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    role: \"button\"\n  }, innerProps), children || (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(CrossIcon, {\n    size: 14\n  }));\n}\nvar MultiValue = function MultiValue(props) {\n  var children = props.children,\n    components = props.components,\n    data = props.data,\n    innerProps = props.innerProps,\n    isDisabled = props.isDisabled,\n    removeProps = props.removeProps,\n    selectProps = props.selectProps;\n  var Container = components.Container,\n    Label = components.Label,\n    Remove = components.Remove;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(Container, {\n    data: data,\n    innerProps: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, getStyleProps(props, 'multiValue', {\n      'multi-value': true,\n      'multi-value--is-disabled': isDisabled\n    })), innerProps),\n    selectProps: selectProps\n  }, (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(Label, {\n    data: data,\n    innerProps: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, getStyleProps(props, 'multiValueLabel', {\n      'multi-value__label': true\n    })),\n    selectProps: selectProps\n  }, children), (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(Remove, {\n    data: data,\n    innerProps: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, getStyleProps(props, 'multiValueRemove', {\n      'multi-value__remove': true\n    })), {}, {\n      'aria-label': \"Remove \".concat(children || 'option')\n    }, removeProps),\n    selectProps: selectProps\n  }));\n};\nvar MultiValue$1 = MultiValue;\n\nvar optionCSS = function optionCSS(_ref, unstyled) {\n  var isDisabled = _ref.isDisabled,\n    isFocused = _ref.isFocused,\n    isSelected = _ref.isSelected,\n    _ref$theme = _ref.theme,\n    spacing = _ref$theme.spacing,\n    colors = _ref$theme.colors;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    label: 'option',\n    cursor: 'default',\n    display: 'block',\n    fontSize: 'inherit',\n    width: '100%',\n    userSelect: 'none',\n    WebkitTapHighlightColor: 'rgba(0, 0, 0, 0)'\n  }, unstyled ? {} : {\n    backgroundColor: isSelected ? colors.primary : isFocused ? colors.primary25 : 'transparent',\n    color: isDisabled ? colors.neutral20 : isSelected ? colors.neutral0 : 'inherit',\n    padding: \"\".concat(spacing.baseUnit * 2, \"px \").concat(spacing.baseUnit * 3, \"px\"),\n    // provide some affordance on touch devices\n    ':active': {\n      backgroundColor: !isDisabled ? isSelected ? colors.primary : colors.primary50 : undefined\n    }\n  });\n};\nvar Option = function Option(props) {\n  var children = props.children,\n    isDisabled = props.isDisabled,\n    isFocused = props.isFocused,\n    isSelected = props.isSelected,\n    innerRef = props.innerRef,\n    innerProps = props.innerProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'option', {\n    option: true,\n    'option--is-disabled': isDisabled,\n    'option--is-focused': isFocused,\n    'option--is-selected': isSelected\n  }), {\n    ref: innerRef,\n    \"aria-disabled\": isDisabled\n  }, innerProps), children);\n};\nvar Option$1 = Option;\n\nvar placeholderCSS = function placeholderCSS(_ref, unstyled) {\n  var _ref$theme = _ref.theme,\n    spacing = _ref$theme.spacing,\n    colors = _ref$theme.colors;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    label: 'placeholder',\n    gridArea: '1 / 1 / 2 / 3'\n  }, unstyled ? {} : {\n    color: colors.neutral50,\n    marginLeft: spacing.baseUnit / 2,\n    marginRight: spacing.baseUnit / 2\n  });\n};\nvar Placeholder = function Placeholder(props) {\n  var children = props.children,\n    innerProps = props.innerProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'placeholder', {\n    placeholder: true\n  }), innerProps), children);\n};\nvar Placeholder$1 = Placeholder;\n\nvar css = function css(_ref, unstyled) {\n  var isDisabled = _ref.isDisabled,\n    _ref$theme = _ref.theme,\n    spacing = _ref$theme.spacing,\n    colors = _ref$theme.colors;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    label: 'singleValue',\n    gridArea: '1 / 1 / 2 / 3',\n    maxWidth: '100%',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap'\n  }, unstyled ? {} : {\n    color: isDisabled ? colors.neutral40 : colors.neutral80,\n    marginLeft: spacing.baseUnit / 2,\n    marginRight: spacing.baseUnit / 2\n  });\n};\nvar SingleValue = function SingleValue(props) {\n  var children = props.children,\n    isDisabled = props.isDisabled,\n    innerProps = props.innerProps;\n  return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_10__.jsx)(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, getStyleProps(props, 'singleValue', {\n    'single-value': true,\n    'single-value--is-disabled': isDisabled\n  }), innerProps), children);\n};\nvar SingleValue$1 = SingleValue;\n\nvar components = {\n  ClearIndicator: ClearIndicator,\n  Control: Control$1,\n  DropdownIndicator: DropdownIndicator,\n  DownChevron: DownChevron,\n  CrossIcon: CrossIcon,\n  Group: Group$1,\n  GroupHeading: GroupHeading,\n  IndicatorsContainer: IndicatorsContainer,\n  IndicatorSeparator: IndicatorSeparator,\n  Input: Input$1,\n  LoadingIndicator: LoadingIndicator,\n  Menu: Menu$1,\n  MenuList: MenuList,\n  MenuPortal: MenuPortal,\n  LoadingMessage: LoadingMessage,\n  NoOptionsMessage: NoOptionsMessage,\n  MultiValue: MultiValue$1,\n  MultiValueContainer: MultiValueContainer,\n  MultiValueLabel: MultiValueLabel,\n  MultiValueRemove: MultiValueRemove,\n  Option: Option$1,\n  Placeholder: Placeholder$1,\n  SelectContainer: SelectContainer,\n  SingleValue: SingleValue$1,\n  ValueContainer: ValueContainer\n};\nvar defaultComponents = function defaultComponents(props) {\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, components), props.components);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-select/dist/index-641ee5b8.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-select/dist/react-select.esm.js":
/*!************************************************************!*\
  !*** ./node_modules/react-select/dist/react-select.esm.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NonceProvider: () => (/* binding */ NonceProvider),\n/* harmony export */   components: () => (/* reexport safe */ _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_5__.c),\n/* harmony export */   createFilter: () => (/* reexport safe */ _Select_aab027f3_esm_js__WEBPACK_IMPORTED_MODULE_3__.c),\n/* harmony export */   \"default\": () => (/* binding */ StateManagedSelect$1),\n/* harmony export */   defaultTheme: () => (/* reexport safe */ _Select_aab027f3_esm_js__WEBPACK_IMPORTED_MODULE_3__.d),\n/* harmony export */   mergeStyles: () => (/* reexport safe */ _Select_aab027f3_esm_js__WEBPACK_IMPORTED_MODULE_3__.m),\n/* harmony export */   useStateManager: () => (/* reexport safe */ _useStateManager_7e1e8489_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)\n/* harmony export */ });\n/* harmony import */ var _useStateManager_7e1e8489_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useStateManager-7e1e8489.esm.js */ \"(ssr)/./node_modules/react-select/dist/useStateManager-7e1e8489.esm.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Select_aab027f3_esm_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Select-aab027f3.esm.js */ \"(ssr)/./node_modules/react-select/dist/Select-aab027f3.esm.js\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @emotion/react */ \"(ssr)/./node_modules/@emotion/react/dist/emotion-element-782f682d.development.esm.js\");\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @emotion/cache */ \"(ssr)/./node_modules/@emotion/cache/dist/emotion-cache.development.esm.js\");\n/* harmony import */ var _index_641ee5b8_esm_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./index-641ee5b8.esm.js */ \"(ssr)/./node_modules/react-select/dist/index-641ee5b8.esm.js\");\n/* harmony import */ var _babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectSpread2__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @babel/runtime/helpers/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @babel/runtime/helpers/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_inherits__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _babel_runtime_helpers_createSuper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @babel/runtime/helpers/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_createSuper__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_createSuper__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @babel/runtime/helpers/taggedTemplateLiteral */ \"(ssr)/./node_modules/@babel/runtime/helpers/taggedTemplateLiteral.js\");\n/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! use-isomorphic-layout-effect */ \"(ssr)/./node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar StateManagedSelect = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(function (props, ref) {\n  var baseSelectProps = (0,_useStateManager_7e1e8489_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)(props);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_Select_aab027f3_esm_js__WEBPACK_IMPORTED_MODULE_3__.S, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    ref: ref\n  }, baseSelectProps));\n});\nvar StateManagedSelect$1 = StateManagedSelect;\n\nvar NonceProvider = (function (_ref) {\n  var nonce = _ref.nonce,\n    children = _ref.children,\n    cacheKey = _ref.cacheKey;\n  var emotionCache = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n      key: cacheKey,\n      nonce: nonce\n    });\n  }, [cacheKey, nonce]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_emotion_react__WEBPACK_IMPORTED_MODULE_19__.C, {\n    value: emotionCache\n  }, children);\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-select/dist/react-select.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-select/dist/useStateManager-7e1e8489.esm.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-select/dist/useStateManager-7e1e8489.esm.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   u: () => (/* binding */ useStateManager)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\nvar _excluded = [\"defaultInputValue\", \"defaultMenuIsOpen\", \"defaultValue\", \"inputValue\", \"menuIsOpen\", \"onChange\", \"onInputChange\", \"onMenuClose\", \"onMenuOpen\", \"value\"];\nfunction useStateManager(_ref) {\n  var _ref$defaultInputValu = _ref.defaultInputValue,\n    defaultInputValue = _ref$defaultInputValu === void 0 ? '' : _ref$defaultInputValu,\n    _ref$defaultMenuIsOpe = _ref.defaultMenuIsOpen,\n    defaultMenuIsOpen = _ref$defaultMenuIsOpe === void 0 ? false : _ref$defaultMenuIsOpe,\n    _ref$defaultValue = _ref.defaultValue,\n    defaultValue = _ref$defaultValue === void 0 ? null : _ref$defaultValue,\n    propsInputValue = _ref.inputValue,\n    propsMenuIsOpen = _ref.menuIsOpen,\n    propsOnChange = _ref.onChange,\n    propsOnInputChange = _ref.onInputChange,\n    propsOnMenuClose = _ref.onMenuClose,\n    propsOnMenuOpen = _ref.onMenuOpen,\n    propsValue = _ref.value,\n    restSelectProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_ref, _excluded);\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(propsInputValue !== undefined ? propsInputValue : defaultInputValue),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_useState, 2),\n    stateInputValue = _useState2[0],\n    setStateInputValue = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(propsMenuIsOpen !== undefined ? propsMenuIsOpen : defaultMenuIsOpen),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_useState3, 2),\n    stateMenuIsOpen = _useState4[0],\n    setStateMenuIsOpen = _useState4[1];\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(propsValue !== undefined ? propsValue : defaultValue),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_useState5, 2),\n    stateValue = _useState6[0],\n    setStateValue = _useState6[1];\n  var onChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (value, actionMeta) {\n    if (typeof propsOnChange === 'function') {\n      propsOnChange(value, actionMeta);\n    }\n    setStateValue(value);\n  }, [propsOnChange]);\n  var onInputChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (value, actionMeta) {\n    var newValue;\n    if (typeof propsOnInputChange === 'function') {\n      newValue = propsOnInputChange(value, actionMeta);\n    }\n    setStateInputValue(newValue !== undefined ? newValue : value);\n  }, [propsOnInputChange]);\n  var onMenuOpen = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {\n    if (typeof propsOnMenuOpen === 'function') {\n      propsOnMenuOpen();\n    }\n    setStateMenuIsOpen(true);\n  }, [propsOnMenuOpen]);\n  var onMenuClose = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {\n    if (typeof propsOnMenuClose === 'function') {\n      propsOnMenuClose();\n    }\n    setStateMenuIsOpen(false);\n  }, [propsOnMenuClose]);\n  var inputValue = propsInputValue !== undefined ? propsInputValue : stateInputValue;\n  var menuIsOpen = propsMenuIsOpen !== undefined ? propsMenuIsOpen : stateMenuIsOpen;\n  var value = propsValue !== undefined ? propsValue : stateValue;\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restSelectProps), {}, {\n    inputValue: inputValue,\n    menuIsOpen: menuIsOpen,\n    onChange: onChange,\n    onInputChange: onInputChange,\n    onMenuClose: onMenuClose,\n    onMenuOpen: onMenuOpen,\n    value: value\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-select/dist/useStateManager-7e1e8489.esm.js\n");

/***/ })

};
;